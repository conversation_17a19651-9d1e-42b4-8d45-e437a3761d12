#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDAI.space Cloudflare Turnstile 复选框点击测试脚本
专门针对可见的 Turnstile 复选框进行点击操作
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'botasaurus-project'))

from botasaurus.browser import browser, Driver
import time
import json
from datetime import datetime

@browser(
    headless=False,  # 必须使用有头模式
    max_retry=3,
    reuse_driver=False,
    block_images_and_css=False,
    wait_for_complete_page_load=True,
    window_size=(1200, 800)
)
def test_ddai_click_turnstile_checkbox(driver: Driver, data):
    """
    测试点击 DDAI.space 的 Turnstile 复选框
    """
    print("🎯 开始测试点击 DDAI.space Turnstile 复选框...")
    
    try:
        # 启用人类模式
        print("🤖 启用人类模式...")
        driver.enable_human_mode()
        
        # 访问页面
        print("🌐 访问 DDAI 注册页面...")
        driver.get("https://app.ddai.space/register")
        
        # 等待页面加载
        print("⏳ 等待页面加载...")
        driver.sleep(5)

        print(f"📄 当前 URL: {driver.current_url}")
        print(f"📄 页面标题: {driver.title}")

        # 等待 Turnstile 加载 - 更长时间等待
        print("⏳ 等待 Turnstile 完全加载...")

        # 等待 Turnstile iframe 出现
        turnstile_loaded = False
        for wait_attempt in range(20):  # 等待最多20秒
            try:
                iframes = driver.select_all('iframe')
                turnstile_elements = driver.select_all('.cf-turnstile, [data-sitekey]')

                print(f"   尝试 {wait_attempt + 1}/20: 发现 {len(iframes)} 个 iframe, {len(turnstile_elements)} 个 Turnstile 元素")

                if iframes or turnstile_elements:
                    # 检查是否有 Turnstile 相关的 iframe
                    for iframe in iframes:
                        src = iframe.get_attribute("src") or ""
                        if "turnstile" in src.lower() or "cloudflare" in src.lower():
                            print(f"   ✅ 发现 Turnstile iframe: {src[:100]}...")
                            turnstile_loaded = True
                            break

                    if not turnstile_loaded and turnstile_elements:
                        print("   ✅ 发现 Turnstile 元素")
                        turnstile_loaded = True

                if turnstile_loaded:
                    break

                driver.sleep(1)

            except Exception as e:
                print(f"   ⚠️ 检查 Turnstile 加载状态时出错: {e}")
                driver.sleep(1)

        if not turnstile_loaded:
            print("   ⚠️ 未检测到 Turnstile 加载，继续尝试...")
        else:
            print("   ✅ Turnstile 已加载，等待2秒后开始点击...")
            driver.sleep(2)
        
        # 查找并点击 Turnstile 复选框
        print("🔍 查找 Turnstile 复选框...")
        
        # 尝试多种方式查找复选框
        checkbox_found = False
        click_success = False
        
        # 方法1: 查找 iframe 并切换进去
        print("🔍 方法1: 查找 Turnstile iframe...")
        try:
            iframes = driver.select_all('iframe')
            print(f"   发现 {len(iframes)} 个 iframe")
            
            for i, iframe in enumerate(iframes):
                try:
                    src = iframe.get_attribute("src") or ""
                    print(f"   iframe {i}: {src[:100]}...")
                    
                    if "turnstile" in src.lower() or "cloudflare" in src.lower():
                        print(f"   🎯 找到 Turnstile iframe: {i}")
                        
                        # 切换到 iframe
                        driver.switch_to.frame(iframe)
                        print("   🔄 已切换到 Turnstile iframe")
                        
                        # 在 iframe 内查找复选框
                        checkbox_selectors = [
                            'input[type="checkbox"]',
                            '[role="checkbox"]',
                            '.cb-i',
                            'span[role="checkbox"]',
                            'div[role="checkbox"]'
                        ]
                        
                        for selector in checkbox_selectors:
                            try:
                                checkboxes = driver.select_all(selector)
                                if checkboxes:
                                    print(f"   🎯 在 iframe 中找到复选框: {selector} (数量: {len(checkboxes)})")
                                    checkbox = checkboxes[0]
                                    
                                    # 检查是否可见
                                    if checkbox.is_displayed():
                                        print("   👁️ 复选框可见，尝试点击...")
                                        checkbox.click()
                                        print("   ✅ 成功点击复选框！")
                                        checkbox_found = True
                                        click_success = True
                                        break
                                    else:
                                        print("   👁️ 复选框不可见")
                            except Exception as e:
                                print(f"   ⚠️ 查找 {selector} 时出错: {e}")
                        
                        # 切换回主页面
                        driver.switch_to.default_content()
                        
                        if click_success:
                            break
                            
                except Exception as e:
                    print(f"   ⚠️ 处理 iframe {i} 时出错: {e}")
                    driver.switch_to.default_content()
        
        except Exception as e:
            print(f"⚠️ iframe 方法失败: {e}")
            driver.switch_to.default_content()
        
        # 方法2: 直接在主页面查找可点击元素
        if not click_success:
            print("🔍 方法2: 在主页面查找可点击元素...")
            
            clickable_selectors = [
                # Turnstile 相关
                '.cf-turnstile',
                '[data-sitekey]',
                # 复选框相关
                'input[type="checkbox"]',
                '[role="checkbox"]',
                'span[role="checkbox"]',
                'div[role="checkbox"]',
                # 通用可点击元素
                'div[class*="checkbox"]',
                'div[class*="turnstile"]',
                'div[class*="cloudflare"]'
            ]
            
            for selector in clickable_selectors:
                try:
                    elements = driver.select_all(selector)
                    if elements:
                        print(f"   🎯 找到元素: {selector} (数量: {len(elements)})")
                        
                        for element in elements:
                            try:
                                if element.is_displayed():
                                    print(f"   👁️ 元素可见，尝试点击...")
                                    element.click()
                                    print(f"   ✅ 成功点击 {selector}！")
                                    click_success = True
                                    checkbox_found = True
                                    break
                                else:
                                    print(f"   👁️ 元素不可见: {selector}")
                            except Exception as e:
                                print(f"   ⚠️ 点击元素时出错: {e}")
                        
                        if click_success:
                            break
                            
                except Exception as e:
                    print(f"   ⚠️ 查找 {selector} 时出错: {e}")
        
        # 方法3: 使用 JavaScript 点击
        if not click_success:
            print("🔍 方法3: 使用 JavaScript 查找并点击...")
            
            js_scripts = [
                # 查找并点击 Turnstile 复选框
                """
                var checkboxes = document.querySelectorAll('input[type="checkbox"], [role="checkbox"]');
                for (var i = 0; i < checkboxes.length; i++) {
                    if (checkboxes[i].offsetParent !== null) {
                        checkboxes[i].click();
                        return 'clicked_checkbox_' + i;
                    }
                }
                return 'no_visible_checkbox';
                """,
                
                # 查找 Turnstile iframe 并点击内部元素
                """
                var iframes = document.querySelectorAll('iframe');
                for (var i = 0; i < iframes.length; i++) {
                    var src = iframes[i].src || '';
                    if (src.includes('turnstile') || src.includes('cloudflare')) {
                        try {
                            var iframeDoc = iframes[i].contentDocument || iframes[i].contentWindow.document;
                            var checkbox = iframeDoc.querySelector('input[type="checkbox"], [role="checkbox"]');
                            if (checkbox) {
                                checkbox.click();
                                return 'clicked_iframe_checkbox_' + i;
                            }
                        } catch (e) {
                            // Cross-origin iframe, can't access
                        }
                    }
                }
                return 'no_iframe_checkbox';
                """
            ]
            
            for i, script in enumerate(js_scripts):
                try:
                    result = driver.run_js(script)
                    print(f"   📝 JavaScript 脚本 {i+1} 结果: {result}")
                    
                    if result and 'clicked' in str(result):
                        print("   ✅ JavaScript 点击成功！")
                        click_success = True
                        checkbox_found = True
                        break
                        
                except Exception as e:
                    print(f"   ⚠️ JavaScript 脚本 {i+1} 执行失败: {e}")
        
        # 等待验证处理
        if click_success:
            print("⏳ 等待 Turnstile 验证处理...")
            
            # 监控验证状态
            for wait_time in range(0, 31, 5):  # 等待最多30秒
                if wait_time > 0:
                    print(f"   ⏳ 已等待 {wait_time} 秒...")
                    driver.sleep(5)
                
                # 检查验证状态
                try:
                    response_field = driver.select('input[name="cf-turnstile-response"]')
                    if response_field:
                        response_value = response_field.get_attribute("value") or ""
                        if response_value and len(response_value) > 10:
                            print(f"   ✅ 验证完成！令牌长度: {len(response_value)} 字符")
                            break
                        else:
                            print(f"   ⏳ 验证处理中... (令牌长度: {len(response_value)})")
                    else:
                        print("   ❌ 未找到响应字段")
                except Exception as e:
                    print(f"   ⚠️ 检查验证状态时出错: {e}")
        
        # 最终状态检查
        print("🔍 最终状态检查...")
        
        final_response = ""
        try:
            response_field = driver.select('input[name="cf-turnstile-response"]')
            if response_field:
                final_response = response_field.get_attribute("value") or ""
        except:
            pass
        
        verification_complete = bool(final_response and len(final_response) > 10)
        
        # 禁用人类模式
        driver.disable_human_mode()
        
        # 生成结果
        result = {
            "timestamp": datetime.now().isoformat(),
            "test_url": "https://app.ddai.space/register",
            "current_url": driver.current_url,
            "page_title": driver.title,
            "checkbox_found": checkbox_found,
            "click_success": click_success,
            "verification_complete": verification_complete,
            "final_response_length": len(final_response),
            "final_response_preview": final_response[:50] + "..." if len(final_response) > 50 else final_response,
            "status": "success" if verification_complete else "partial" if click_success else "failed"
        }
        
        # 打印结果
        print("\n" + "="*60)
        print("📊 DDAI Turnstile 复选框点击测试结果")
        print("="*60)
        print(f"🎯 目标网站: https://app.ddai.space/register")
        print(f"📄 页面标题: {driver.title}")
        print(f"🔍 复选框发现: {'是' if checkbox_found else '否'}")
        print(f"🖱️ 点击成功: {'是' if click_success else '否'}")
        print(f"✅ 验证完成: {'是' if verification_complete else '否'}")
        print(f"📝 令牌长度: {len(final_response)} 字符")
        
        if verification_complete:
            print("🎉 恭喜！成功点击复选框并完成验证！")
        elif click_success:
            print("⚠️ 成功点击复选框，但验证可能未完成")
        else:
            print("❌ 未能成功点击复选框")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        
        try:
            driver.disable_human_mode()
        except:
            pass
        
        return {
            "timestamp": datetime.now().isoformat(),
            "test_url": "https://app.ddai.space/register",
            "status": "error",
            "error": str(e)
        }

def run_checkbox_click_test():
    """
    运行复选框点击测试
    """
    print("🚀 启动 DDAI.space Turnstile 复选框点击测试")
    print("="*60)
    print("📝 此测试将尝试点击可见的 Turnstile 复选框")
    print("👁️ 请观察浏览器窗口中的点击过程")
    print("="*60)
    
    # 运行测试
    result = test_ddai_click_turnstile_checkbox()
    
    # 保存结果
    output_file = f"ddai_checkbox_click_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"\n💾 详细结果已保存到: {output_file}")
    except Exception as e:
        print(f"⚠️ 保存结果文件时出错: {e}")
    
    return result

if __name__ == "__main__":
    run_checkbox_click_test()
