#!/usr/bin/env python3
"""
Android应用UI组件
自定义的用户界面组件
"""

from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.uix.progressbar import ProgressBar
from kivy.uix.popup import Popup
from kivy.uix.scrollview import ScrollView
from kivy.uix.textinput import TextInput
from kivy.graphics import Color, RoundedRectangle
from kivy.clock import Clock
import threading
import asyncio

class DeviceCard(BoxLayout):
    """设备卡片组件"""
    
    def __init__(self, device_info, on_analyze=None, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.size_hint_y = None
        self.height = '120dp'
        self.spacing = '5dp'
        self.padding = '10dp'
        
        self.device_info = device_info
        self.on_analyze = on_analyze
        
        # 添加背景色
        with self.canvas.before:
            Color(0.95, 0.95, 0.95, 1)
            self.rect = RoundedRectangle(size=self.size, pos=self.pos, radius=[10])
        
        self.bind(size=self._update_rect, pos=self._update_rect)
        
        # 设备信息头部
        header_layout = BoxLayout(orientation='horizontal', size_hint_y=0.4)
        
        # 设备名称
        name_label = Label(
            text=device_info.get('name', 'Unknown Device'),
            font_size='16sp',
            bold=True,
            color=(0.2, 0.2, 0.2, 1),
            text_size=(None, None),
            halign='left'
        )
        
        # 风险等级标签
        risk_level = device_info.get('risk_level', 'UNKNOWN')
        risk_color = self._get_risk_color(risk_level)
        risk_label = Label(
            text=risk_level,
            font_size='12sp',
            bold=True,
            color=risk_color,
            size_hint_x=0.3
        )
        
        header_layout.add_widget(name_label)
        header_layout.add_widget(risk_label)
        
        # 设备详情
        details_layout = BoxLayout(orientation='horizontal', size_hint_y=0.3)
        
        address_label = Label(
            text=f"地址: {device_info.get('address', 'N/A')}",
            font_size='12sp',
            color=(0.5, 0.5, 0.5, 1),
            text_size=(None, None),
            halign='left'
        )
        
        rssi_label = Label(
            text=f"信号: {device_info.get('rssi', 'N/A')}dBm",
            font_size='12sp',
            color=(0.5, 0.5, 0.5, 1),
            size_hint_x=0.4
        )
        
        details_layout.add_widget(address_label)
        details_layout.add_widget(rssi_label)
        
        # 按钮布局
        button_layout = BoxLayout(orientation='horizontal', size_hint_y=0.3, spacing='10dp')
        
        # 分析按钮
        analyze_btn = Button(
            text='安全分析',
            background_color=(0.2, 0.6, 1, 1),
            size_hint_x=0.5
        )
        analyze_btn.bind(on_press=self._on_analyze_press)
        
        # 攻击按钮
        attack_btn = Button(
            text='漏洞利用',
            background_color=(1, 0.4, 0.2, 1),
            size_hint_x=0.5
        )
        attack_btn.bind(on_press=self._on_attack_press)
        
        button_layout.add_widget(analyze_btn)
        button_layout.add_widget(attack_btn)
        
        # 添加所有组件
        self.add_widget(header_layout)
        self.add_widget(details_layout)
        self.add_widget(button_layout)
    
    def _update_rect(self, instance, value):
        """更新背景矩形"""
        self.rect.pos = instance.pos
        self.rect.size = instance.size
    
    def _get_risk_color(self, risk_level):
        """获取风险等级颜色"""
        colors = {
            'HIGH': (1, 0.2, 0.2, 1),
            'MEDIUM': (1, 0.6, 0.2, 1),
            'LOW': (0.2, 0.8, 0.2, 1),
            'UNKNOWN': (0.5, 0.5, 0.5, 1)
        }
        return colors.get(risk_level, colors['UNKNOWN'])
    
    def _on_analyze_press(self, instance):
        """分析按钮点击"""
        if self.on_analyze:
            self.on_analyze(self.device_info, 'analyze')
    
    def _on_attack_press(self, instance):
        """攻击按钮点击"""
        if self.on_analyze:
            self.on_analyze(self.device_info, 'attack')

class ProgressDialog(Popup):
    """进度对话框"""
    
    def __init__(self, title="处理中...", **kwargs):
        super().__init__(**kwargs)
        self.title = title
        self.size_hint = (0.8, 0.4)
        self.auto_dismiss = False
        
        content = BoxLayout(orientation='vertical', spacing='10dp', padding='20dp')
        
        # 状态标签
        self.status_label = Label(
            text="正在初始化...",
            size_hint_y=None,
            height='40dp'
        )
        
        # 进度条
        self.progress_bar = ProgressBar(
            max=100,
            value=0,
            size_hint_y=None,
            height='20dp'
        )
        
        # 取消按钮
        cancel_btn = Button(
            text='取消',
            size_hint_y=None,
            height='40dp',
            background_color=(0.8, 0.4, 0.4, 1)
        )
        cancel_btn.bind(on_press=self.dismiss)
        
        content.add_widget(self.status_label)
        content.add_widget(self.progress_bar)
        content.add_widget(cancel_btn)
        
        self.content = content
    
    def update_progress(self, value, status=""):
        """更新进度"""
        self.progress_bar.value = value
        if status:
            self.status_label.text = status

class ResultDialog(Popup):
    """结果显示对话框"""
    
    def __init__(self, title, result_data, **kwargs):
        super().__init__(**kwargs)
        self.title = title
        self.size_hint = (0.9, 0.8)
        
        content = BoxLayout(orientation='vertical', spacing='10dp', padding='10dp')
        
        # 结果显示区域
        scroll = ScrollView()
        result_layout = BoxLayout(orientation='vertical', size_hint_y=None, spacing='5dp')
        result_layout.bind(minimum_height=result_layout.setter('height'))
        
        # 添加结果内容
        if isinstance(result_data, dict):
            self._add_dict_content(result_layout, result_data)
        elif isinstance(result_data, list):
            self._add_list_content(result_layout, result_data)
        else:
            result_label = Label(
                text=str(result_data),
                text_size=(None, None),
                halign='left',
                size_hint_y=None
            )
            result_label.bind(texture_size=result_label.setter('size'))
            result_layout.add_widget(result_label)
        
        scroll.add_widget(result_layout)
        
        # 按钮布局
        button_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height='50dp', spacing='10dp')
        
        # 保存按钮
        save_btn = Button(
            text='保存结果',
            background_color=(0.2, 0.8, 0.2, 1)
        )
        save_btn.bind(on_press=self._save_result)
        
        # 关闭按钮
        close_btn = Button(
            text='关闭',
            background_color=(0.6, 0.6, 0.6, 1)
        )
        close_btn.bind(on_press=self.dismiss)
        
        button_layout.add_widget(save_btn)
        button_layout.add_widget(close_btn)
        
        content.add_widget(scroll)
        content.add_widget(button_layout)
        
        self.content = content
        self.result_data = result_data
    
    def _add_dict_content(self, layout, data):
        """添加字典内容"""
        for key, value in data.items():
            # 键标签
            key_label = Label(
                text=f"{key}:",
                font_size='14sp',
                bold=True,
                color=(0.2, 0.2, 0.8, 1),
                text_size=(None, None),
                halign='left',
                size_hint_y=None,
                height='30dp'
            )
            layout.add_widget(key_label)
            
            # 值标签
            if isinstance(value, (dict, list)):
                value_text = str(value)
            else:
                value_text = str(value)
            
            value_label = Label(
                text=value_text,
                text_size=(300, None),
                halign='left',
                size_hint_y=None
            )
            value_label.bind(texture_size=value_label.setter('size'))
            layout.add_widget(value_label)
    
    def _add_list_content(self, layout, data):
        """添加列表内容"""
        for i, item in enumerate(data):
            item_label = Label(
                text=f"{i+1}. {str(item)}",
                text_size=(300, None),
                halign='left',
                size_hint_y=None
            )
            item_label.bind(texture_size=item_label.setter('size'))
            layout.add_widget(item_label)
    
    def _save_result(self, instance):
        """保存结果"""
        # 这里可以实现保存到文件的功能
        from datetime import datetime
        filename = f"ble_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"BLE安全分析结果\n")
                f.write(f"生成时间: {datetime.now().isoformat()}\n")
                f.write("="*50 + "\n\n")
                f.write(str(self.result_data))
            
            # 显示保存成功消息
            success_popup = Popup(
                title='保存成功',
                content=Label(text=f'结果已保存到: {filename}'),
                size_hint=(0.6, 0.3)
            )
            success_popup.open()
            
        except Exception as e:
            # 显示保存失败消息
            error_popup = Popup(
                title='保存失败',
                content=Label(text=f'保存失败: {str(e)}'),
                size_hint=(0.6, 0.3)
            )
            error_popup.open()

class AttackToolCard(BoxLayout):
    """攻击工具卡片"""
    
    def __init__(self, tool_name, tool_desc, tool_icon="🔧", on_click=None, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.size_hint_y = None
        self.height = '100dp'
        self.spacing = '5dp'
        self.padding = '10dp'
        
        # 添加背景
        with self.canvas.before:
            Color(0.9, 0.9, 1, 1)
            self.rect = RoundedRectangle(size=self.size, pos=self.pos, radius=[8])
        
        self.bind(size=self._update_rect, pos=self._update_rect)
        
        # 工具图标和名称
        header_layout = BoxLayout(orientation='horizontal', size_hint_y=0.5)
        
        icon_label = Label(
            text=tool_icon,
            font_size='24sp',
            size_hint_x=0.2
        )
        
        name_label = Label(
            text=tool_name,
            font_size='16sp',
            bold=True,
            color=(0.2, 0.2, 0.2, 1),
            text_size=(None, None),
            halign='left'
        )
        
        header_layout.add_widget(icon_label)
        header_layout.add_widget(name_label)
        
        # 工具描述
        desc_label = Label(
            text=tool_desc,
            font_size='12sp',
            color=(0.5, 0.5, 0.5, 1),
            text_size=(None, None),
            halign='left',
            size_hint_y=0.5
        )
        
        self.add_widget(header_layout)
        self.add_widget(desc_label)
        
        # 添加点击事件
        self.on_click = on_click
        self.bind(on_touch_down=self._on_touch_down)
    
    def _update_rect(self, instance, value):
        """更新背景矩形"""
        self.rect.pos = instance.pos
        self.rect.size = instance.size
    
    def _on_touch_down(self, instance, touch):
        """处理触摸事件"""
        if self.collide_point(*touch.pos):
            if self.on_click:
                self.on_click(self)
            return True
        return super()._on_touch_down(instance, touch)

class SettingsItem(BoxLayout):
    """设置项组件"""
    
    def __init__(self, title, widget, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'horizontal'
        self.size_hint_y = None
        self.height = '50dp'
        self.spacing = '10dp'
        self.padding = '10dp'
        
        # 标题标签
        title_label = Label(
            text=title,
            size_hint_x=0.6,
            text_size=(None, None),
            halign='left'
        )
        
        # 设置控件
        widget.size_hint_x = 0.4
        
        self.add_widget(title_label)
        self.add_widget(widget)
