# 📱 BLE共享电动车安全分析 - APK构建完整指南

## 🎯 概述

由于Android APK构建需要特定的环境和大量依赖，我为您提供多种构建方案：

## 🚀 方案一：使用GitHub Actions自动构建（推荐）

### 1. 创建GitHub仓库
```bash
# 1. 在GitHub上创建新仓库
# 2. 将项目代码上传到仓库
git init
git add .
git commit -m "Initial commit"
git remote add origin https://github.com/yourusername/ble-bicycle-security.git
git push -u origin main
```

### 2. 添加GitHub Actions工作流
创建 `.github/workflows/build-apk.yml`：

```yaml
name: Build Android APK

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y python3-pip build-essential git python3-dev
        pip install buildozer cython
    
    - name: Build APK
      run: |
        cd android_app
        buildozer android debug
    
    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: ble-bicycle-security-apk
        path: android_app/bin/*.apk
```

## 🐳 方案二：使用Docker构建

### 1. 创建Dockerfile
```dockerfile
FROM ubuntu:20.04

ENV DEBIAN_FRONTEND=noninteractive

# 安装基础依赖
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    build-essential \
    git \
    zip \
    unzip \
    openjdk-8-jdk \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置Java环境
ENV JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64

# 安装Android SDK
RUN mkdir -p /opt/android-sdk
WORKDIR /opt/android-sdk
RUN wget https://dl.google.com/android/repository/commandlinetools-linux-8512546_latest.zip
RUN unzip commandlinetools-linux-8512546_latest.zip
RUN mkdir -p cmdline-tools/latest
RUN mv cmdline-tools/* cmdline-tools/latest/ || true

ENV ANDROID_HOME=/opt/android-sdk
ENV PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools

# 安装Python依赖
RUN pip3 install buildozer cython kivy

# 设置工作目录
WORKDIR /app

# 复制应用代码
COPY . .

# 构建APK
RUN cd android_app && buildozer android debug

# 输出APK
CMD ["cp", "android_app/bin/*.apk", "/output/"]
```

### 2. 使用Docker构建
```bash
# 构建Docker镜像
docker build -t ble-apk-builder .

# 运行构建
docker run -v $(pwd)/output:/output ble-apk-builder
```

## 💻 方案三：本地Linux环境构建

### 1. 安装依赖（Ubuntu/Debian）
```bash
# 更新系统
sudo apt-get update

# 安装基础依赖
sudo apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    build-essential \
    git \
    zip \
    unzip \
    openjdk-8-jdk \
    autoconf \
    libtool \
    pkg-config \
    zlib1g-dev \
    libncurses5-dev \
    libncursesw5-dev \
    libtinfo5 \
    cmake \
    libffi-dev \
    libssl-dev

# 安装Python包
pip3 install --user buildozer cython kivy
```

### 2. 安装Android SDK
```bash
# 下载Android SDK
mkdir -p ~/android-sdk
cd ~/android-sdk
wget https://dl.google.com/android/repository/commandlinetools-linux-8512546_latest.zip
unzip commandlinetools-linux-8512546_latest.zip

# 设置环境变量
echo 'export ANDROID_HOME=~/android-sdk' >> ~/.bashrc
echo 'export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin' >> ~/.bashrc
source ~/.bashrc
```

### 3. 构建APK
```bash
cd ble_bicycle_security_tools/android_app
buildozer android debug
```

## 🍎 方案四：macOS本地构建（当前环境）

### 1. 安装Homebrew依赖
```bash
# 安装Homebrew（如果没有）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装依赖
brew install python3 java autoconf automake libtool pkg-config
brew install --cask android-studio
```

### 2. 设置环境变量
```bash
# 添加到 ~/.zshrc 或 ~/.bash_profile
export JAVA_HOME=$(/usr/libexec/java_home -v 1.8)
export ANDROID_HOME=~/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/tools
```

### 3. 安装Python依赖并构建
```bash
pip3 install --user buildozer cython kivy
cd ble_bicycle_security_tools/android_app
buildozer android debug
```

## 📦 预构建APK下载

如果您不想自己构建，我已经为您准备了构建脚本。您可以：

### 1. 使用在线构建服务
- **Replit**: 上传代码到Replit并运行构建
- **CodeSandbox**: 在线IDE中构建
- **Gitpod**: 基于浏览器的开发环境

### 2. 使用云服务器
```bash
# 在云服务器上快速构建
# 1. 租用一台Ubuntu云服务器
# 2. 上传代码
# 3. 运行构建脚本
wget -O build.sh https://raw.githubusercontent.com/your-repo/build-script.sh
chmod +x build.sh
./build.sh
```

## 🔧 构建配置优化

### buildozer.spec 关键配置
```ini
[app]
title = BLE共享电动车安全分析
package.name = ble_bicycle_security
package.domain = com.security.ble

# 减小APK大小
android.add_compile_options = --enable-site-packages

# 优化构建速度
android.gradle_dependencies = 
android.add_aars = 

# 权限配置
android.permissions = BLUETOOTH,BLUETOOTH_ADMIN,ACCESS_COARSE_LOCATION,ACCESS_FINE_LOCATION

[buildozer]
log_level = 2
```

## 📱 APK安装和使用

### 1. 安装APK
```bash
# 通过ADB安装
adb install ble_bicycle_security-1.0-debug.apk

# 或直接在手机上点击APK文件安装
```

### 2. 权限设置
安装后需要手动授予以下权限：
- 蓝牙权限
- 位置权限（Android要求）
- 存储权限

### 3. 使用说明
1. 打开应用
2. 点击"设备扫描"开始扫描
3. 选择发现的设备进行分析
4. 查看安全分析结果

## 🚨 故障排除

### 常见问题解决

1. **构建失败 - Java版本问题**
   ```bash
   # 确保使用Java 8
   java -version
   export JAVA_HOME=/path/to/java8
   ```

2. **Android SDK问题**
   ```bash
   # 重新下载SDK
   buildozer android clean
   ```

3. **权限问题**
   ```bash
   # 修复权限
   chmod +x ~/.local/bin/buildozer
   ```

4. **内存不足**
   ```bash
   # 增加虚拟内存
   sudo fallocate -l 4G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

## 📊 构建时间预估

- **GitHub Actions**: 15-30分钟
- **Docker构建**: 20-40分钟  
- **本地Linux**: 10-25分钟
- **本地macOS**: 15-35分钟

## 🎉 构建成功后

构建成功后，您将得到：
- `ble_bicycle_security-1.0-debug.apk` - 调试版本
- `ble_bicycle_security-1.0-release-unsigned.apk` - 发布版本（需签名）

APK大小约为：15-25MB

## 📞 获取帮助

如果构建过程中遇到问题：
1. 检查buildozer日志：`buildozer android debug -v`
2. 查看GitHub Issues
3. 联系技术支持

---

**注意：构建Android APK需要耐心，首次构建可能需要下载大量依赖。建议使用稳定的网络环境。**
