# BLE共享电动车安全分析 - Android应用

## 📱 应用概述

这是一个专为Android设备开发的BLE（蓝牙低功耗）共享电动车安全分析应用，基于Python + Kivy框架构建，提供移动端的BLE设备扫描、安全分析和漏洞利用功能。

## ⚠️ 重要声明

**本应用仅用于教育目的和授权安全测试！**
- 🔒 仅在您拥有或明确授权的设备上使用
- 📚 用于学习BLE安全知识和技术
- 🛡️ 遵守当地法律法规
- ❌ 禁止用于非法活动

## 🎯 应用功能

### 1. 📡 设备扫描
- **智能识别**: 自动识别潜在的共享电动车设备
- **实时扫描**: 持续扫描附近的BLE设备
- **设备信息**: 显示设备名称、MAC地址、信号强度
- **风险评估**: 实时评估设备安全风险等级

### 2. 🔍 安全分析
- **服务枚举**: 分析设备的GATT服务和特征值
- **漏洞检测**: 识别常见的BLE安全漏洞
- **风险评分**: 基于发现的漏洞计算风险分数
- **详细报告**: 生成完整的安全分析报告

### 3. 🛠️ 工具箱
- **Nordic UART攻击**: 针对Nordic芯片的UART服务攻击
- **自定义服务攻击**: 利用厂商自定义协议漏洞
- **弱认证测试**: 测试常见的弱密码和认证缺陷
- **批量测试**: 对多个设备进行批量安全测试

### 4. ⚙️ 设置选项
- **扫描参数**: 自定义扫描时长和间隔
- **攻击配置**: 配置攻击参数和超时时间
- **结果保存**: 自动保存分析结果到本地

## 📋 系统要求

### Android设备要求：
- **Android版本**: 5.0 (API 21) 或更高
- **蓝牙支持**: 支持BLE (蓝牙4.0+)
- **内存**: 至少2GB RAM
- **存储**: 至少100MB可用空间

### 权限要求：
- **蓝牙权限**: 扫描和连接BLE设备
- **位置权限**: Android系统要求（用于BLE扫描）
- **存储权限**: 保存分析结果和日志

## 🚀 安装指南

### 方法1: 直接安装APK
1. **下载APK**: 从发布页面下载最新的APK文件
2. **启用未知来源**: 
   - 进入 设置 > 安全 > 未知来源
   - 或 设置 > 应用和通知 > 特殊应用访问 > 安装未知应用
3. **安装应用**: 点击APK文件进行安装
4. **授予权限**: 首次启动时授予必要权限

### 方法2: 从源码构建
```bash
# 1. 克隆项目
git clone <repository-url>
cd ble_bicycle_security_tools/android_app

# 2. 安装依赖
pip install buildozer kivy kivymd

# 3. 构建APK
./build_android.sh

# 4. 安装生成的APK
adb install bin/ble_bicycle_security-1.0-debug.apk
```

## 📖 使用指南

### 1. 首次启动
1. **权限授予**: 应用会请求蓝牙和位置权限，请全部允许
2. **蓝牙检查**: 确保设备蓝牙已开启
3. **位置服务**: 确保位置服务已启用（Android要求）

### 2. 设备扫描
1. **开始扫描**: 点击"设备扫描"标签页中的"开始扫描"按钮
2. **查看结果**: 扫描到的设备会实时显示在列表中
3. **风险标识**: 每个设备都会显示风险等级（高/中/低）
4. **停止扫描**: 点击"停止扫描"结束扫描过程

### 3. 安全分析
1. **选择设备**: 在扫描结果中点击"安全分析"按钮
2. **等待分析**: 应用会自动分析设备的安全性
3. **查看结果**: 分析完成后显示详细的安全报告
4. **保存结果**: 可以将分析结果保存到本地文件

### 4. 漏洞利用
1. **选择工具**: 在"工具箱"标签页选择攻击工具
2. **选择目标**: 选择要测试的目标设备
3. **执行攻击**: 点击执行按钮开始攻击测试
4. **查看结果**: 攻击完成后查看成功/失败结果

## 🔧 界面说明

### 主界面标签页：

#### 📡 设备扫描
- **扫描控制**: 开始/停止扫描按钮
- **进度显示**: 扫描进度条和状态信息
- **设备列表**: 显示发现的设备及其信息
- **快速操作**: 每个设备的分析和攻击按钮

#### 🔍 安全分析
- **分析结果**: 显示详细的安全分析报告
- **漏洞列表**: 列出发现的所有安全漏洞
- **风险评分**: 显示设备的整体风险分数
- **建议措施**: 提供安全改进建议

#### 🛠️ 工具箱
- **攻击工具**: 各种BLE攻击工具的图标和说明
- **快速访问**: 点击工具图标快速启动攻击
- **批量操作**: 支持对多个设备进行批量测试

#### ⚙️ 设置
- **扫描配置**: 设置扫描时长和参数
- **攻击配置**: 配置攻击超时和重试次数
- **应用信息**: 显示版本信息和法律声明

## 🎯 实际使用场景

### 场景1: 安全研究
```
1. 在授权环境中扫描测试设备
2. 分析设备的BLE安全实现
3. 识别潜在的安全漏洞
4. 生成安全评估报告
```

### 场景2: 渗透测试
```
1. 对客户的IoT设备进行安全测试
2. 验证BLE实现的安全性
3. 测试认证和加密机制
4. 提供安全改进建议
```

### 场景3: 教育学习
```
1. 学习BLE协议和安全机制
2. 了解常见的BLE安全漏洞
3. 实践BLE安全测试技术
4. 提高移动安全意识
```

## 🔍 技术原理

### BLE扫描机制
- 使用Android BLE API进行设备发现
- 分析广播数据包识别设备类型
- 基于服务UUID判断潜在风险

### 安全分析算法
- GATT服务枚举和分析
- 特征值权限检查
- 已知漏洞模式匹配
- 风险评分计算

### 攻击技术实现
- Nordic UART服务利用
- 自定义协议逆向分析
- 认证绕过技术
- 命令注入测试

## 📊 预期效果

### 成功扫描示例：
```
发现设备: SmartBike-001
地址: AA:BB:CC:DD:EE:FF
风险等级: 高
服务: Nordic UART, 自定义服务FFF0
```

### 安全分析结果：
```
风险分数: 85/100
发现漏洞:
• Nordic UART服务缺乏认证
• 自定义服务未加密
• 可能存在解锁漏洞

可利用攻击:
• Nordic UART绕过攻击
• 自定义服务攻击
```

## ⚠️ 注意事项

### 法律合规：
- 仅在授权设备上使用
- 遵守当地法律法规
- 不得用于非法目的
- 承担使用责任

### 技术限制：
- 需要目标设备在蓝牙范围内
- 某些攻击可能需要物理接近
- 成功率取决于目标设备的安全实现
- 部分功能可能需要root权限

### 隐私保护：
- 不收集用户个人信息
- 分析结果仅保存在本地
- 不上传任何数据到服务器
- 用户完全控制数据

## 🛠️ 故障排除

### 常见问题：

1. **扫描不到设备**
   - 检查蓝牙是否开启
   - 确认位置权限已授予
   - 确保目标设备在范围内

2. **连接失败**
   - 检查设备是否支持连接
   - 尝试重启蓝牙
   - 清除应用缓存

3. **攻击失败**
   - 确认目标设备存在漏洞
   - 检查网络连接
   - 尝试不同的攻击参数

## 📞 技术支持

如有问题或建议，请：
1. 查看应用内帮助文档
2. 检查GitHub项目的Issues页面
3. 联系开发团队

---

**记住：技术的力量应该用于保护，而不是破坏。负责任地使用这个工具！**
