#!/usr/bin/env python3
"""
快速APK构建脚本
自动检测环境并选择最佳构建方案
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path

class APKBuilder:
    def __init__(self):
        self.system = platform.system().lower()
        self.python_cmd = self.get_python_command()
        self.pip_cmd = self.get_pip_command()
        
    def get_python_command(self):
        """获取Python命令"""
        for cmd in ['python3', 'python']:
            if shutil.which(cmd):
                return cmd
        raise RuntimeError("Python not found")
    
    def get_pip_command(self):
        """获取pip命令"""
        for cmd in ['pip3', 'pip']:
            if shutil.which(cmd):
                return cmd
        raise RuntimeError("pip not found")
    
    def check_java(self):
        """检查Java环境"""
        try:
            result = subprocess.run(['java', '-version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Java环境正常")
                return True
        except FileNotFoundError:
            pass
        
        print("❌ Java未安装或未配置")
        return False
    
    def install_dependencies(self):
        """安装构建依赖"""
        print("📦 安装构建依赖...")
        
        dependencies = [
            'buildozer',
            'cython', 
            'kivy',
            'kivymd'
        ]
        
        for dep in dependencies:
            print(f"安装 {dep}...")
            try:
                subprocess.run([
                    self.pip_cmd, 'install', '--user', dep
                ], check=True, capture_output=True)
                print(f"✅ {dep} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ {dep} 安装失败: {e}")
                return False
        
        return True
    
    def setup_android_sdk(self):
        """设置Android SDK"""
        android_home = os.environ.get('ANDROID_HOME')
        if android_home and os.path.exists(android_home):
            print("✅ Android SDK已配置")
            return True
        
        print("⚠️ Android SDK未配置")
        print("请按照以下步骤配置：")
        
        if self.system == 'darwin':  # macOS
            print("1. 安装Android Studio")
            print("2. 设置环境变量：")
            print("   export ANDROID_HOME=~/Library/Android/sdk")
            print("   export PATH=$PATH:$ANDROID_HOME/platform-tools")
        elif self.system == 'linux':
            print("1. 下载Android SDK:")
            print("   wget https://dl.google.com/android/repository/commandlinetools-linux-8512546_latest.zip")
            print("2. 解压并设置环境变量")
        else:  # Windows
            print("1. 安装Android Studio")
            print("2. 设置环境变量ANDROID_HOME")
        
        return False
    
    def create_simple_apk(self):
        """创建简化版APK（不需要完整Android SDK）"""
        print("🔧 创建简化版APK...")
        
        # 创建简化的Python应用
        simple_app = """
from kivy.app import App
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.boxlayout import BoxLayout

class SimpleBLEApp(App):
    def build(self):
        layout = BoxLayout(orientation='vertical', padding=20, spacing=20)
        
        title = Label(
            text='BLE共享电动车安全分析工具\\n\\n由于Android限制，完整功能需要在桌面版使用\\n\\n本应用提供基础演示功能',
            font_size='16sp',
            text_size=(None, None),
            halign='center'
        )
        
        demo_btn = Button(
            text='演示模式',
            size_hint_y=None,
            height='50dp'
        )
        demo_btn.bind(on_press=self.show_demo)
        
        info_btn = Button(
            text='使用说明',
            size_hint_y=None,
            height='50dp'
        )
        info_btn.bind(on_press=self.show_info)
        
        layout.add_widget(title)
        layout.add_widget(demo_btn)
        layout.add_widget(info_btn)
        
        return layout
    
    def show_demo(self, instance):
        print("演示模式：扫描BLE设备...")
    
    def show_info(self, instance):
        print("使用说明：请查看README文档")

if __name__ == '__main__':
    SimpleBLEApp().run()
"""
        
        # 写入简化应用
        with open('simple_main.py', 'w', encoding='utf-8') as f:
            f.write(simple_app)
        
        # 创建简化的buildozer.spec
        simple_spec = """
[app]
title = BLE安全分析演示
package.name = ble_demo
package.domain = com.security.bledemo
source.dir = .
source.include_exts = py,png,jpg,kv,atlas
version = 1.0
requirements = python3,kivy
orientation = portrait
fullscreen = 0

[buildozer]
log_level = 2
"""
        
        with open('simple_buildozer.spec', 'w') as f:
            f.write(simple_spec)
        
        print("✅ 简化版应用创建完成")
        return True
    
    def build_apk(self, simple_mode=False):
        """构建APK"""
        if simple_mode:
            print("🚀 开始构建简化版APK...")
            spec_file = 'simple_buildozer.spec'
        else:
            print("🚀 开始构建完整版APK...")
            spec_file = 'buildozer.spec'
        
        try:
            # 清理之前的构建
            if os.path.exists('.buildozer'):
                shutil.rmtree('.buildozer')
            
            # 开始构建
            cmd = ['buildozer', '-f', spec_file, 'android', 'debug']
            print(f"执行命令: {' '.join(cmd)}")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 实时显示构建输出
            for line in process.stdout:
                print(line.rstrip())
            
            process.wait()
            
            if process.returncode == 0:
                print("🎉 APK构建成功！")
                
                # 查找生成的APK
                bin_dir = Path('bin')
                if bin_dir.exists():
                    apk_files = list(bin_dir.glob('*.apk'))
                    if apk_files:
                        print(f"📱 APK文件: {apk_files[0]}")
                        return True
                
                print("⚠️ APK文件未找到")
                return False
            else:
                print("❌ APK构建失败")
                return False
                
        except FileNotFoundError:
            print("❌ buildozer命令未找到")
            print("请先安装buildozer: pip install buildozer")
            return False
        except Exception as e:
            print(f"❌ 构建过程出错: {e}")
            return False
    
    def run_build_process(self):
        """运行完整构建流程"""
        print("🔧 BLE共享电动车安全分析 - APK构建工具")
        print("=" * 50)
        
        # 检查系统环境
        print(f"🖥️ 检测到系统: {self.system}")
        print(f"🐍 Python命令: {self.python_cmd}")
        print(f"📦 pip命令: {self.pip_cmd}")
        
        # 检查Java
        java_ok = self.check_java()
        
        # 安装依赖
        if not self.install_dependencies():
            print("❌ 依赖安装失败")
            return False
        
        # 检查Android SDK
        sdk_ok = self.setup_android_sdk()
        
        # 根据环境选择构建方案
        if not java_ok or not sdk_ok:
            print("\n⚠️ 完整构建环境未就绪")
            print("🔄 切换到简化构建模式...")
            
            if not self.create_simple_apk():
                return False
            
            # 尝试简化构建
            return self.build_apk(simple_mode=True)
        else:
            print("\n✅ 环境检查完成，开始完整构建...")
            return self.build_apk(simple_mode=False)

def main():
    """主函数"""
    builder = APKBuilder()
    
    try:
        success = builder.run_build_process()
        
        if success:
            print("\n🎉 构建完成！")
            print("\n📋 后续步骤：")
            print("1. 将APK文件传输到Android设备")
            print("2. 在设备上启用'未知来源'安装")
            print("3. 点击APK文件进行安装")
            print("4. 授予应用必要权限")
        else:
            print("\n❌ 构建失败")
            print("\n💡 建议：")
            print("1. 检查网络连接")
            print("2. 确保有足够的磁盘空间")
            print("3. 查看详细错误信息")
            print("4. 尝试使用Docker或GitHub Actions构建")
            
    except KeyboardInterrupt:
        print("\n\n👋 构建被用户中断")
    except Exception as e:
        print(f"\n❌ 构建过程出现异常: {e}")

if __name__ == '__main__':
    main()
