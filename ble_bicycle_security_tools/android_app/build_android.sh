#!/bin/bash

# Android APK构建脚本
# 用于将Python应用打包为Android APK

echo "🔧 BLE共享电动车安全分析 - Android APK构建"
echo "=============================================="

# 检查系统要求
echo "📋 检查系统要求..."

# 检查Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3未安装"
    exit 1
fi

# 检查Java
if ! command -v java &> /dev/null; then
    echo "❌ Java未安装，请安装OpenJDK 8或11"
    exit 1
fi

# 检查Android SDK
if [ -z "$ANDROID_HOME" ]; then
    echo "⚠️  ANDROID_HOME环境变量未设置"
    echo "请设置Android SDK路径，例如："
    echo "export ANDROID_HOME=/path/to/android-sdk"
fi

echo "✅ 系统检查完成"

# 安装依赖
echo "📦 安装构建依赖..."

# 安装buildozer
pip3 install --user buildozer

# 安装Cython (加速构建)
pip3 install --user cython

# 安装其他依赖
pip3 install --user kivy kivymd

echo "✅ 依赖安装完成"

# 初始化buildozer
echo "🔧 初始化buildozer配置..."

if [ ! -f "buildozer.spec" ]; then
    buildozer init
    echo "⚠️  请编辑buildozer.spec文件配置应用信息"
else
    echo "✅ buildozer.spec已存在"
fi

# 清理之前的构建
echo "🧹 清理之前的构建文件..."
buildozer android clean

# 开始构建APK
echo "🚀 开始构建Android APK..."
echo "⏰ 这可能需要20-60分钟，请耐心等待..."

# 构建debug版本
buildozer android debug

if [ $? -eq 0 ]; then
    echo "🎉 APK构建成功!"
    echo "📱 APK文件位置: bin/ble_bicycle_security-1.0-debug.apk"
    echo ""
    echo "📋 安装说明:"
    echo "1. 将APK文件传输到Android设备"
    echo "2. 在设备上启用'未知来源'安装"
    echo "3. 点击APK文件进行安装"
    echo ""
    echo "⚠️  权限说明:"
    echo "- 蓝牙权限: 用于扫描和连接BLE设备"
    echo "- 位置权限: Android要求BLE扫描需要位置权限"
    echo "- 存储权限: 用于保存分析结果"
else
    echo "❌ APK构建失败"
    echo "请检查错误信息并解决问题后重试"
    exit 1
fi

# 构建release版本 (可选)
read -p "是否构建release版本? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🔐 构建release版本..."
    buildozer android release
    
    if [ $? -eq 0 ]; then
        echo "🎉 Release APK构建成功!"
        echo "📱 Release APK: bin/ble_bicycle_security-1.0-release-unsigned.apk"
        echo "⚠️  注意: Release版本需要签名才能安装"
    fi
fi

echo ""
echo "🎯 构建完成!"
echo "📖 更多信息请查看 README_ANDROID.md"
