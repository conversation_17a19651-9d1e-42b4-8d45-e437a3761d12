<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BLE共享电动车安全分析 - Web版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 20px;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .tab {
            flex: 1;
            padding: 12px 8px;
            text-align: center;
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .tab.active {
            background: #3498db;
            color: white;
        }
        
        .tab-content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            flex: 1;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .tab-panel {
            display: none;
        }
        
        .tab-panel.active {
            display: block;
        }
        
        .scan-button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 20px;
            transition: all 0.3s;
        }
        
        .scan-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(46, 204, 113, 0.4);
        }
        
        .scan-button.scanning {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            width: 0%;
            transition: width 0.3s;
        }
        
        .device-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .device-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #3498db;
        }
        
        .device-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .device-info {
            font-size: 12px;
            color: #7f8c8d;
            margin-bottom: 10px;
        }
        
        .risk-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .risk-high {
            background: #e74c3c;
            color: white;
        }
        
        .risk-medium {
            background: #f39c12;
            color: white;
        }
        
        .risk-low {
            background: #2ecc71;
            color: white;
        }
        
        .device-actions {
            margin-top: 10px;
        }
        
        .action-btn {
            padding: 8px 12px;
            margin-right: 8px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .analyze-btn {
            background: #3498db;
            color: white;
        }
        
        .exploit-btn {
            background: #e74c3c;
            color: white;
        }
        
        .tool-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .tool-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        
        .tool-card:hover {
            transform: translateY(-3px);
            border-color: #3498db;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.2);
        }
        
        .tool-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .tool-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .tool-desc {
            font-size: 11px;
            color: #7f8c8d;
        }
        
        .status-message {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            font-style: italic;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #856404;
            font-size: 14px;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 15px;
            padding: 20px;
            max-width: 350px;
            width: 90%;
        }
        
        .modal-header {
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .modal-close {
            float: right;
            background: #95a5a6;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            cursor: pointer;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 BLE安全分析工具</h1>
            <p>共享电动车安全检测 - Web演示版</p>
        </div>
        
        <div class="warning">
            ⚠️ <strong>重要提醒：</strong>本工具仅用于教育目的和授权安全测试。请遵守当地法律法规。
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('scan')">📡 扫描</button>
            <button class="tab" onclick="showTab('analysis')">🔍 分析</button>
            <button class="tab" onclick="showTab('tools')">🛠️ 工具</button>
            <button class="tab" onclick="showTab('settings')">⚙️ 设置</button>
        </div>
        
        <div class="tab-content">
            <!-- 扫描标签页 -->
            <div id="scan" class="tab-panel active">
                <button id="scanBtn" class="scan-button" onclick="toggleScan()">
                    开始扫描 BLE 设备
                </button>
                
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill"></div>
                </div>
                
                <div id="statusMessage" class="status-message">
                    准备扫描附近的BLE设备...
                </div>
                
                <div id="deviceList" class="device-list"></div>
            </div>
            
            <!-- 分析标签页 -->
            <div id="analysis" class="tab-panel">
                <div id="analysisContent" class="status-message">
                    选择一个设备进行安全分析...
                </div>
            </div>
            
            <!-- 工具标签页 -->
            <div id="tools" class="tab-panel">
                <div class="tool-grid">
                    <div class="tool-card" onclick="showTool('nordic')">
                        <div class="tool-icon">🔧</div>
                        <div class="tool-name">Nordic UART</div>
                        <div class="tool-desc">攻击Nordic服务</div>
                    </div>
                    
                    <div class="tool-card" onclick="showTool('custom')">
                        <div class="tool-icon">⚙️</div>
                        <div class="tool-name">自定义服务</div>
                        <div class="tool-desc">利用自定义协议</div>
                    </div>
                    
                    <div class="tool-card" onclick="showTool('auth')">
                        <div class="tool-icon">🔐</div>
                        <div class="tool-name">弱认证测试</div>
                        <div class="tool-desc">测试认证机制</div>
                    </div>
                    
                    <div class="tool-card" onclick="showTool('batch')">
                        <div class="tool-icon">📊</div>
                        <div class="tool-name">批量测试</div>
                        <div class="tool-desc">批量安全检测</div>
                    </div>
                </div>
            </div>
            
            <!-- 设置标签页 -->
            <div id="settings" class="tab-panel">
                <div class="status-message">
                    <h3>应用信息</h3>
                    <p>BLE共享电动车安全分析工具 v1.0</p>
                    <p>Web演示版本</p>
                    <br>
                    <p><strong>注意：</strong></p>
                    <p>• 本Web版本仅提供界面演示</p>
                    <p>• 完整功能需要原生Android应用</p>
                    <p>• 实际BLE操作需要设备权限</p>
                    <br>
                    <p>📱 获取完整Android APK请查看构建指南</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div id="modalHeader" class="modal-header">标题</div>
            <div id="modalBody">内容</div>
            <button class="modal-close" onclick="closeModal()">关闭</button>
        </div>
    </div>
    
    <script>
        let isScanning = false;
        let scanProgress = 0;
        let discoveredDevices = [];
        
        // 模拟设备数据
        const mockDevices = [
            {
                name: "SmartBike-001",
                address: "AA:BB:CC:DD:EE:01",
                rssi: -45,
                risk: "high",
                services: ["Nordic UART", "Custom FFF0"]
            },
            {
                name: "MoBike-Lock-123",
                address: "AA:BB:CC:DD:EE:02", 
                rssi: -52,
                risk: "medium",
                services: ["Device Info", "Battery"]
            },
            {
                name: "HelloBike-456",
                address: "AA:BB:CC:DD:EE:03",
                rssi: -38,
                risk: "high",
                services: ["Custom FFE0"]
            }
        ];
        
        function showTab(tabName) {
            // 隐藏所有标签页
            document.querySelectorAll('.tab-panel').forEach(panel => {
                panel.classList.remove('active');
            });
            
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }
        
        function toggleScan() {
            if (!isScanning) {
                startScan();
            } else {
                stopScan();
            }
        }
        
        function startScan() {
            isScanning = true;
            scanProgress = 0;
            discoveredDevices = [];
            
            const scanBtn = document.getElementById('scanBtn');
            const statusMessage = document.getElementById('statusMessage');
            const deviceList = document.getElementById('deviceList');
            
            scanBtn.textContent = '停止扫描';
            scanBtn.classList.add('scanning');
            statusMessage.textContent = '正在扫描BLE设备...';
            deviceList.innerHTML = '';
            
            // 模拟扫描进度
            const scanInterval = setInterval(() => {
                scanProgress += 5;
                document.getElementById('progressFill').style.width = scanProgress + '%';
                
                // 模拟发现设备
                if (scanProgress === 30 || scanProgress === 60 || scanProgress === 90) {
                    const deviceIndex = Math.floor(scanProgress / 30) - 1;
                    if (deviceIndex < mockDevices.length) {
                        addDevice(mockDevices[deviceIndex]);
                    }
                }
                
                if (scanProgress >= 100) {
                    clearInterval(scanInterval);
                    stopScan();
                }
            }, 200);
        }
        
        function stopScan() {
            isScanning = false;
            
            const scanBtn = document.getElementById('scanBtn');
            const statusMessage = document.getElementById('statusMessage');
            
            scanBtn.textContent = '开始扫描 BLE 设备';
            scanBtn.classList.remove('scanning');
            statusMessage.textContent = `扫描完成，发现 ${discoveredDevices.length} 个设备`;
            
            document.getElementById('progressFill').style.width = '0%';
        }
        
        function addDevice(device) {
            discoveredDevices.push(device);
            
            const deviceList = document.getElementById('deviceList');
            const deviceElement = document.createElement('div');
            deviceElement.className = 'device-item';
            
            deviceElement.innerHTML = `
                <div class="device-name">${device.name}</div>
                <div class="device-info">
                    地址: ${device.address} | 信号: ${device.rssi}dBm
                </div>
                <span class="risk-badge risk-${device.risk}">
                    ${device.risk.toUpperCase()} RISK
                </span>
                <div class="device-actions">
                    <button class="action-btn analyze-btn" onclick="analyzeDevice('${device.address}')">
                        安全分析
                    </button>
                    <button class="action-btn exploit-btn" onclick="exploitDevice('${device.address}')">
                        漏洞利用
                    </button>
                </div>
            `;
            
            deviceList.appendChild(deviceElement);
        }
        
        function analyzeDevice(address) {
            const device = discoveredDevices.find(d => d.address === address);
            if (!device) return;
            
            showTab('analysis');
            document.querySelector('.tab[onclick="showTab(\'analysis\')"]').classList.add('active');
            
            const analysisContent = document.getElementById('analysisContent');
            analysisContent.innerHTML = `
                <h3>🔍 安全分析结果</h3>
                <br>
                <strong>设备:</strong> ${device.name}<br>
                <strong>地址:</strong> ${device.address}<br>
                <strong>风险等级:</strong> <span class="risk-badge risk-${device.risk}">${device.risk.toUpperCase()}</span>
                <br><br>
                <strong>🚨 发现的漏洞:</strong><br>
                • Nordic UART服务缺乏认证<br>
                • 自定义服务未加密<br>
                • 可能存在解锁漏洞<br>
                <br>
                <strong>⚡ 可利用攻击:</strong><br>
                • Nordic UART绕过攻击<br>
                • 自定义服务攻击<br>
                <br>
                <strong>🛡️ 安全建议:</strong><br>
                • 实施强认证机制<br>
                • 加密敏感通信<br>
                • 定期安全审计<br>
            `;
        }
        
        function exploitDevice(address) {
            const device = discoveredDevices.find(d => d.address === address);
            if (!device) return;
            
            showModal('漏洞利用测试', `
                <p><strong>目标设备:</strong> ${device.name}</p>
                <p><strong>地址:</strong> ${device.address}</p>
                <br>
                <p>🔧 <strong>Nordic UART攻击:</strong></p>
                <p>✅ 攻击成功!</p>
                <p>使用命令: 0x57008200...</p>
                <br>
                <p>⚠️ <strong>注意:</strong> 这是演示结果</p>
                <p>实际攻击需要原生应用和设备权限</p>
            `);
        }
        
        function showTool(toolName) {
            const toolNames = {
                'nordic': 'Nordic UART攻击',
                'custom': '自定义服务攻击', 
                'auth': '弱认证测试',
                'batch': '批量测试'
            };
            
            const toolDescs = {
                'nordic': '正在尝试Nordic UART服务攻击...\n这是最常见的共享单车漏洞类型。',
                'custom': '正在分析自定义GATT服务...\n寻找未加密的控制特征值。',
                'auth': '正在测试弱认证机制...\n尝试常见密码和认证绕过。',
                'batch': '正在对所有发现的设备进行批量测试...\n这可能需要几分钟时间。'
            };
            
            showModal(toolNames[toolName], toolDescs[toolName]);
        }
        
        function showModal(title, content) {
            document.getElementById('modalHeader').textContent = title;
            document.getElementById('modalBody').innerHTML = content;
            document.getElementById('modal').style.display = 'block';
        }
        
        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('modal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
