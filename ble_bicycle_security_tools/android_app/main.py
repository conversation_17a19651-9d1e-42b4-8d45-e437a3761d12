#!/usr/bin/env python3
"""
BLE共享电动车安全分析 - Android应用主程序
基于Kivy框架的移动端BLE安全分析工具

WARNING: 仅用于教育和授权安全测试！
"""

import os
import sys
import asyncio
import threading
from datetime import datetime
from typing import List, Dict, Optional

# Kivy imports
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.scrollview import ScrollView
from kivy.uix.popup import Popup
from kivy.uix.progressbar import ProgressBar
from kivy.uix.tabbedpanel import TabbedPanel, TabbedPanelItem
from kivy.clock import Clock
from kivy.logger import Logger

# 尝试导入BLE相关模块
try:
    from bleak import BleakScanner, BleakClient
    from bleak.backends.device import BLEDevice
    from bleak.backends.scanner import AdvertisementData
    BLE_AVAILABLE = True
except ImportError:
    Logger.warning("BLE: Bleak not available, using mock mode")
    BLE_AVAILABLE = False

# 导入我们的核心模块（简化版）
from ble_core_mobile import MobileBLEAnalyzer, MobileExploiter

class ScanResultItem(BoxLayout):
    """扫描结果项目组件"""
    def __init__(self, device_info, callback=None, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'horizontal'
        self.size_hint_y = None
        self.height = '60dp'
        self.spacing = '10dp'
        self.padding = '10dp'
        
        self.device_info = device_info
        self.callback = callback
        
        # 设备信息布局
        info_layout = BoxLayout(orientation='vertical', size_hint_x=0.7)
        
        # 设备名称
        name_label = Label(
            text=device_info.get('name', 'Unknown Device'),
            font_size='16sp',
            bold=True,
            text_size=(None, None),
            halign='left'
        )
        
        # 设备地址和RSSI
        details_label = Label(
            text=f"{device_info.get('address', 'N/A')} | RSSI: {device_info.get('rssi', 'N/A')}dBm",
            font_size='12sp',
            color=(0.7, 0.7, 0.7, 1),
            text_size=(None, None),
            halign='left'
        )
        
        info_layout.add_widget(name_label)
        info_layout.add_widget(details_label)
        
        # 分析按钮
        analyze_btn = Button(
            text='分析',
            size_hint_x=0.3,
            background_color=(0.2, 0.6, 1, 1)
        )
        analyze_btn.bind(on_press=self.on_analyze)
        
        self.add_widget(info_layout)
        self.add_widget(analyze_btn)
    
    def on_analyze(self, instance):
        """分析按钮点击事件"""
        if self.callback:
            self.callback(self.device_info)

class BLESecurityApp(App):
    """BLE安全分析应用主类"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "BLE共享电动车安全分析"
        self.analyzer = MobileBLEAnalyzer()
        self.exploiter = MobileExploiter()
        self.scanning = False
        self.discovered_devices = []
    
    def build(self):
        """构建应用界面"""
        # 创建标签页面板
        tab_panel = TabbedPanel(do_default_tab=False)
        
        # 扫描标签页
        scan_tab = TabbedPanelItem(text='设备扫描')
        scan_tab.content = self.create_scan_interface()
        tab_panel.add_widget(scan_tab)
        
        # 分析标签页
        analysis_tab = TabbedPanelItem(text='安全分析')
        analysis_tab.content = self.create_analysis_interface()
        tab_panel.add_widget(analysis_tab)
        
        # 工具标签页
        tools_tab = TabbedPanelItem(text='工具箱')
        tools_tab.content = self.create_tools_interface()
        tab_panel.add_widget(tools_tab)
        
        # 设置标签页
        settings_tab = TabbedPanelItem(text='设置')
        settings_tab.content = self.create_settings_interface()
        tab_panel.add_widget(settings_tab)
        
        return tab_panel
    
    def create_scan_interface(self):
        """创建扫描界面"""
        layout = BoxLayout(orientation='vertical', padding='10dp', spacing='10dp')
        
        # 标题
        title = Label(
            text='BLE设备扫描',
            font_size='20sp',
            bold=True,
            size_hint_y=None,
            height='40dp'
        )
        layout.add_widget(title)
        
        # 控制按钮布局
        control_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height='50dp', spacing='10dp')
        
        # 扫描按钮
        self.scan_btn = Button(
            text='开始扫描',
            background_color=(0.2, 0.8, 0.2, 1)
        )
        self.scan_btn.bind(on_press=self.toggle_scan)
        
        # 清除按钮
        clear_btn = Button(
            text='清除结果',
            background_color=(0.8, 0.4, 0.2, 1)
        )
        clear_btn.bind(on_press=self.clear_results)
        
        control_layout.add_widget(self.scan_btn)
        control_layout.add_widget(clear_btn)
        layout.add_widget(control_layout)
        
        # 进度条
        self.progress_bar = ProgressBar(
            max=100,
            value=0,
            size_hint_y=None,
            height='20dp'
        )
        layout.add_widget(self.progress_bar)
        
        # 状态标签
        self.status_label = Label(
            text='准备扫描...',
            size_hint_y=None,
            height='30dp'
        )
        layout.add_widget(self.status_label)
        
        # 结果滚动视图
        scroll = ScrollView()
        self.results_layout = BoxLayout(orientation='vertical', size_hint_y=None)
        self.results_layout.bind(minimum_height=self.results_layout.setter('height'))
        scroll.add_widget(self.results_layout)
        layout.add_widget(scroll)
        
        return layout
    
    def create_analysis_interface(self):
        """创建分析界面"""
        layout = BoxLayout(orientation='vertical', padding='10dp', spacing='10dp')
        
        # 标题
        title = Label(
            text='安全分析结果',
            font_size='20sp',
            bold=True,
            size_hint_y=None,
            height='40dp'
        )
        layout.add_widget(title)
        
        # 分析结果显示区域
        scroll = ScrollView()
        self.analysis_layout = BoxLayout(orientation='vertical', size_hint_y=None)
        self.analysis_layout.bind(minimum_height=self.analysis_layout.setter('height'))
        scroll.add_widget(self.analysis_layout)
        layout.add_widget(scroll)
        
        return layout
    
    def create_tools_interface(self):
        """创建工具界面"""
        layout = BoxLayout(orientation='vertical', padding='10dp', spacing='10dp')
        
        # 标题
        title = Label(
            text='安全工具箱',
            font_size='20sp',
            bold=True,
            size_hint_y=None,
            height='40dp'
        )
        layout.add_widget(title)
        
        # 工具按钮网格
        tools_grid = GridLayout(cols=2, spacing='10dp', size_hint_y=None)
        tools_grid.bind(minimum_height=tools_grid.setter('height'))
        
        # Nordic UART攻击
        nordic_btn = Button(
            text='Nordic UART\n攻击',
            size_hint_y=None,
            height='80dp'
        )
        nordic_btn.bind(on_press=self.nordic_attack)
        
        # 自定义服务攻击
        custom_btn = Button(
            text='自定义服务\n攻击',
            size_hint_y=None,
            height='80dp'
        )
        custom_btn.bind(on_press=self.custom_attack)
        
        # 弱认证测试
        auth_btn = Button(
            text='弱认证\n测试',
            size_hint_y=None,
            height='80dp'
        )
        auth_btn.bind(on_press=self.auth_test)
        
        # 批量测试
        batch_btn = Button(
            text='批量\n测试',
            size_hint_y=None,
            height='80dp'
        )
        batch_btn.bind(on_press=self.batch_test)
        
        tools_grid.add_widget(nordic_btn)
        tools_grid.add_widget(custom_btn)
        tools_grid.add_widget(auth_btn)
        tools_grid.add_widget(batch_btn)
        
        scroll = ScrollView()
        scroll.add_widget(tools_grid)
        layout.add_widget(scroll)
        
        return layout
    
    def create_settings_interface(self):
        """创建设置界面"""
        layout = BoxLayout(orientation='vertical', padding='10dp', spacing='10dp')
        
        # 标题
        title = Label(
            text='应用设置',
            font_size='20sp',
            bold=True,
            size_hint_y=None,
            height='40dp'
        )
        layout.add_widget(title)
        
        # 设置项
        settings_layout = BoxLayout(orientation='vertical', spacing='10dp', size_hint_y=None)
        settings_layout.bind(minimum_height=settings_layout.setter('height'))
        
        # 扫描时长设置
        scan_duration_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height='40dp')
        scan_duration_layout.add_widget(Label(text='扫描时长(秒):', size_hint_x=0.6))
        self.scan_duration_input = TextInput(
            text='15',
            multiline=False,
            size_hint_x=0.4,
            input_filter='int'
        )
        scan_duration_layout.add_widget(self.scan_duration_input)
        settings_layout.add_widget(scan_duration_layout)
        
        # 关于信息
        about_label = Label(
            text='BLE共享电动车安全分析工具 v1.0\n仅用于教育和授权安全测试\n\n⚠️ 警告：请遵守当地法律法规',
            text_size=(None, None),
            halign='center',
            size_hint_y=None,
            height='120dp'
        )
        settings_layout.add_widget(about_label)
        
        scroll = ScrollView()
        scroll.add_widget(settings_layout)
        layout.add_widget(scroll)
        
        return layout
    
    def toggle_scan(self, instance):
        """切换扫描状态"""
        if not self.scanning:
            self.start_scan()
        else:
            self.stop_scan()
    
    def start_scan(self):
        """开始扫描"""
        self.scanning = True
        self.scan_btn.text = '停止扫描'
        self.scan_btn.background_color = (0.8, 0.2, 0.2, 1)
        self.status_label.text = '正在扫描BLE设备...'
        self.progress_bar.value = 0
        
        # 在后台线程中执行扫描
        threading.Thread(target=self.scan_devices, daemon=True).start()
    
    def stop_scan(self):
        """停止扫描"""
        self.scanning = False
        self.scan_btn.text = '开始扫描'
        self.scan_btn.background_color = (0.2, 0.8, 0.2, 1)
        self.status_label.text = '扫描已停止'
        self.progress_bar.value = 0
    
    def scan_devices(self):
        """扫描BLE设备（后台线程）"""
        try:
            duration = int(self.scan_duration_input.text)
        except:
            duration = 15
        
        # 模拟扫描进度
        for i in range(duration):
            if not self.scanning:
                break
            
            Clock.schedule_once(lambda dt: setattr(self.progress_bar, 'value', (i+1)/duration*100))
            Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', f'扫描中... {i+1}/{duration}秒'))
            
            # 这里应该调用实际的BLE扫描
            # 暂时使用模拟数据
            if i % 3 == 0:  # 每3秒发现一个设备
                mock_device = {
                    'name': f'SmartBike-{i//3+1:03d}',
                    'address': f'AA:BB:CC:DD:EE:{i//3+10:02X}',
                    'rssi': -45 - i,
                    'services': ['6E400001-B5A3-F393-E0A9-E50E24DCCA9E']
                }
                Clock.schedule_once(lambda dt, device=mock_device: self.add_scan_result(device))
            
            import time
            time.sleep(1)
        
        Clock.schedule_once(lambda dt: self.scan_completed())
    
    def scan_completed(self):
        """扫描完成"""
        self.scanning = False
        self.scan_btn.text = '开始扫描'
        self.scan_btn.background_color = (0.2, 0.8, 0.2, 1)
        self.status_label.text = f'扫描完成，发现 {len(self.discovered_devices)} 个设备'
        self.progress_bar.value = 100
    
    def add_scan_result(self, device_info):
        """添加扫描结果"""
        self.discovered_devices.append(device_info)
        result_item = ScanResultItem(device_info, callback=self.analyze_device)
        self.results_layout.add_widget(result_item)
    
    def clear_results(self, instance):
        """清除扫描结果"""
        self.results_layout.clear_widgets()
        self.discovered_devices.clear()
        self.status_label.text = '结果已清除'
    
    def analyze_device(self, device_info):
        """分析选中的设备"""
        self.show_analysis_popup(device_info)
    
    def show_analysis_popup(self, device_info):
        """显示分析弹窗"""
        content = BoxLayout(orientation='vertical', spacing='10dp')
        
        # 设备信息
        info_label = Label(
            text=f"设备: {device_info['name']}\n地址: {device_info['address']}\n信号强度: {device_info['rssi']}dBm",
            size_hint_y=None,
            height='80dp'
        )
        content.add_widget(info_label)
        
        # 分析结果
        analysis_label = Label(
            text="正在分析设备安全性...\n\n⚠️ 检测到潜在漏洞:\n- Nordic UART服务暴露\n- 缺乏认证机制\n- 可能存在解锁漏洞",
            text_size=(300, None),
            halign='left'
        )
        content.add_widget(analysis_label)
        
        # 按钮布局
        btn_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height='40dp', spacing='10dp')
        
        # 详细分析按钮
        detail_btn = Button(text='详细分析')
        detail_btn.bind(on_press=lambda x: self.detailed_analysis(device_info))
        
        # 关闭按钮
        close_btn = Button(text='关闭')
        
        btn_layout.add_widget(detail_btn)
        btn_layout.add_widget(close_btn)
        content.add_widget(btn_layout)
        
        # 创建弹窗
        popup = Popup(
            title='设备安全分析',
            content=content,
            size_hint=(0.9, 0.7)
        )
        
        close_btn.bind(on_press=popup.dismiss)
        popup.open()
    
    def detailed_analysis(self, device_info):
        """详细分析"""
        # 这里应该调用实际的分析功能
        Logger.info(f"BLE: Starting detailed analysis for {device_info['address']}")
    
    def nordic_attack(self, instance):
        """Nordic UART攻击"""
        self.show_info_popup("Nordic UART攻击", "正在尝试Nordic UART服务攻击...\n这可能需要几分钟时间。")
    
    def custom_attack(self, instance):
        """自定义服务攻击"""
        self.show_info_popup("自定义服务攻击", "正在尝试自定义服务攻击...\n正在枚举可写特征值。")
    
    def auth_test(self, instance):
        """弱认证测试"""
        self.show_info_popup("弱认证测试", "正在测试弱认证机制...\n尝试常见密码模式。")
    
    def batch_test(self, instance):
        """批量测试"""
        self.show_info_popup("批量测试", "正在对所有发现的设备进行批量安全测试...")
    
    def show_info_popup(self, title, message):
        """显示信息弹窗"""
        content = BoxLayout(orientation='vertical', spacing='10dp')
        
        label = Label(text=message, text_size=(300, None), halign='left')
        content.add_widget(label)
        
        close_btn = Button(text='确定', size_hint_y=None, height='40dp')
        content.add_widget(close_btn)
        
        popup = Popup(title=title, content=content, size_hint=(0.8, 0.6))
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

if __name__ == '__main__':
    BLESecurityApp().run()
