#!/usr/bin/env python3
"""
Example usage of BLE Bicycle Lock Security Analysis Tools
Demonstrates how to use the tools programmatically
"""

import asyncio
import logging
from ble_bicycle_analyzer import BLEBicycle<PERSON><PERSON>yzer
from ble_gatt_framework import BLEGATTFramework
from bicycle_lock_exploits import Bicycle<PERSON>ockExploiter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def comprehensive_security_test():
    """
    Comprehensive security test workflow
    """
    print("🔍 Starting Comprehensive BLE Bicycle Lock Security Test")
    print("=" * 60)
    
    # Step 1: Scan for devices
    print("\n📡 Step 1: Scanning for BLE devices...")
    analyzer = BLEBicycleAnalyzer()
    devices = await analyzer.scan_devices(duration=10)
    
    if not devices:
        print("❌ No potential bicycle devices found.")
        return
    
    print(f"✅ Found {len(devices)} potential bicycle devices")
    analyzer.print_summary()
    
    # Step 2: Select target device
    target_device = devices[0]  # Use first device for demo
    device_address = target_device.address
    device_name = target_device.name or "Unknown"
    
    print(f"\n🎯 Step 2: Targeting device: {device_name} ({device_address})")
    
    # Step 3: Basic security analysis
    print("\n🔒 Step 3: Performing basic security analysis...")
    security_analysis = await analyzer.analyze_device(device_address)
    
    if security_analysis:
        print(f"Risk Level: {security_analysis.risk_level}")
        print(f"Vulnerabilities: {len(security_analysis.vulnerabilities)}")
        for vuln in security_analysis.vulnerabilities[:3]:  # Show first 3
            print(f"  - {vuln}")
    
    # Step 4: Advanced GATT framework testing
    print("\n⚙️ Step 4: Advanced GATT framework testing...")
    gatt_framework = BLEGATTFramework(device_address)
    
    try:
        if await gatt_framework.connect():
            print("✅ Connected to device")
            
            # Discover services
            services = await gatt_framework.discover_services()
            print(f"📋 Discovered {len(services)} services")
            
            # Run limited attack suite (for demo)
            print("🔧 Running limited attack tests...")
            results = await gatt_framework.run_comprehensive_attack()
            
            successful_attacks = [r for r in results if r.success]
            if successful_attacks:
                print(f"⚠️ Found {len(successful_attacks)} successful attacks!")
                for result in successful_attacks[:2]:  # Show first 2
                    print(f"  - {result.attack_type.value}")
            else:
                print("✅ No successful attacks found (good security)")
        
        await gatt_framework.disconnect()
    
    except Exception as e:
        logger.error(f"GATT framework testing failed: {e}")
    
    # Step 5: Specialized exploit testing
    print("\n🎯 Step 5: Specialized exploit testing...")
    exploiter = BicycleLockExploiter()
    
    try:
        exploit_results = await exploiter.run_all_exploits(device_address)
        successful_exploits = [r for r in exploit_results if r.success]
        
        if successful_exploits:
            print(f"🚨 CRITICAL: Found {len(successful_exploits)} working exploits!")
            for result in successful_exploits:
                print(f"  ✅ {result.exploit_type.value}")
                print(f"     Command: {result.unlock_command.hex() if result.unlock_command else 'N/A'}")
                print(f"     Notes: {result.notes}")
        else:
            print("✅ No exploits successful - device appears secure")
    
    except Exception as e:
        logger.error(f"Exploit testing failed: {e}")
    
    # Step 6: Generate report
    print("\n📊 Step 6: Generating security report...")
    analyzer.save_results(f"security_report_{device_address.replace(':', '_')}.json")
    
    print("\n🎉 Comprehensive security test completed!")
    print("📄 Check the generated JSON report for detailed results.")

async def quick_scan_demo():
    """
    Quick demonstration of device scanning
    """
    print("🚀 Quick BLE Device Scan Demo")
    print("=" * 30)
    
    exploiter = BicycleLockExploiter()
    devices = await exploiter.scan_for_vulnerable_locks(duration=5)
    
    if devices:
        print(f"Found {len(devices)} potentially vulnerable devices:")
        for i, device in enumerate(devices, 1):
            print(f"{i}. {device.name or 'Unknown'} ({device.address})")
    else:
        print("No vulnerable devices found in quick scan.")

async def educational_demo():
    """
    Educational demonstration of BLE security concepts
    """
    print("📚 BLE Security Educational Demo")
    print("=" * 35)
    
    print("\n🔍 What this tool demonstrates:")
    print("1. BLE device discovery and enumeration")
    print("2. GATT service and characteristic analysis")
    print("3. Common BLE security vulnerabilities")
    print("4. Authentication bypass techniques")
    print("5. Command injection testing")
    print("6. Manufacturer backdoor detection")
    
    print("\n⚠️ Common BLE vulnerabilities in bicycle locks:")
    print("- Unencrypted communication")
    print("- Weak or missing authentication")
    print("- Exposed debug services")
    print("- Predictable unlock commands")
    print("- Lack of replay protection")
    
    print("\n🛡️ Security best practices:")
    print("- Implement strong encryption")
    print("- Use proper authentication mechanisms")
    print("- Remove debug services in production")
    print("- Implement replay protection")
    print("- Regular security audits")
    
    # Quick scan to show real devices
    print("\n📡 Scanning for nearby BLE devices...")
    analyzer = BLEBicycleAnalyzer()
    devices = await analyzer.scan_devices(duration=5)
    
    if devices:
        print(f"Educational scan found {len(devices)} devices:")
        for device in devices[:3]:  # Show first 3
            print(f"  - {device.name or 'Unknown'} ({device.address})")
            print(f"    Services: {', '.join(device.service_uuids[:2])}")  # First 2 services

async def main():
    """
    Main function with different demo modes
    """
    import sys
    
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
    else:
        mode = "educational"
    
    print("BLE Bicycle Lock Security Analysis - Example Usage")
    print("=" * 55)
    print("⚠️  WARNING: For educational and authorized testing only!")
    print("=" * 55)
    
    if mode == "comprehensive":
        await comprehensive_security_test()
    elif mode == "quick":
        await quick_scan_demo()
    elif mode == "educational":
        await educational_demo()
    else:
        print("Usage: python example_usage.py [comprehensive|quick|educational]")
        print("\nModes:")
        print("  educational  - Educational demo (default)")
        print("  quick       - Quick device scan")
        print("  comprehensive - Full security test")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        print("Make sure you have the required dependencies installed:")
        print("pip install -r requirements.txt")
