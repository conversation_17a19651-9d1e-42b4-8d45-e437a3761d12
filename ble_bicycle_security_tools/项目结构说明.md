# BLE共享电动车安全分析工具 - 项目结构

## 📁 文件结构说明

```
ble_bicycle_security_tools/
├── 📄 README.md                    # 详细使用文档和技术说明
├── 📄 requirements.txt             # Python依赖包列表
├── 📄 setup.py                     # 项目安装配置文件
├── 🚀 install_and_run.sh          # 一键安装和运行脚本
│
├── 🔧 核心工具
│   ├── 📄 ble_bicycle_analyzer.py      # 基础BLE设备扫描分析器
│   ├── 📄 ble_gatt_framework.py        # 高级GATT服务交互框架  
│   └── 📄 bicycle_lock_exploits.py     # 专用漏洞利用工具
│
├── 📚 示例和扩展
│   ├── 📄 example_usage.py             # 使用示例和教育演示
│   ├── 📄 batch_test.py                # 批量设备测试工具
│   └── 📄 realtime_monitor.py          # 实时设备监控工具
│
└── 📄 项目结构说明.md              # 本文件
```

## 🚀 快速开始

1. **进入项目目录**：
   ```bash
   cd ble_bicycle_security_tools
   ```

2. **运行安装脚本**：
   ```bash
   ./install_and_run.sh
   ```

3. **开始使用**：
   ```bash
   # 教育演示模式
   python3 example_usage.py educational
   
   # 基础扫描分析
   python3 ble_bicycle_analyzer.py
   
   # 专用漏洞利用
   python3 bicycle_lock_exploits.py
   ```

## 🔧 工具功能对比

| 工具 | 难度 | 功能 | 适用场景 |
|------|------|------|----------|
| `ble_bicycle_analyzer.py` | ⭐⭐ | 基础扫描和分析 | 新手学习，设备发现 |
| `ble_gatt_framework.py` | ⭐⭐⭐ | 高级GATT交互 | 深度分析，自定义攻击 |
| `bicycle_lock_exploits.py` | ⭐⭐⭐⭐ | 专用漏洞利用 | 实际渗透测试 |
| `batch_test.py` | ⭐⭐⭐ | 批量设备测试 | 大规模安全评估 |
| `realtime_monitor.py` | ⭐⭐ | 实时监控 | 持续安全监控 |

## ⚠️ 重要提醒

- 🔒 **仅用于教育和授权测试**
- 📚 **先阅读 README.md 了解详细信息**
- 🛡️ **遵守当地法律法规**
- 🎯 **只在自己拥有的设备上测试**

## 📞 技术支持

如有问题，请查看：
1. `README.md` - 详细文档
2. `example_usage.py` - 使用示例
3. 各工具文件中的注释说明
