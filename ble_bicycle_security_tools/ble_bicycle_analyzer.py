#!/usr/bin/env python3
"""
BLE Bicycle Lock Security Analysis Tool
Educational tool for analyzing BLE bicycle lock security
Based on publicly available security research from DEF CON, Black Hat, etc.

WARNING: This tool is for educational and authorized security testing only.
Do not use on devices you do not own or have explicit permission to test.
"""

import asyncio
import logging
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

try:
    from bleak import BleakScanner, BleakClient
    from bleak.backends.device import BLEDevice
    from bleak.backends.scanner import AdvertisementData
except ImportError:
    print("Please install bleak: pip install bleak")
    exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class BLEDeviceInfo:
    """Structure to hold BLE device information"""
    address: str
    name: Optional[str]
    rssi: int
    manufacturer_data: Dict[int, bytes]
    service_data: Dict[str, bytes]
    service_uuids: List[str]
    local_name: Optional[str]
    tx_power: Optional[int]
    scan_time: str

@dataclass
class GATTService:
    """Structure to hold GATT service information"""
    uuid: str
    description: str
    characteristics: List[Dict[str, Any]]

@dataclass
class SecurityAnalysis:
    """Structure to hold security analysis results"""
    device_address: str
    vulnerabilities: List[str]
    recommendations: List[str]
    risk_level: str
    analysis_time: str

class BLEBicycleAnalyzer:
    """Main class for BLE bicycle lock security analysis"""
    
    def __init__(self):
        self.discovered_devices: Dict[str, BLEDeviceInfo] = {}
        self.target_keywords = [
            'bike', 'bicycle', 'lock', 'wheel', 'smart', 'share',
            'mobike', 'ofo', 'lime', 'bird', 'spin', 'jump',
            # 添加更多中国品牌
            'hello', 'didi', 'meituan', 'qingju', 'kuaidi'
        ]
        
        # Common BLE service UUIDs related to locks and bicycles
        self.interesting_services = {
            '1800': 'Generic Access',
            '1801': 'Generic Attribute', 
            '180A': 'Device Information',
            '180F': 'Battery Service',
            '1812': 'Human Interface Device',
            '6E400001-B5A3-F393-E0A9-E50E24DCCA9E': 'Nordic UART Service',
            'FFF0': 'Custom Service (Common)',
            'FFE0': 'Custom Service (Common)',
        }

    async def scan_devices(self, duration: int = 10) -> List[BLEDeviceInfo]:
        """Scan for BLE devices and filter for potential bicycle locks"""
        logger.info(f"Starting BLE scan for {duration} seconds...")
        
        def detection_callback(device: BLEDevice, advertisement_data: AdvertisementData):
            device_info = BLEDeviceInfo(
                address=device.address,
                name=device.name,
                rssi=advertisement_data.rssi,
                manufacturer_data=dict(advertisement_data.manufacturer_data),
                service_data=dict(advertisement_data.service_data),
                service_uuids=advertisement_data.service_uuids,
                local_name=advertisement_data.local_name,
                tx_power=advertisement_data.tx_power,
                scan_time=datetime.now().isoformat()
            )
            
            # Check if device might be a bicycle lock
            if self._is_potential_bicycle_device(device_info):
                self.discovered_devices[device.address] = device_info
                logger.info(f"Found potential bicycle device: {device.name} ({device.address})")

        scanner = BleakScanner(detection_callback)
        await scanner.start()
        await asyncio.sleep(duration)
        await scanner.stop()
        
        logger.info(f"Scan complete. Found {len(self.discovered_devices)} potential bicycle devices.")
        return list(self.discovered_devices.values())

    def _is_potential_bicycle_device(self, device_info: BLEDeviceInfo) -> bool:
        """Check if a device might be a bicycle lock based on various indicators"""
        # Check device name
        if device_info.name:
            name_lower = device_info.name.lower()
            if any(keyword in name_lower for keyword in self.target_keywords):
                return True
        
        # Check local name
        if device_info.local_name:
            local_name_lower = device_info.local_name.lower()
            if any(keyword in local_name_lower for keyword in self.target_keywords):
                return True
        
        # Check for common bicycle lock service UUIDs
        for service_uuid in device_info.service_uuids:
            if service_uuid.upper() in self.interesting_services:
                return True
        
        # Check manufacturer data for known bicycle companies
        # This would need to be expanded with actual manufacturer IDs
        bicycle_manufacturers = [0x004C, 0x0059, 0x006D]  # Example IDs
        for mfg_id in device_info.manufacturer_data.keys():
            if mfg_id in bicycle_manufacturers:
                return True
        
        return False

    async def analyze_device(self, device_address: str) -> Optional[SecurityAnalysis]:
        """Perform security analysis on a specific device"""
        if device_address not in self.discovered_devices:
            logger.error(f"Device {device_address} not found in discovered devices")
            return None
        
        device_info = self.discovered_devices[device_address]
        logger.info(f"Analyzing device: {device_info.name} ({device_address})")
        
        vulnerabilities = []
        recommendations = []
        
        try:
            async with BleakClient(device_address) as client:
                # Get services
                services = await client.get_services()
                
                # Analyze each service
                for service in services:
                    service_analysis = await self._analyze_service(client, service)
                    vulnerabilities.extend(service_analysis.get('vulnerabilities', []))
                    recommendations.extend(service_analysis.get('recommendations', []))
                
        except Exception as e:
            logger.error(f"Failed to connect to device {device_address}: {e}")
            vulnerabilities.append(f"Connection failed: {e}")
        
        # Determine risk level
        risk_level = self._calculate_risk_level(vulnerabilities)
        
        return SecurityAnalysis(
            device_address=device_address,
            vulnerabilities=vulnerabilities,
            recommendations=recommendations,
            risk_level=risk_level,
            analysis_time=datetime.now().isoformat()
        )

    async def _analyze_service(self, client: BleakClient, service) -> Dict[str, List[str]]:
        """Analyze a specific GATT service for security issues"""
        vulnerabilities = []
        recommendations = []
        
        service_uuid = service.uuid.upper()
        service_desc = self.interesting_services.get(service_uuid, "Unknown Service")
        
        logger.info(f"Analyzing service: {service_desc} ({service_uuid})")
        
        # Check for unprotected services
        if service_uuid not in ['1800', '1801', '180A']:  # Standard services
            vulnerabilities.append(f"Custom service {service_uuid} exposed without authentication")
            recommendations.append("Implement proper authentication for custom services")
        
        # Analyze characteristics
        for char in service.characteristics:
            char_analysis = await self._analyze_characteristic(client, char)
            vulnerabilities.extend(char_analysis.get('vulnerabilities', []))
            recommendations.extend(char_analysis.get('recommendations', []))
        
        return {'vulnerabilities': vulnerabilities, 'recommendations': recommendations}

    async def _analyze_characteristic(self, client: BleakClient, characteristic) -> Dict[str, List[str]]:
        """Analyze a specific characteristic for security issues"""
        vulnerabilities = []
        recommendations = []
        
        char_uuid = characteristic.uuid
        properties = characteristic.properties
        
        # Check for writable characteristics without authentication
        if 'write' in properties or 'write-without-response' in properties:
            vulnerabilities.append(f"Writable characteristic {char_uuid} may lack proper authentication")
            recommendations.append("Implement authentication before allowing writes")
        
        # Check for readable sensitive data
        if 'read' in properties:
            try:
                # Attempt to read (this is where actual testing would happen)
                # For educational purposes, we'll just log the attempt
                logger.info(f"Characteristic {char_uuid} is readable")
                vulnerabilities.append(f"Readable characteristic {char_uuid} may expose sensitive data")
                recommendations.append("Encrypt or authenticate access to sensitive characteristics")
            except Exception as e:
                logger.debug(f"Could not read characteristic {char_uuid}: {e}")
        
        return {'vulnerabilities': vulnerabilities, 'recommendations': recommendations}

    def _calculate_risk_level(self, vulnerabilities: List[str]) -> str:
        """Calculate risk level based on found vulnerabilities"""
        if len(vulnerabilities) == 0:
            return "LOW"
        elif len(vulnerabilities) <= 3:
            return "MEDIUM"
        else:
            return "HIGH"

    def save_results(self, filename: str = None):
        """Save analysis results to JSON file"""
        if filename is None:
            filename = f"ble_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        results = {
            'scan_time': datetime.now().isoformat(),
            'devices': [asdict(device) for device in self.discovered_devices.values()]
        }
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Results saved to {filename}")

    def print_summary(self):
        """Print a summary of discovered devices"""
        print("\n" + "="*60)
        print("BLE BICYCLE LOCK ANALYSIS SUMMARY")
        print("="*60)
        
        for device in self.discovered_devices.values():
            print(f"\nDevice: {device.name or 'Unknown'}")
            print(f"Address: {device.address}")
            print(f"RSSI: {device.rssi} dBm")
            print(f"Services: {', '.join(device.service_uuids)}")
            if device.manufacturer_data:
                print(f"Manufacturer Data: {device.manufacturer_data}")

async def main():
    """Main function to run the BLE bicycle analyzer"""
    print("BLE Bicycle Lock Security Analysis Tool")
    print("=" * 50)
    print("WARNING: For educational and authorized testing only!")
    print("=" * 50)
    
    analyzer = BLEBicycleAnalyzer()
    
    # Scan for devices
    devices = await analyzer.scan_devices(duration=15)
    
    if not devices:
        print("No potential bicycle devices found.")
        return
    
    # Print summary
    analyzer.print_summary()
    
    # Save results
    analyzer.save_results()
    
    # Analyze first device found (for demonstration)
    if devices:
        first_device = devices[0]
        print(f"\nPerforming detailed analysis on: {first_device.name}")
        analysis = await analyzer.analyze_device(first_device.address)
        
        if analysis:
            print(f"\nSecurity Analysis Results:")
            print(f"Risk Level: {analysis.risk_level}")
            print(f"Vulnerabilities Found: {len(analysis.vulnerabilities)}")
            for vuln in analysis.vulnerabilities:
                print(f"  - {vuln}")

if __name__ == "__main__":
    asyncio.run(main())
