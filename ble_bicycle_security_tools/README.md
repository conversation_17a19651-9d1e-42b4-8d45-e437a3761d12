# BLE Bicycle Lock Security Analysis Tools

这是一套用于分析共享电动车BLE（蓝牙低功耗）安全性的教育工具集，基于公开的安全研究成果开发。

## ⚠️ 重要声明

**本工具仅用于教育目的和授权的安全测试！**
- 仅在您拥有或明确授权测试的设备上使用
- 不得用于非法解锁他人设备
- 使用者需承担所有法律责任

## 工具概述

### 1. `ble_bicycle_analyzer.py` - BLE设备扫描分析器
基础的BLE设备扫描和分析工具，用于发现潜在的自行车锁设备。

**功能特性：**
- 扫描附近的BLE设备
- 识别潜在的自行车锁设备
- 分析GATT服务和特征值
- 生成安全分析报告

**使用方法：**
```bash
python ble_bicycle_analyzer.py
```

### 2. `ble_gatt_framework.py` - GATT服务交互框架
高级的BLE GATT服务交互框架，提供多种攻击技术。

**功能特性：**
- 服务发现和特征值枚举
- 认证绕过尝试
- 命令注入测试
- 暴力破解攻击
- 模糊测试（Fuzzing）

**使用方法：**
```bash
python ble_gatt_framework.py <设备MAC地址>
```

### 3. `bicycle_lock_exploits.py` - 专用漏洞利用工具
基于安全研究的专门化漏洞利用工具。

**功能特性：**
- Nordic UART服务漏洞利用
- 自定义服务漏洞利用
- 弱认证机制绕过
- 制造商后门检测
- 重放攻击

**使用方法：**
```bash
python bicycle_lock_exploits.py
```

## 安装依赖

```bash
pip install bleak asyncio
```

## 技术原理

### BLE安全漏洞类型

1. **认证绕过**
   - 缺乏适当的身份验证
   - 使用可预测的认证令牌
   - 认证逻辑缺陷

2. **GATT服务暴露**
   - 敏感服务未加密
   - 特征值权限配置错误
   - 调试服务未移除

3. **命令注入**
   - 输入验证不足
   - 缓冲区溢出
   - 格式字符串漏洞

4. **重放攻击**
   - 缺乏时间戳验证
   - 无随机数机制
   - 会话管理缺陷

### 常见漏洞服务UUID

- `6E400001-B5A3-F393-E0A9-E50E24DCCA9E` - Nordic UART服务
- `FFF0` - 自定义锁控制服务
- `FFE0` - 自定义通信服务

## 安全研究参考

本工具基于以下公开安全研究：

1. **DEF CON 26** - "Hacking BLE Bicycle Locks" by Vincent Tan
2. **Black Hat** - BLE安全研究
3. **学术论文** - BLE协议安全分析

## 防护建议

### 对于制造商：
1. 实施强认证机制
2. 加密敏感通信
3. 移除调试服务
4. 定期安全审计

### 对于用户：
1. 及时更新固件
2. 使用额外物理锁
3. 避免在高风险区域停放

## 工具输出示例

```
BLE Bicycle Lock Security Analysis Tool
======================================
WARNING: For educational and authorized testing only!
======================================

Scanning for vulnerable bicycle locks...
Found vulnerable device: SmartLock-ABC123 (AA:BB:CC:DD:EE:FF)

Testing device: SmartLock-ABC123 (AA:BB:CC:DD:EE:FF)

Exploit Results:
------------------------------
🎉 Found 1 successful exploits!
✅ nordic_uart_bypass
   Command: 5700820000000000000000000000000000000000
   Notes: Successful with mobike pattern
```

## 技术细节

### Nordic UART服务漏洞
许多共享单车使用Nordic UART服务进行通信，但缺乏适当的认证：

```python
# 常见的解锁命令模式
unlock_commands = [
    b'\x57\x00\x82\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00',  # Mobike
    b'\x01',  # 简单解锁
    b'\x55\xAA\x01',  # 带头部的解锁命令
]
```

### GATT特征值分析
工具会分析以下特征值属性：
- **Read** - 可读取数据
- **Write** - 可写入数据
- **Notify** - 可接收通知
- **Indicate** - 可接收指示

### 安全测试流程
1. **设备发现** - 扫描BLE设备
2. **服务枚举** - 发现GATT服务
3. **漏洞测试** - 执行各种攻击
4. **结果分析** - 评估安全风险

## 法律声明

使用本工具进行未授权访问是违法行为。用户必须：
- 仅在自己拥有的设备上测试
- 获得明确的书面授权
- 遵守当地法律法规
- 承担使用责任

## 贡献指南

欢迎提交安全研究成果和改进建议：
1. Fork项目
2. 创建功能分支
3. 提交Pull Request
4. 详细描述改进内容

## 联系方式

如有技术问题或安全发现，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至安全团队

---

**记住：知识的力量应该用于保护，而不是破坏。**
