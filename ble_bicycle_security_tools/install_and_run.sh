#!/bin/bash

# BLE Bicycle Lock Security Tools - Installation and Setup Script
# For educational and authorized testing only!

echo "🔧 BLE Bicycle Lock Security Tools - Setup"
echo "=========================================="
echo "⚠️  WARNING: For educational and authorized testing only!"
echo "=========================================="

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8 or later."
    exit 1
fi

# Check Python version
python_version=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
echo "✅ Python version: $python_version"

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip3."
    exit 1
fi

echo "📦 Installing required dependencies..."

# Install dependencies
pip3 install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully!"
else
    echo "❌ Failed to install dependencies. Please check your internet connection and try again."
    exit 1
fi

# Make scripts executable
chmod +x ble_bicycle_analyzer.py
chmod +x ble_gatt_framework.py
chmod +x bicycle_lock_exploits.py
chmod +x example_usage.py

echo "🎯 Setup completed successfully!"
echo ""
echo "📚 Available tools:"
echo "1. ble_bicycle_analyzer.py    - Basic BLE device scanning and analysis"
echo "2. ble_gatt_framework.py      - Advanced GATT service interaction"
echo "3. bicycle_lock_exploits.py   - Specialized bicycle lock exploits"
echo "4. example_usage.py           - Example usage and educational demo"
echo ""
echo "🚀 Quick start:"
echo "python3 example_usage.py educational    # Educational demo"
echo "python3 example_usage.py quick          # Quick device scan"
echo "python3 ble_bicycle_analyzer.py         # Full analysis tool"
echo ""
echo "📖 For detailed documentation, see README.md"
echo ""
echo "⚠️  REMEMBER: Only use on devices you own or have explicit permission to test!"
