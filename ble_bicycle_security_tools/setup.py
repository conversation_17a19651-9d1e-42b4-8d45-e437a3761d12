#!/usr/bin/env python3
"""
Setup script for BLE Bicycle Lock Security Analysis Tools
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="ble-bicycle-security-tools",
    version="1.0.0",
    author="Security Research Team",
    author_email="<EMAIL>",
    description="Educational tools for analyzing BLE bicycle lock security",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/example/ble-bicycle-security-tools",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Education",
        "Intended Audience :: Information Technology",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Security",
        "Topic :: Education",
        "Topic :: System :: Networking",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "ble-bicycle-analyzer=ble_bicycle_analyzer:main",
            "ble-gatt-framework=ble_gatt_framework:main",
            "bicycle-lock-exploits=bicycle_lock_exploits:main",
        ],
    },
    keywords="bluetooth ble security bicycle lock penetration testing education",
    project_urls={
        "Bug Reports": "https://github.com/example/ble-bicycle-security-tools/issues",
        "Source": "https://github.com/example/ble-bicycle-security-tools",
        "Documentation": "https://github.com/example/ble-bicycle-security-tools/wiki",
    },
)
