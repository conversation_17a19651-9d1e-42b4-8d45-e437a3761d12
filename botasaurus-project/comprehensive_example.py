from botasaurus.browser import browser, Driver
from botasaurus.request import request, Request
from botasaurus.soupify import soupify
from botasaurus.task import task

# 示例1：使用浏览器模式进行高级爬取
@browser(
    cache=True,  # 启用缓存
    max_retry=3,  # 最大重试次数
    reuse_driver=True,  # 重用浏览器驱动
)
def scrape_with_browser(driver: Driver, url):
    """
    使用浏览器模式爬取网站
    适用于需要 JavaScript 渲染或反爬虫保护的网站
    """
    # 启用人类模式，模拟真实用户行为
    driver.enable_human_mode()
    
    # 访问网站
    driver.google_get(url)
    
    # 等待页面加载
    driver.wait(2)
    
    # 获取页面信息
    title = driver.title
    current_url = driver.current_url
    
    # 尝试获取主要内容
    try:
        heading = driver.get_text('h1')
    except:
        heading = "无法获取标题"
    
    return {
        "title": title,
        "heading": heading,
        "url": current_url,
        "method": "browser"
    }

# 示例2：使用请求模式进行快速爬取
@request(
    cache=True,
    max_retry=5,
)
def scrape_with_request(request: Request, url):
    """
    使用请求模式进行快速爬取
    适用于静态网站或API
    """
    try:
        # 发送请求
        response = request.get(url)
        response.raise_for_status()
        
        # 解析HTML
        soup = soupify(response)
        
        # 提取信息
        title = soup.find('title').get_text() if soup.find('title') else "无标题"
        heading = soup.find('h1').get_text() if soup.find('h1') else "无标题"
        
        return {
            "title": title.strip(),
            "heading": heading.strip(),
            "url": url,
            "method": "request",
            "status_code": response.status_code
        }
    except Exception as e:
        return {
            "error": str(e),
            "url": url,
            "method": "request"
        }

# 示例3：任务管理和批量处理
@task(cache=True)
def batch_scrape_websites(urls):
    """
    批量爬取多个网站
    """
    results = []
    
    for url in urls:
        print(f"正在处理: {url}")
        
        # 首先尝试使用请求模式（更快）
        try:
            result = scrape_with_request(url)
            if "error" not in result:
                results.append(result)
                continue
        except:
            pass
        
        # 如果请求模式失败，使用浏览器模式
        try:
            result = scrape_with_browser(url)
            results.append(result)
        except Exception as e:
            results.append({
                "error": str(e),
                "url": url,
                "method": "failed"
            })
    
    return results

# 主函数
def main():
    """
    主函数：演示 Botasaurus 的各种功能
    """
    print("=== Botasaurus 综合示例 ===\n")
    
    # 测试网站列表
    test_urls = [
        "https://httpbin.org/html",  # 简单的测试网站
        "https://example.com",       # 基础示例网站
    ]
    
    print("1. 测试浏览器模式:")
    try:
        browser_result = scrape_with_browser("https://example.com")
        print(f"   结果: {browser_result}")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n2. 测试请求模式:")
    try:
        request_result = scrape_with_request("https://httpbin.org/html")
        print(f"   结果: {request_result}")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n3. 测试批量处理:")
    try:
        batch_results = batch_scrape_websites(test_urls)
        print(f"   处理了 {len(batch_results)} 个网站")
        for result in batch_results:
            print(f"   - {result.get('url', 'Unknown')}: {result.get('method', 'Unknown')}")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
