#!/usr/bin/env python3
"""
Botasaurus 快速启动脚本
选择并运行不同的示例
"""

import os
import sys

def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🤖 欢迎使用 Botasaurus - 无法被检测的网络爬虫框架")
    print("=" * 60)
    print()

def print_menu():
    """打印菜单选项"""
    print("请选择要运行的示例:")
    print()
    print("1. 🌐 基础示例 (main.py)")
    print("   - 访问 omkar.cloud 并提取标题")
    print("   - 适合初学者了解基本功能")
    print()
    print("2. ✅ 简单测试 (simple_test.py)")
    print("   - 访问 example.com 进行基础测试")
    print("   - 验证安装是否正确")
    print()
    print("3. 🛡️ Cloudflare 绕过示例 (cloudflare_example.py)")
    print("   - 演示如何绕过 Cloudflare 保护")
    print("   - 需要稳定的网络连接")
    print()
    print("4. 🔐 Turnstile 验证码绕过 (turnstile_bypass_example.py)")
    print("   - 自动解决 Cloudflare Turnstile 验证码")
    print("   - 展示高级反检测功能")
    print()
    print("5. 🚀 综合功能示例 (comprehensive_example.py)")
    print("   - 展示多种爬取模式和批量处理")
    print("   - 包含错误处理和重试机制")
    print()
    print("6. 📊 查看已有结果")
    print("   - 查看 output 文件夹中的爬取结果")
    print()
    print("7. 📖 查看文档")
    print("   - 显示 README.md 内容")
    print()
    print("0. 🚪 退出")
    print()

def run_script(script_name):
    """运行指定的脚本"""
    print(f"正在运行 {script_name}...")
    print("-" * 40)
    os.system(f"python3 {script_name}")
    print("-" * 40)
    print(f"✅ {script_name} 运行完成")
    print()

def show_results():
    """显示输出结果"""
    output_dir = "output"
    if os.path.exists(output_dir):
        print("📊 爬取结果:")
        print("-" * 40)
        for file in os.listdir(output_dir):
            if file.endswith('.json'):
                file_path = os.path.join(output_dir, file)
                print(f"📄 {file}:")
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        print(content)
                except Exception as e:
                    print(f"   读取失败: {e}")
                print()
    else:
        print("❌ 没有找到输出结果，请先运行一些示例")
    print()

def show_readme():
    """显示 README 内容"""
    try:
        with open("README.md", 'r', encoding='utf-8') as f:
            content = f.read()
            # 只显示前50行，避免输出过长
            lines = content.split('\n')[:50]
            print("📖 README.md (前50行):")
            print("-" * 40)
            print('\n'.join(lines))
            if len(content.split('\n')) > 50:
                print("\n... (更多内容请直接查看 README.md 文件)")
    except Exception as e:
        print(f"❌ 读取 README.md 失败: {e}")
    print()

def main():
    """主函数"""
    while True:
        print_banner()
        print_menu()
        
        try:
            choice = input("请输入选项 (0-7): ").strip()
            print()
            
            if choice == '0':
                print("👋 感谢使用 Botasaurus！再见！")
                break
            elif choice == '1':
                run_script("main.py")
            elif choice == '2':
                run_script("simple_test.py")
            elif choice == '3':
                run_script("cloudflare_example.py")
            elif choice == '4':
                run_script("turnstile_bypass_example.py")
            elif choice == '5':
                run_script("comprehensive_example.py")
            elif choice == '6':
                show_results()
            elif choice == '7':
                show_readme()
            else:
                print("❌ 无效选项，请输入 0-7 之间的数字")
                print()
            
            if choice != '0':
                input("按 Enter 键继续...")
                print("\n" * 2)  # 清空屏幕
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            input("按 Enter 键继续...")

if __name__ == "__main__":
    main()
