#!/usr/bin/env python3
"""
Botasaurus 安装验证脚本
用于验证 Botasaurus 是否正确安装并可以正常工作
"""

import sys
import json
from botasaurus.browser import browser, Driver

def check_installation():
    """检查 Botasaurus 安装状态"""
    print("🔍 检查 Botasaurus 安装状态...")
    
    try:
        import botasaurus
        import botasaurus_driver
        import botasaurus_requests
        import botasaurus_api
        import botasaurus_humancursor
        print("✅ 所有 Botasaurus 模块已正确安装")
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

@browser
def test_basic_functionality(driver: Driver, data):
    """测试基本功能"""
    print("🌐 测试基本网页访问功能...")
    
    # 访问测试网站
    driver.get("https://example.com")
    
    # 获取基本信息
    title = driver.title
    url = driver.current_url
    
    # 获取页面内容
    try:
        heading = driver.get_text('h1')
    except:
        heading = "无法获取标题"
    
    return {
        "title": title,
        "heading": heading,
        "url": url,
        "test_passed": True
    }

@browser
def test_cloudflare_bypass(driver: Driver, data):
    """测试 Cloudflare 绕过功能"""
    print("🛡️ 测试 Cloudflare 绕过功能...")
    
    try:
        # 使用 google_get 访问受保护的网站
        driver.google_get("https://www.cloudflare.com/")
        
        title = driver.title
        url = driver.current_url
        
        return {
            "title": title,
            "url": url,
            "cloudflare_bypass_test": True,
            "success": "cloudflare" in title.lower() or "cloudflare" in url.lower()
        }
    except Exception as e:
        return {
            "error": str(e),
            "cloudflare_bypass_test": False,
            "success": False
        }

def run_verification():
    """运行完整的验证流程"""
    print("=" * 50)
    print("🤖 Botasaurus 安装验证开始")
    print("=" * 50)
    
    # 1. 检查安装
    if not check_installation():
        print("❌ 安装验证失败，请重新安装 Botasaurus")
        return False
    
    # 2. 测试基本功能
    print("\n📋 测试 1: 基本网页访问功能")
    try:
        result1 = test_basic_functionality()
        if result1 and result1.get('test_passed'):
            print("✅ 基本功能测试通过")
            print(f"   - 访问网站: {result1.get('url')}")
            print(f"   - 页面标题: {result1.get('title')}")
            print(f"   - 主要内容: {result1.get('heading')}")
        else:
            print("❌ 基本功能测试失败")
            return False
    except Exception as e:
        print(f"❌ 基本功能测试出错: {e}")
        return False
    
    # 3. 测试 Cloudflare 绕过（可选）
    print("\n📋 测试 2: Cloudflare 绕过功能")
    try:
        result2 = test_cloudflare_bypass()
        if result2 and result2.get('success'):
            print("✅ Cloudflare 绕过测试通过")
            print(f"   - 访问网站: {result2.get('url')}")
            print(f"   - 页面标题: {result2.get('title')}")
        else:
            print("⚠️ Cloudflare 绕过测试未完全通过（这是正常的，可能由于网络原因）")
            if result2 and 'error' in result2:
                print(f"   - 错误信息: {result2['error']}")
    except Exception as e:
        print(f"⚠️ Cloudflare 绕过测试出错: {e}")
        print("   这通常是由于网络连接问题，不影响基本功能")
    
    print("\n" + "=" * 50)
    print("🎉 Botasaurus 安装验证完成！")
    print("=" * 50)
    
    # 显示使用指南
    print("\n📚 快速使用指南:")
    print("1. 运行基本示例: python3 main.py")
    print("2. 查看输出结果: cat output/scrape_heading_task.json")
    print("3. 阅读完整文档: cat README.md")
    print("4. 探索更多示例:")
    print("   - cloudflare_example.py (Cloudflare 绕过)")
    print("   - turnstile_bypass_example.py (Turnstile 验证码绕过)")
    print("   - comprehensive_example.py (综合功能演示)")
    
    return True

if __name__ == "__main__":
    success = run_verification()
    if success:
        print("\n🚀 您现在可以开始使用 Botasaurus 了！")
        sys.exit(0)
    else:
        print("\n❌ 验证失败，请检查安装或网络连接")
        sys.exit(1)
