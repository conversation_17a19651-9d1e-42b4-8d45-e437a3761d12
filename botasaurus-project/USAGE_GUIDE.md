# 🚀 Botasaurus 使用指南

## 📋 快速开始

### 方法1: 交互式启动（推荐新手）
```bash
cd botasaurus-project
python3 start.py
```
然后选择相应的选项。

### 方法2: 直接使用 Turnstile 绕过工具
```bash
cd botasaurus-project
python3 turnstile_solver_tool.py
```

## 🔐 Turnstile 验证码绕过详细步骤

### 步骤1: 启动工具
```bash
python3 turnstile_solver_tool.py
```

### 步骤2: 输入网址
当提示时，输入您要访问的网址，例如：
```
请输入要访问的网址: https://nopecha.com/demo/cloudflare
```

### 步骤3: 等待处理
工具会自动：
1. 🚀 启动 Chrome 浏览器
2. 🛡️ 使用反检测技术访问网站
3. 🤖 自动处理 Turnstile 验证码
4. ✅ 显示处理结果

### 步骤4: 查看结果
- 成功：显示 "✅ 成功绕过 Cloudflare Turnstile 验证！"
- 失败：显示具体的错误信息

## 💡 使用示例

### 示例1: 测试 Cloudflare 保护的网站
```bash
python3 turnstile_solver_tool.py
# 输入: https://nopecha.com/demo/cloudflare
```

### 示例2: 命令行直接使用
```bash
python3 turnstile_solver_tool.py https://example-protected-site.com
```

### 示例3: 批量处理多个网址
```bash
python3 turnstile_solver_tool.py https://site1.com https://site2.com https://site3.com
```

## 🎯 支持的网站类型

✅ **完全支持**:
- Cloudflare 保护的网站
- Turnstile 验证码网站
- 基础的反爬虫保护

⚠️ **部分支持**:
- 复杂的多步验证
- 需要登录的网站
- 高度定制的反爬虫系统

❌ **不支持**:
- 需要人工验证的 CAPTCHA
- 需要手机验证码的网站

## 🔧 高级用法

### 自定义脚本
您可以基于现有代码创建自己的脚本：

```python
from botasaurus.browser import browser, Driver

@browser
def my_custom_scraper(driver: Driver, url):
    # 使用 Cloudflare 绕过
    driver.google_get(url, bypass_cloudflare=True)
    
    # 等待页面加载
    import time
    time.sleep(3)
    
    # 提取数据
    title = driver.title
    content = driver.get_text('body')
    
    return {
        "url": url,
        "title": title,
        "content": content[:500]  # 前500个字符
    }

# 使用
result = my_custom_scraper("https://your-target-site.com")
print(result)
```

### 配置选项
```python
@browser(
    headless=False,           # 显示浏览器窗口
    cache=False,             # 不使用缓存
    max_retry=3,             # 最多重试3次
    reuse_driver=False,      # 每次使用新浏览器
    block_images_and_css=True, # 阻止图片和CSS加载（加速）
)
def fast_scraper(driver: Driver, url):
    driver.google_get(url, bypass_cloudflare=True)
    return {"title": driver.title}
```

## 📊 结果说明

### 成功的结果示例
```json
{
  "timestamp": "2025-07-22 13:30:45",
  "original_url": "https://example.com",
  "final_url": "https://example.com/",
  "title": "Example Domain",
  "success": true,
  "page_preview": "Example Domain This domain is for use...",
  "status": "成功绕过 Turnstile 验证"
}
```

### 失败的结果示例
```json
{
  "timestamp": "2025-07-22 13:30:45",
  "original_url": "https://protected-site.com",
  "error": "Connection timeout",
  "success": false,
  "status": "访问失败: Connection timeout"
}
```

## 🛠️ 故障排除

### 常见问题

**Q: 浏览器无法启动**
A: 确保已安装 Chrome 浏览器，并检查系统权限

**Q: 网络连接超时**
A: 检查网络连接，或尝试使用代理

**Q: 验证码仍然出现**
A: 某些高级保护可能需要多次尝试，或者网站使用了更复杂的验证

**Q: 脚本运行缓慢**
A: 这是正常的，反检测技术需要时间来模拟真实用户行为

### 优化建议

1. **网络环境**: 使用稳定的网络连接
2. **代理设置**: 如果IP被封，考虑使用住宅代理
3. **重试机制**: 失败时可以多尝试几次
4. **时间间隔**: 不要过于频繁地访问同一网站

## 🔒 使用注意事项

### 法律合规
- ✅ 确保遵守目标网站的使用条款
- ✅ 遵守当地法律法规
- ✅ 尊重网站的 robots.txt

### 技术建议
- ⚠️ 不要过度频繁地请求
- ⚠️ 使用合理的延迟间隔
- ⚠️ 避免对网站造成过大负载

### 数据处理
- 🔐 妥善保护爬取的数据
- 🔐 不要泄露敏感信息
- 🔐 遵守数据保护法规

## 📞 获取帮助

如果遇到问题：
1. 查看 `README.md` 获取详细文档
2. 检查 `output/` 文件夹中的错误日志
3. 参考官方文档: https://www.omkar.cloud/botasaurus/
4. 查看 GitHub 仓库: https://github.com/omkarcloud/botasaurus

---

## 🎉 开始使用

现在您已经了解了如何使用 Botasaurus！

**最简单的开始方式**:
```bash
cd botasaurus-project
python3 turnstile_solver_tool.py
```

然后输入您想要访问的网址，工具会自动处理 Cloudflare Turnstile 验证码！
