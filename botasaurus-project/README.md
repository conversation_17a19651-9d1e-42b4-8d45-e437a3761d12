# Botasaurus 项目

这是一个完整的 Botasaurus 安装和示例项目，展示了这个强大的网络爬虫框架的各种功能。

## 🚀 Botasaurus 简介

Botasaurus 是一个全能的网络爬虫框架，专门设计用于构建无法被检测的爬虫。它的主要特点包括：

- **绕过反爬虫检测**：能够绕过 Cloudflare、Datadome、Fingerprint 等主流反爬虫系统
- **Cloudflare Turnstile 绕过**：自动解决 Cloudflare Turnstile 验证码
- **人类化操作**：模拟真实用户的鼠标移动和点击行为
- **双模式支持**：支持浏览器模式和请求模式
- **缓存系统**：智能缓存，避免重复请求
- **并行处理**：支持多线程并行爬取

## 📁 项目结构

```
botasaurus-project/
├── main.py                      # 基础示例：爬取网站标题
├── request_example.py           # 请求模式示例
├── cloudflare_example.py        # Cloudflare 绕过示例
├── turnstile_bypass_example.py  # Turnstile 验证码绕过示例
├── comprehensive_example.py     # 综合功能示例
├── output/                      # 输出文件夹
└── README.md                    # 本文件
```

## 🛠️ 安装

Botasaurus 已经安装完成，包含以下组件：
- botasaurus (主框架)
- botasaurus-driver (浏览器驱动)
- botasaurus-requests (请求模块)
- botasaurus-api (API 模块)
- botasaurus-humancursor (人类化鼠标)
- botasaurus-proxy-authentication (代理认证)

## 📖 使用示例

### 1. 基础示例 (main.py)
```bash
python3 main.py
```
这个示例展示了如何使用浏览器模式爬取网站标题。

### 2. Cloudflare 绕过示例 (cloudflare_example.py)
```bash
python3 cloudflare_example.py
```
演示如何访问受 Cloudflare 保护的网站。

### 3. Turnstile 验证码绕过 (turnstile_bypass_example.py)
```bash
python3 turnstile_bypass_example.py
```
展示如何自动绕过 Cloudflare Turnstile 验证码。

### 4. 综合功能示例 (comprehensive_example.py)
```bash
python3 comprehensive_example.py
```
包含多种爬取模式和批量处理功能的综合示例。

## 🔧 核心功能

### 浏览器模式
```python
from botasaurus.browser import browser, Driver

@browser
def scrape_with_browser(driver: Driver, data):
    driver.google_get("https://example.com")
    return {"title": driver.title}
```

### 请求模式
```python
from botasaurus.request import request, Request
from botasaurus.soupify import soupify

@request
def scrape_with_request(request: Request, data):
    response = request.get("https://example.com")
    soup = soupify(response)
    return {"title": soup.find('title').get_text()}
```

### Cloudflare 绕过
```python
@browser
def bypass_cloudflare(driver: Driver, data):
    # 使用 bypass_cloudflare=True 参数
    driver.google_get("https://protected-site.com", bypass_cloudflare=True)
    return {"success": True}
```

## 🎯 高级功能

### 人类化模式
```python
driver.enable_human_mode()  # 启用人类化鼠标移动
driver.human_click(element)  # 人类化点击
```

### 缓存和重试
```python
@browser(cache=True, max_retry=5, reuse_driver=True)
def cached_scraper(driver: Driver, data):
    # 自动缓存结果，支持重试
    pass
```

### 并行处理
```python
@browser(parallel=10)  # 10个并行任务
def parallel_scraper(driver: Driver, data):
    pass
```

## 📊 输出结果

所有爬取结果会自动保存到 `output/` 文件夹中，格式为 JSON。

## 🔍 调试和监控

- 使用 `driver.prompt()` 暂停执行进行调试
- 查看 `output/` 文件夹中的结果
- 使用缓存避免重复请求

## 🌟 特色功能

1. **反检测能力**：通过多种技术绕过主流反爬虫系统
2. **Turnstile 自动解决**：无需手动处理验证码
3. **智能重试**：自动处理网络错误和临时故障
4. **代理支持**：支持各种代理配置
5. **桌面应用**：可以将爬虫转换为桌面应用

## 📝 注意事项

- 请遵守网站的 robots.txt 和使用条款
- 合理设置请求间隔，避免对目标网站造成压力
- 在生产环境中使用代理以避免IP被封
- 定期更新 Botasaurus 以获得最新的反检测功能

## 🆘 故障排除

如果遇到问题：
1. 确保网络连接正常
2. 检查目标网站是否可访问
3. 尝试使用代理
4. 查看错误日志
5. 更新到最新版本

## 📚 更多资源

- [官方文档](https://www.omkar.cloud/botasaurus/)
- [GitHub 仓库](https://github.com/omkarcloud/botasaurus)
- [示例项目](https://github.com/omkarcloud/botasaurus-starter)

---

现在您可以开始使用 Botasaurus 进行网络爬虫开发了！🎉
