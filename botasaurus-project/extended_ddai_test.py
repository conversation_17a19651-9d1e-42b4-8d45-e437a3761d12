#!/usr/bin/env python3
"""
扩展版 DDAI Turnstile 测试工具
增加了更长的等待时间和详细的验证状态监控
"""

from botasaurus.browser import browser, Driver
import time

@browser(
    headless=False, 
    cache=False, 
    max_retry=2,
    wait_for_complete_page_load=False,
    block_images_and_css=True,
)
def extended_ddai_test(driver: Driver, data):
    """
    扩展版 DDAI 网站访问测试 - 详细监控 Cloudflare 验证过程
    """
    url = "https://app.ddai.space/register"
    
    print(f"🚀 访问 DDAI 注册页面: {url}")
    print("🔧 使用扩展配置：详细监控 Cloudflare 验证过程")
    
    try:
        # 第一阶段：初始访问
        print("\n📍 第一阶段：启动 Cloudflare 绕过模式")
        driver.google_get(url, bypass_cloudflare=True)
        
        # 第二阶段：初始等待和状态检查
        print("\n📍 第二阶段：初始页面加载检查")
        time.sleep(5)
        
        current_url = driver.current_url
        title = driver.title
        print(f"   当前URL: {current_url}")
        print(f"   页面标题: {title}")
        
        # 第三阶段：等待页面完全加载
        print("\n📍 第三阶段：等待页面完全加载")
        print("⏳ 等待10秒让页面完全加载...")
        time.sleep(10)
        
        # 第四阶段：最终状态确认
        print(f"\n📍 第四阶段：最终状态确认")
        final_url = driver.current_url
        final_title = driver.title
        
        print(f"   最终URL: {final_url}")
        print(f"   最终标题: {final_title}")
        
        # 获取页面内容
        try:
            body_text = driver.get_text('body')
            if body_text:
                print(f"\n📝 页面内容预览 (前400字符):")
                print(body_text[:400])
                print("...")
                
                # 详细内容分析
                has_register = "register" in body_text.lower()
                has_email = "email" in body_text.lower()
                has_password = "password" in body_text.lower()
                has_cloudflare = "cloudflare" in body_text.lower()
                has_turnstile = "turnstile" in body_text.lower()
                has_checking = "checking your browser" in body_text.lower()
                has_moment = "just a moment" in body_text.lower()
                
                print(f"\n🔍 详细内容分析:")
                print(f"   注册相关内容: {'✅' if has_register else '❌'}")
                print(f"   邮箱字段: {'✅' if has_email else '❌'}")
                print(f"   密码字段: {'✅' if has_password else '❌'}")
                print(f"   Cloudflare 文本: {'⚠️' if has_cloudflare else '✅'}")
                print(f"   Turnstile 文本: {'⚠️' if has_turnstile else '✅'}")
                print(f"   浏览器检查文本: {'⚠️' if has_checking else '✅'}")
                print(f"   请稍候文本: {'⚠️' if has_moment else '✅'}")
                
                # 判断验证状态
                cf_indicators = has_cloudflare or has_turnstile or has_checking or has_moment
                success_indicators = has_register and has_email and not cf_indicators
                
                if success_indicators:
                    print("\n🎉 Cloudflare 验证状态: ✅ 已成功绕过")
                elif cf_indicators:
                    print("\n⚠️ Cloudflare 验证状态: 🔄 仍在验证中")
                else:
                    print("\n❓ Cloudflare 验证状态: 🤔 状态不明确")
                    
            else:
                print("⚠️ 无法获取页面文本内容")
                
        except Exception as e:
            print(f"❌ 获取页面内容失败: {e}")
        
        # 第五阶段：延长观察时间
        print(f"\n📍 第五阶段：延长观察时间")
        print("🔍 浏览器将保持打开60秒，供您详细观察验证过程...")
        print("💡 请在浏览器中观察:")
        print("   1. 是否出现 Turnstile 验证码框")
        print("   2. 验证码是否自动完成")
        print("   3. 注册表单是否可用")
        print("   4. 页面是否完全加载")
        
        # 分段显示倒计时
        for remaining in range(60, 0, -10):
            print(f"   ⏰ 剩余观察时间: {remaining} 秒...")
            time.sleep(10)
        
        print("✅ 观察时间结束")
        
        return {
            "url": final_url,
            "title": final_title,
            "success": "ddai.space" in final_url.lower() and "register" in final_url.lower(),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
    except Exception as e:
        print(f"❌ 访问过程中出现错误: {e}")
        return {
            "url": "访问失败",
            "title": "访问失败", 
            "success": False,
            "error": str(e),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

def main():
    """主函数"""
    print("=" * 70)
    print("🤖 扩展版 DDAI Turnstile 详细监控工具")
    print("=" * 70)
    print("🎯 目标: https://app.ddai.space/register")
    print("🔧 特性: 详细监控 Cloudflare 验证过程，延长观察时间")
    print("⏰ 总时间: 约2-3分钟 (包含详细监控和观察)")
    print()
    
    try:
        result = extended_ddai_test()
        
        print("\n" + "=" * 70)
        print("📊 最终测试结果:")
        print("=" * 70)
        print(f"🕐 测试时间: {result.get('timestamp', 'N/A')}")
        print(f"🌐 最终URL: {result.get('url', 'N/A')}")
        print(f"📄 页面标题: {result.get('title', 'N/A')}")
        print(f"✅ 访问状态: {'🎉 成功' if result.get('success') else '❌ 失败'}")
        
        if result.get('error'):
            print(f"❌ 错误信息: {result.get('error')}")
        
        print("=" * 70)
        
        if result.get('success'):
            print("\n🎉 测试完成！您已经详细观察了整个 Cloudflare 验证过程")
            print("💡 现在您可以确信工具的绕过能力和验证状态")
        else:
            print("\n⚠️ 这次测试遇到了问题，但您已经看到了详细的诊断信息")
            
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
