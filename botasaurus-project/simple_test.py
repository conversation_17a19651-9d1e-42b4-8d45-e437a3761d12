from botasaurus.browser import browser, Driver

@browser
def simple_test(driver: Driver, data):
    """
    简单测试：访问一个基础网站
    """
    # 访问一个简单的测试网站
    driver.get("https://example.com")

    # 获取页面信息
    title = driver.title
    current_url = driver.current_url

    # 尝试获取页面内容
    try:
        heading = driver.get_text('h1')
    except:
        heading = "无法获取标题"

    return {
        "title": title,
        "heading": heading,
        "url": current_url,
        "success": True
    }

if __name__ == "__main__":
    print("=== Botasaurus 简单测试 ===")
    try:
        result = simple_test()
        print("测试结果:", result)
        print("✅ 测试成功！Botasaurus 工作正常。")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("请检查网络连接或稍后重试。")
