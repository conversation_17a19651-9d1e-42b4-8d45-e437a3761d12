Traceback (most recent call last):
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/botasaurus/browser_decorator.py", line 201, in run_task
    result = func(driver, data)
  File "/Users/<USER>/Desktop/自动化验证/botasaurus-project/simple_ddai_test.py", line 20, in test_ddai_access
    driver.google_get(url, bypass_cloudflare=True)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/botasaurus_driver/driver.py", line 2230, in google_get
    self.get_via(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/botasaurus_driver/driver.py", line 2201, in get_via
    wait_till_document_is_ready(self._tab, self.config.wait_for_complete_page_load, timeout=timeout)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/botasaurus_driver/solve_cloudflare_captcha.py", line 40, in wait_till_document_is_ready
    raise TimeoutError(f"Document did not become ready within {timeout} seconds")
TimeoutError: Document did not become ready within 60 seconds
