#!/usr/bin/env python3
"""
Botasaurus Turnstile 验证码绕过工具
专门用于绕过 Cloudflare Turnstile 验证码
"""

from botasaurus.browser import browser, Driver
import sys
import json
from datetime import datetime

@browser(
    # 重要配置：启用 Cloudflare 绕过功能
    cache=False,  # 不使用缓存，确保每次都是新的尝试
    max_retry=3,  # 最多重试3次
    reuse_driver=False,  # 每次使用新的浏览器实例
    headless=False,  # 不使用无头模式，因为 Turnstile 可能检测无头浏览器
)
def solve_turnstile(driver: Driver, url):
    """
    绕过 Cloudflare Turnstile 验证码
    
    Args:
        driver: Botasaurus 浏览器驱动
        url: 要访问的网址
    
    Returns:
        dict: 包含访问结果的字典
    """
    print(f"🚀 开始处理网址: {url}")
    print("🔄 正在启动浏览器...")
    
    try:
        # 使用 google_get 方法，并启用 Cloudflare 绕过
        print("🛡️ 使用 Cloudflare 绕过模式访问网站...")
        driver.google_get(url, bypass_cloudflare=True)
        
        # 等待页面完全加载
        print("⏳ 等待页面加载和验证码处理...")
        import time
        time.sleep(5)  # 给 Turnstile 验证一些时间
        
        # 获取当前页面信息
        current_url = driver.current_url
        title = driver.title
        
        print(f"📍 当前URL: {current_url}")
        print(f"📄 页面标题: {title}")
        
        # 检查是否还在验证页面
        try:
            page_source = driver.get_page_source().lower()
        except:
            page_source = ""

        # 判断是否成功绕过
        success_indicators = [
            "challenge" not in current_url.lower(),
            "cloudflare" not in title.lower() or "just a moment" not in page_source,
            "checking your browser" not in page_source,
            "please wait" not in page_source
        ]
        
        is_success = all(success_indicators)
        
        # 尝试获取页面内容
        try:
            # 获取页面的主要内容
            body_text = driver.get_text('body')[:1000] if driver.get_text('body') else "无法获取页面内容"
        except:
            body_text = "无法获取页面内容"
        
        result = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "original_url": url,
            "final_url": current_url,
            "title": title,
            "success": is_success,
            "page_preview": body_text,
            "status": "成功绕过 Turnstile 验证" if is_success else "可能仍在验证中"
        }
        
        if is_success:
            print("✅ 成功绕过 Cloudflare Turnstile 验证！")
        else:
            print("⚠️ 可能仍在验证过程中，请检查浏览器窗口")
        
        return result
        
    except Exception as e:
        error_result = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "original_url": url,
            "error": str(e),
            "success": False,
            "status": f"访问失败: {str(e)}"
        }
        print(f"❌ 访问失败: {e}")
        return error_result

def interactive_mode():
    """交互式模式"""
    print("=" * 60)
    print("🤖 Botasaurus Turnstile 验证码绕过工具")
    print("=" * 60)
    print()
    print("这个工具可以帮助您绕过 Cloudflare Turnstile 验证码")
    print("支持的网站类型:")
    print("- 受 Cloudflare 保护的网站")
    print("- 带有 Turnstile 验证码的网站")
    print("- 其他反爬虫保护的网站")
    print()
    
    while True:
        try:
            url = input("请输入要访问的网址 (输入 'quit' 退出): ").strip()
            
            if url.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            
            if not url:
                print("❌ 请输入有效的网址")
                continue
            
            # 确保URL有协议
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            print(f"\n🎯 开始处理: {url}")
            print("-" * 40)
            
            # 调用绕过函数
            result = solve_turnstile(url)
            
            print("-" * 40)
            print("📊 处理结果:")
            print(f"   原始网址: {result.get('original_url', 'N/A')}")
            print(f"   最终网址: {result.get('final_url', 'N/A')}")
            print(f"   页面标题: {result.get('title', 'N/A')}")
            print(f"   处理状态: {result.get('status', 'N/A')}")
            print(f"   是否成功: {'✅ 是' if result.get('success') else '❌ 否'}")
            
            # 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"output/turnstile_result_{timestamp}.json"
            
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                print(f"💾 结果已保存到: {filename}")
            except Exception as e:
                print(f"⚠️ 保存结果失败: {e}")
            
            print("\n" + "=" * 60)
            
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

def batch_mode(urls):
    """批量处理模式"""
    print(f"🚀 批量处理 {len(urls)} 个网址...")
    results = []
    
    for i, url in enumerate(urls, 1):
        print(f"\n📍 处理第 {i}/{len(urls)} 个网址: {url}")
        print("-" * 40)
        
        result = solve_turnstile(url)
        results.append(result)
        
        print(f"✅ 完成 {i}/{len(urls)}")
    
    # 保存批量结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"output/batch_turnstile_results_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"\n💾 批量结果已保存到: {filename}")
    except Exception as e:
        print(f"⚠️ 保存批量结果失败: {e}")
    
    return results

def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 命令行模式
        urls = sys.argv[1:]
        if len(urls) == 1:
            print("🎯 单个网址处理模式")
            result = solve_turnstile(urls[0])
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print("📦 批量处理模式")
            batch_mode(urls)
    else:
        # 交互式模式
        interactive_mode()

if __name__ == "__main__":
    main()
