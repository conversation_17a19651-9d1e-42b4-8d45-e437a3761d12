#!/usr/bin/env python3
"""
简化版 DDAI Turnstile 测试工具
专注于基本的访问和验证码绕过
"""

from botasaurus.browser import browser, Driver
import time

@browser(headless=False, cache=False)
def test_ddai_access(driver: Driver, data):
    """
    简单测试 DDAI 网站访问
    """
    url = "https://app.ddai.space/register"
    
    print(f"🚀 访问 DDAI 注册页面: {url}")
    
    # 使用 Cloudflare 绕过模式访问
    driver.google_get(url, bypass_cloudflare=True)
    
    print("⏳ 等待页面加载...")
    time.sleep(10)  # 等待10秒让页面完全加载
    
    # 获取基本信息
    current_url = driver.current_url
    title = driver.title
    
    print(f"📍 当前URL: {current_url}")
    print(f"📄 页面标题: {title}")
    
    # 尝试获取页面文本内容
    try:
        body_text = driver.get_text('body')
        print(f"📝 页面内容预览 (前200字符):")
        print(body_text[:200])
        print("...")
        
        # 检查关键词
        has_register = "register" in body_text.lower()
        has_email = "email" in body_text.lower()
        has_password = "password" in body_text.lower()
        has_turnstile = "turnstile" in body_text.lower() or "cloudflare" in body_text.lower()
        
        print(f"\n🔍 页面内容分析:")
        print(f"   注册相关内容: {'✅' if has_register else '❌'}")
        print(f"   邮箱字段: {'✅' if has_email else '❌'}")
        print(f"   密码字段: {'✅' if has_password else '❌'}")
        print(f"   Turnstile/Cloudflare: {'⚠️' if has_turnstile else '✅'}")
        
    except Exception as e:
        print(f"❌ 获取页面内容失败: {e}")
    
    # 保持浏览器打开让用户查看
    print(f"\n🔍 浏览器将保持打开30秒，请查看页面状态...")
    print("💡 您可以在浏览器中手动检查 Turnstile 验证码的状态")
    time.sleep(30)
    
    return {
        "url": current_url,
        "title": title,
        "success": "register" in current_url.lower()
    }

if __name__ == "__main__":
    print("=" * 50)
    print("🤖 简化版 DDAI Turnstile 测试")
    print("=" * 50)
    print("🎯 这个工具将:")
    print("   1. 使用 Cloudflare 绕过模式访问 DDAI")
    print("   2. 显示页面内容")
    print("   3. 保持浏览器打开供您查看")
    print()
    
    result = test_ddai_access()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   最终URL: {result['url']}")
    print(f"   页面标题: {result['title']}")
    print(f"   访问状态: {'✅ 成功' if result['success'] else '⚠️ 需要检查'}")
    print("=" * 50)
