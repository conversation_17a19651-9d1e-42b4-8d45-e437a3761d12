#!/usr/bin/env python3
"""
增强版 DDAI Turnstile 测试工具
增加了更好的错误处理和重试机制
"""

from botasaurus.browser import browser, Driver
import time

@browser(
    headless=False, 
    cache=False, 
    max_retry=2,
    wait_for_complete_page_load=False,  # 不等待完整页面加载
    block_images_and_css=True,  # 阻止图片和CSS加载以提高速度
)
def robust_ddai_test(driver: Driver, data):
    """
    增强版 DDAI 网站访问测试
    """
    url = "https://app.ddai.space/register"
    
    print(f"🚀 访问 DDAI 注册页面: {url}")
    print("🔧 使用增强配置：阻止图片/CSS加载，不等待完整页面加载")
    
    try:
        # 使用 Cloudflare 绕过模式访问
        print("🛡️ 启动 Cloudflare 绕过模式...")
        driver.google_get(url, bypass_cloudflare=True)
        
        print("⏳ 等待页面基本加载...")
        time.sleep(8)  # 等待8秒让页面基本加载
        
        # 获取基本信息
        current_url = driver.current_url
        title = driver.title
        
        print(f"📍 当前URL: {current_url}")
        print(f"📄 页面标题: {title}")
        
        # 检查是否成功到达目标页面
        success = "ddai.space" in current_url.lower() and "register" in current_url.lower()
        
        if success:
            print("✅ 成功到达 DDAI 注册页面！")
            
            # 尝试获取页面文本内容
            try:
                body_text = driver.get_text('body')
                if body_text:
                    print(f"📝 页面内容预览 (前300字符):")
                    print(body_text[:300])
                    print("...")
                    
                    # 检查关键词
                    has_register = "register" in body_text.lower()
                    has_email = "email" in body_text.lower()
                    has_password = "password" in body_text.lower()
                    has_turnstile = "turnstile" in body_text.lower() or "cloudflare" in body_text.lower()
                    
                    print(f"\n🔍 页面内容分析:")
                    print(f"   注册相关内容: {'✅' if has_register else '❌'}")
                    print(f"   邮箱字段: {'✅' if has_email else '❌'}")
                    print(f"   密码字段: {'✅' if has_password else '❌'}")
                    print(f"   Turnstile/Cloudflare: {'⚠️ 检测到' if has_turnstile else '✅ 未检测到'}")
                    
                else:
                    print("⚠️ 无法获取页面文本内容")
                    
            except Exception as e:
                print(f"⚠️ 获取页面内容时出错: {e}")
        else:
            print("⚠️ 未能到达预期的注册页面")
        
        # 保持浏览器打开让用户查看
        print(f"\n🔍 浏览器将保持打开20秒，请查看页面状态...")
        print("💡 您可以在浏览器中手动检查 Turnstile 验证码的状态")
        time.sleep(20)
        
        return {
            "url": current_url,
            "title": title,
            "success": success,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
    except Exception as e:
        print(f"❌ 访问过程中出现错误: {e}")
        print("🔄 这可能是由于网络连接问题或网站临时不可用")
        
        return {
            "url": "访问失败",
            "title": "访问失败", 
            "success": False,
            "error": str(e),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

def main():
    """主函数"""
    print("=" * 60)
    print("🤖 增强版 DDAI Turnstile 测试工具")
    print("=" * 60)
    print("🎯 目标: https://app.ddai.space/register")
    print("🔧 特性: 增强错误处理、快速加载、稳定连接")
    print()
    
    try:
        result = robust_ddai_test()
        
        print("\n" + "=" * 60)
        print("📊 测试结果:")
        print("=" * 60)
        print(f"🕐 测试时间: {result.get('timestamp', 'N/A')}")
        print(f"🌐 最终URL: {result.get('url', 'N/A')}")
        print(f"📄 页面标题: {result.get('title', 'N/A')}")
        print(f"✅ 访问状态: {'🎉 成功' if result.get('success') else '❌ 失败'}")
        
        if result.get('error'):
            print(f"❌ 错误信息: {result.get('error')}")
        
        print("=" * 60)
        
        if result.get('success'):
            print("\n🎉 恭喜！成功绕过 Cloudflare 并访问 DDAI 注册页面！")
            print("💡 您现在可以在浏览器中完成注册流程")
        else:
            print("\n⚠️ 这次访问遇到了问题，可能的原因:")
            print("   1. 网络连接不稳定")
            print("   2. 网站临时维护")
            print("   3. Cloudflare 规则更新")
            print("   4. 可以稍后重试")
            
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        print("🔄 建议重新运行程序")

if __name__ == "__main__":
    main()
