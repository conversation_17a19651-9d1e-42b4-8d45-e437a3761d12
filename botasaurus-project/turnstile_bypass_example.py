from botasaurus.browser import browser, Driver

@browser
def bypass_cloudflare_turnstile(driver: Driver, data):
    """
    示例：绕过 Cloudflare Turnstile 验证码
    这是 Botasaurus 的核心功能之一
    """
    # 访问 Cloudflare Turnstile 测试页面，启用绕过功能
    driver.google_get("https://nopecha.com/demo/cloudflare", bypass_cloudflare=True)
    
    # 等待页面完全加载
    driver.wait(5)
    
    # 获取页面信息
    title = driver.title
    current_url = driver.current_url
    
    # 尝试获取页面内容
    try:
        page_text = driver.get_text('body')[:500]  # 获取前500个字符
    except:
        page_text = "无法获取页面内容"
    
    return {
        "title": title,
        "url": current_url,
        "page_content_preview": page_text,
        "success": "cloudflare" not in current_url.lower() or "challenge" not in page_text.lower()
    }

# 运行任务
if __name__ == "__main__":
    print("正在测试 Cloudflare Turnstile 绕过功能...")
    result = bypass_cloudflare_turnstile()
    print("结果:", result)
