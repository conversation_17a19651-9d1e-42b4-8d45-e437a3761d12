#!/usr/bin/env python3
"""
优化版 DDAI Turnstile 测试工具
简洁高效，专注核心功能
"""

from botasaurus.browser import browser, Driver
import time

@browser(
    headless=False, 
    cache=False, 
    max_retry=2,
    wait_for_complete_page_load=False,
    block_images_and_css=True,
)
def clean_ddai_test(driver: Driver, data):
    """
    优化版 DDAI 网站访问测试
    """
    url = "https://app.ddai.space/register"
    
    print(f"🚀 访问 DDAI 注册页面: {url}")
    print("🔧 使用优化配置：快速加载，专注核心功能")
    
    try:
        # 第一阶段：启动 Cloudflare 绕过模式
        print("\n📍 第一阶段：启动 Cloudflare 绕过模式")
        driver.google_get(url, bypass_cloudflare=True)
        
        # 第二阶段：初始页面加载检查
        print("\n📍 第二阶段：初始页面加载检查")
        time.sleep(5)
        
        current_url = driver.current_url
        title = driver.title
        print(f"   当前URL: {current_url}")
        print(f"   页面标题: {title}")
        
        # 第三阶段：等待页面完全加载
        print("\n📍 第三阶段：等待页面完全加载")
        print("⏳ 等待10秒让页面完全加载...")
        time.sleep(10)
        
        # 第四阶段：最终状态确认
        print(f"\n📍 第四阶段：最终状态确认")
        final_url = driver.current_url
        final_title = driver.title
        
        print(f"   最终URL: {final_url}")
        print(f"   最终标题: {final_title}")
        
        # 获取页面内容
        try:
            body_text = driver.get_text('body')
            if body_text:
                print(f"\n📝 页面内容预览 (前400字符):")
                print(body_text[:400])
                print("...")
                
                # 简化的内容分析
                has_register = "register" in body_text.lower()
                has_email = "email" in body_text.lower()
                has_password = "password" in body_text.lower()
                has_cloudflare_text = "cloudflare" in body_text.lower()
                has_turnstile_text = "turnstile" in body_text.lower()
                
                print(f"\n🔍 页面内容分析:")
                print(f"   注册相关内容: {'✅' if has_register else '❌'}")
                print(f"   邮箱字段: {'✅' if has_email else '❌'}")
                print(f"   密码字段: {'✅' if has_password else '❌'}")
                print(f"   Cloudflare 文本: {'⚠️' if has_cloudflare_text else '✅'}")
                print(f"   Turnstile 文本: {'⚠️' if has_turnstile_text else '✅'}")
                
                # 判断整体状态
                success_indicators = has_register and has_email
                cf_indicators = has_cloudflare_text or has_turnstile_text
                
                if success_indicators and not cf_indicators:
                    print("\n🎉 状态评估: ✅ 成功绕过，页面完全可用")
                elif success_indicators:
                    print("\n✅ 状态评估: 🔄 基本成功，可能有轻微验证残留")
                else:
                    print("\n⚠️ 状态评估: 🤔 页面状态需要进一步确认")
                    
            else:
                print("⚠️ 无法获取页面文本内容")
                
        except Exception as e:
            print(f"❌ 获取页面内容失败: {e}")
        
        # 第五阶段：用户观察时间
        print(f"\n📍 第五阶段：用户观察时间")
        print("🔍 浏览器将保持打开60秒，供您观察和操作...")
        print("💡 您可以在浏览器中:")
        print("   1. 查看是否有验证码")
        print("   2. 尝试填写注册信息")
        print("   3. 测试页面功能")
        
        # 倒计时显示
        for remaining in range(60, 0, -15):
            print(f"   ⏰ 剩余时间: {remaining} 秒...")
            time.sleep(15)
        
        print("✅ 观察时间结束")
        
        return {
            "url": final_url,
            "title": final_title,
            "success": "ddai.space" in final_url.lower() and "register" in final_url.lower(),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
    except Exception as e:
        print(f"❌ 访问过程中出现错误: {e}")
        return {
            "url": "访问失败",
            "title": "访问失败", 
            "success": False,
            "error": str(e),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

def main():
    """主函数"""
    print("=" * 60)
    print("🤖 优化版 DDAI Turnstile 绕过工具")
    print("=" * 60)
    print("🎯 目标: https://app.ddai.space/register")
    print("🔧 特性: 简洁高效，专注核心功能")
    print("⏰ 预计时间: 约1.5分钟")
    print()
    
    try:
        result = clean_ddai_test()
        
        print("\n" + "=" * 60)
        print("📊 最终测试结果:")
        print("=" * 60)
        print(f"🕐 测试时间: {result.get('timestamp', 'N/A')}")
        print(f"🌐 最终URL: {result.get('url', 'N/A')}")
        print(f"📄 页面标题: {result.get('title', 'N/A')}")
        print(f"✅ 访问状态: {'🎉 成功' if result.get('success') else '❌ 失败'}")
        
        if result.get('error'):
            print(f"❌ 错误信息: {result.get('error')}")
        
        print("=" * 60)
        
        if result.get('success'):
            print("\n🎉 测试完成！Cloudflare 绕过成功")
            print("💡 您现在可以在 DDAI 网站上进行注册操作")
        else:
            print("\n⚠️ 测试遇到问题，请检查网络连接或稍后重试")
            
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
