from botasaurus.browser import browser, Driver

@browser
def scrape_cloudflare_protected_site(driver: Driver, data):
    """
    示例：访问受 Cloudflare 保护的网站
    """
    # 使用 google_get 方法访问受保护的网站
    driver.google_get("https://www.cloudflare.com/en-in/")
    
    # 等待页面加载
    driver.wait(3)
    
    # 获取页面标题
    title = driver.title
    
    # 尝试获取主要标题
    try:
        heading = driver.get_text('h1')
    except:
        heading = "无法获取标题"
    
    return {
        "title": title,
        "heading": heading,
        "url": driver.current_url
    }

# 运行任务
if __name__ == "__main__":
    result = scrape_cloudflare_protected_site()
    print("结果:", result)
