#!/usr/bin/env python3
"""
幻影流量系统 - 系统验证脚本
验证系统的完整性和功能正确性
"""

import sys
import os
import asyncio
import tempfile
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


class SystemValidator:
    """系统验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.project_root = Path(__file__).parent
        self.validation_results = {
            "start_time": time.time(),
            "checks": {},
            "errors": [],
            "warnings": []
        }
    
    def validate_project_structure(self):
        """验证项目结构"""
        print("📁 验证项目结构...")
        
        required_files = [
            "phantom_traffic_system/__init__.py",
            "phantom_traffic_system/main.py",
            "phantom_traffic_system/core/__init__.py",
            "phantom_traffic_system/core/phantom_system.py",
            "phantom_traffic_system/core/config_manager.py",
            "phantom_traffic_system/core/logger.py",
            "phantom_traffic_system/core/exceptions.py",
            "phantom_traffic_system/modules/proxy_manager/__init__.py",
            "phantom_traffic_system/modules/fingerprint_engine/__init__.py",
            "phantom_traffic_system/modules/behavior_simulator/__init__.py",
            "phantom_traffic_system/modules/session_scheduler/__init__.py",
            "phantom_traffic_system/modules/browser_controller/__init__.py",
            "phantom_traffic_system/api/__init__.py",
            "phantom_traffic_system/config/default_config.yaml",
            "requirements.txt",
            "setup.py",
            "README.md"
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
        
        if missing_files:
            self.validation_results["errors"].extend(missing_files)
            print(f"❌ 缺失文件: {len(missing_files)} 个")
            for file_path in missing_files:
                print(f"  • {file_path}")
            return False
        else:
            print("✅ 项目结构完整")
            self.validation_results["checks"]["project_structure"] = True
            return True
    
    def validate_imports(self):
        """验证模块导入"""
        print("\n📦 验证模块导入...")
        
        import_tests = [
            ("核心系统", "phantom_traffic_system.core", "PhantomTrafficSystem"),
            ("配置管理", "phantom_traffic_system.core", "ConfigManager"),
            ("日志系统", "phantom_traffic_system.core", "phantom_logger"),
            ("异常定义", "phantom_traffic_system.core", "PhantomTrafficError"),
            ("代理管理", "phantom_traffic_system.modules.proxy_manager", "ProxyManager"),
            ("指纹引擎", "phantom_traffic_system.modules.fingerprint_engine", "FingerprintEngine"),
            ("行为模拟", "phantom_traffic_system.modules.behavior_simulator", "BehaviorSimulator"),
            ("会话调度", "phantom_traffic_system.modules.session_scheduler", "SessionScheduler"),
            ("浏览器控制", "phantom_traffic_system.modules.browser_controller", "BrowserController"),
            ("API服务", "phantom_traffic_system.api", "APIServer")
        ]
        
        failed_imports = []
        
        for name, module_path, class_name in import_tests:
            try:
                module = __import__(module_path, fromlist=[class_name])
                getattr(module, class_name)
                print(f"  ✅ {name}")
            except ImportError as e:
                failed_imports.append((name, str(e)))
                print(f"  ❌ {name}: {e}")
            except AttributeError as e:
                failed_imports.append((name, str(e)))
                print(f"  ❌ {name}: {e}")
        
        if failed_imports:
            self.validation_results["errors"].extend([f"{name}: {error}" for name, error in failed_imports])
            return False
        else:
            print("✅ 所有模块导入成功")
            self.validation_results["checks"]["imports"] = True
            return True
    
    def validate_configuration(self):
        """验证配置系统"""
        print("\n⚙️ 验证配置系统...")
        
        try:
            from phantom_traffic_system.core import ConfigManager
            
            # 测试默认配置加载
            config_manager = ConfigManager()
            config = config_manager.get_config()
            
            if not isinstance(config, dict):
                self.validation_results["errors"].append("配置不是字典类型")
                print("❌ 配置格式错误")
                return False
            
            # 检查必要的配置项
            required_sections = [
                "proxy_manager",
                "fingerprint_engine", 
                "behavior_simulator",
                "session_scheduler",
                "browser_controller"
            ]
            
            missing_sections = []
            for section in required_sections:
                if section not in config:
                    missing_sections.append(section)
            
            if missing_sections:
                self.validation_results["errors"].extend([f"缺失配置节: {s}" for s in missing_sections])
                print(f"❌ 缺失配置节: {missing_sections}")
                return False
            
            print("✅ 配置系统正常")
            self.validation_results["checks"]["configuration"] = True
            return True
            
        except Exception as e:
            self.validation_results["errors"].append(f"配置验证异常: {e}")
            print(f"❌ 配置验证失败: {e}")
            return False
    
    async def validate_system_initialization(self):
        """验证系统初始化"""
        print("\n🚀 验证系统初始化...")
        
        try:
            from phantom_traffic_system.core import PhantomTrafficSystem
            
            # 创建临时配置
            test_config = {
                "proxy_manager": {"enabled": True, "sources": []},
                "fingerprint_engine": {"enabled": True},
                "behavior_simulator": {"enabled": True},
                "session_scheduler": {"enabled": True},
                "browser_controller": {"enabled": True},
                "api_server": {"enabled": False}
            }
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(test_config, f)
                config_path = f.name
            
            try:
                # 初始化系统
                system = PhantomTrafficSystem(config_path)
                
                # 验证各个组件
                if not hasattr(system, 'proxy_manager'):
                    raise Exception("代理管理器未初始化")
                if not hasattr(system, 'fingerprint_engine'):
                    raise Exception("指纹引擎未初始化")
                if not hasattr(system, 'behavior_simulator'):
                    raise Exception("行为模拟器未初始化")
                if not hasattr(system, 'session_scheduler'):
                    raise Exception("会话调度器未初始化")
                if not hasattr(system, 'browser_controller'):
                    raise Exception("浏览器控制器未初始化")
                
                print("✅ 系统初始化成功")
                self.validation_results["checks"]["system_initialization"] = True
                return True
                
            finally:
                os.unlink(config_path)
                
        except Exception as e:
            self.validation_results["errors"].append(f"系统初始化异常: {e}")
            print(f"❌ 系统初始化失败: {e}")
            return False
    
    async def validate_module_functionality(self):
        """验证模块功能"""
        print("\n🔧 验证模块功能...")
        
        try:
            from phantom_traffic_system.core import PhantomTrafficSystem
            
            # 创建测试配置
            test_config = {
                "proxy_manager": {"enabled": True, "sources": []},
                "fingerprint_engine": {"enabled": True},
                "behavior_simulator": {"enabled": True},
                "session_scheduler": {"enabled": True},
                "browser_controller": {"enabled": True},
                "api_server": {"enabled": False}
            }
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(test_config, f)
                config_path = f.name
            
            try:
                system = PhantomTrafficSystem(config_path)
                
                # 测试指纹生成
                print("  🎭 测试指纹生成...")
                fingerprint = system.fingerprint_engine.generate_complete_fingerprint()
                if not fingerprint:
                    raise Exception("指纹生成失败")
                print("    ✅ 指纹生成正常")
                
                # 测试行为模拟
                print("  🤖 测试行为模拟...")
                behavior_profile = system.behavior_simulator.start_session()
                if not behavior_profile:
                    raise Exception("行为模拟失败")
                print("    ✅ 行为模拟正常")
                
                # 测试页面交互序列生成
                print("  📄 测试页面交互序列...")
                page_context = {
                    "url": "https://example.com",
                    "title": "Example",
                    "viewport_size": (1920, 1080),
                    "page_height": 2000
                }
                sequence = system.behavior_simulator.generate_page_interaction_sequence(page_context)
                if not sequence:
                    raise Exception("页面交互序列生成失败")
                print("    ✅ 页面交互序列生成正常")
                
                # 测试系统状态
                print("  📊 测试系统状态...")
                status = system.get_system_status()
                if not isinstance(status, dict):
                    raise Exception("系统状态格式错误")
                print("    ✅ 系统状态正常")
                
                print("✅ 模块功能验证通过")
                self.validation_results["checks"]["module_functionality"] = True
                return True
                
            finally:
                os.unlink(config_path)
                
        except Exception as e:
            self.validation_results["errors"].append(f"模块功能验证异常: {e}")
            print(f"❌ 模块功能验证失败: {e}")
            return False
    
    async def validate_api_system(self):
        """验证API系统"""
        print("\n🌐 验证API系统...")
        
        try:
            from phantom_traffic_system.core import PhantomTrafficSystem
            from phantom_traffic_system.api import APIServer
            
            # 创建测试配置
            test_config = {
                "proxy_manager": {"enabled": True, "sources": []},
                "fingerprint_engine": {"enabled": True},
                "behavior_simulator": {"enabled": True},
                "session_scheduler": {"enabled": True},
                "browser_controller": {"enabled": True},
                "api_server": {"enabled": True, "host": "127.0.0.1", "port": 8083}
            }
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(test_config, f)
                config_path = f.name
            
            try:
                system = PhantomTrafficSystem(config_path)
                api_server = APIServer(system, test_config["api_server"])
                
                # 测试API服务器初始化
                if not api_server:
                    raise Exception("API服务器初始化失败")
                
                # 测试服务器信息
                server_info = api_server.get_server_info()
                if not isinstance(server_info, dict):
                    raise Exception("服务器信息格式错误")
                
                print("✅ API系统验证通过")
                self.validation_results["checks"]["api_system"] = True
                return True
                
            finally:
                os.unlink(config_path)
                
        except Exception as e:
            self.validation_results["errors"].append(f"API系统验证异常: {e}")
            print(f"❌ API系统验证失败: {e}")
            return False
    
    def validate_dependencies(self):
        """验证依赖包"""
        print("\n📚 验证依赖包...")
        
        required_packages = [
            "asyncio",
            "aiohttp", 
            "pyyaml",
            "psutil"
        ]
        
        optional_packages = [
            "playwright",
            "selenium", 
            "numpy",
            "cryptography"
        ]
        
        missing_required = []
        missing_optional = []
        
        # 检查必需包
        for package in required_packages:
            try:
                __import__(package)
                print(f"  ✅ {package}")
            except ImportError:
                missing_required.append(package)
                print(f"  ❌ {package} (必需)")
        
        # 检查可选包
        for package in optional_packages:
            try:
                __import__(package)
                print(f"  ✅ {package}")
            except ImportError:
                missing_optional.append(package)
                print(f"  ⚠️ {package} (可选)")
        
        if missing_required:
            self.validation_results["errors"].extend([f"缺失必需包: {p}" for p in missing_required])
            return False
        
        if missing_optional:
            self.validation_results["warnings"].extend([f"缺失可选包: {p}" for p in missing_optional])
        
        print("✅ 依赖包验证通过")
        self.validation_results["checks"]["dependencies"] = True
        return True
    
    def generate_report(self):
        """生成验证报告"""
        end_time = time.time()
        duration = end_time - self.validation_results["start_time"]
        
        total_checks = len(self.validation_results["checks"])
        passed_checks = sum(1 for result in self.validation_results["checks"].values() if result)
        
        print(f"\n{'='*60}")
        print("🎭 幻影流量系统 - 验证报告")
        print(f"{'='*60}")
        print(f"验证时间: {duration:.2f}s")
        print(f"总检查项: {total_checks}")
        print(f"通过检查: {passed_checks}")
        print(f"失败检查: {total_checks - passed_checks}")
        print(f"错误数量: {len(self.validation_results['errors'])}")
        print(f"警告数量: {len(self.validation_results['warnings'])}")
        
        if self.validation_results["errors"]:
            print(f"\n❌ 错误列表:")
            for error in self.validation_results["errors"]:
                print(f"  • {error}")
        
        if self.validation_results["warnings"]:
            print(f"\n⚠️ 警告列表:")
            for warning in self.validation_results["warnings"]:
                print(f"  • {warning}")
        
        # 总体结果
        overall_success = len(self.validation_results["errors"]) == 0
        
        if overall_success:
            print(f"\n🎉 系统验证通过！所有核心功能正常！")
        else:
            print(f"\n⚠️ 系统验证失败，请修复上述错误")
        
        return overall_success
    
    async def run_full_validation(self):
        """运行完整验证"""
        print("🎭 幻影流量系统 - 系统验证")
        print("="*60)
        
        validation_steps = [
            ("项目结构", self.validate_project_structure),
            ("模块导入", self.validate_imports),
            ("依赖包", self.validate_dependencies),
            ("配置系统", self.validate_configuration),
            ("系统初始化", self.validate_system_initialization),
            ("模块功能", self.validate_module_functionality),
            ("API系统", self.validate_api_system)
        ]
        
        results = []
        
        for step_name, step_func in validation_steps:
            print(f"\n🔍 {step_name}验证...")
            try:
                if asyncio.iscoroutinefunction(step_func):
                    result = await step_func()
                else:
                    result = step_func()
                results.append(result)
            except Exception as e:
                print(f"❌ {step_name}验证异常: {e}")
                self.validation_results["errors"].append(f"{step_name}验证异常: {e}")
                results.append(False)
        
        # 生成报告
        overall_success = self.generate_report()
        
        return overall_success


async def main():
    """主函数"""
    validator = SystemValidator()
    success = await validator.run_full_validation()
    
    # 退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
