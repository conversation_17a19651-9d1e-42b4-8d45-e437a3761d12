#!/usr/bin/env python3
"""
幻影流量系统 - 简化测试运行器
不依赖pytest，直接运行测试函数
"""

import sys
import os
import asyncio
import time
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


class SimpleTestRunner:
    """简化测试运行器"""
    
    def __init__(self):
        """初始化测试运行器"""
        self.results = {
            "total": 0,
            "passed": 0,
            "failed": 0,
            "errors": []
        }
    
    def run_test(self, test_name, test_func):
        """运行单个测试"""
        self.results["total"] += 1
        
        try:
            print(f"  🧪 {test_name}...", end=" ")
            
            # 如果是异步函数，使用asyncio运行
            if asyncio.iscoroutinefunction(test_func):
                asyncio.run(test_func())
            else:
                test_func()
            
            print("✅")
            self.results["passed"] += 1
            return True
            
        except Exception as e:
            print("❌")
            error_msg = f"{test_name}: {str(e)}"
            self.results["errors"].append(error_msg)
            self.results["failed"] += 1
            print(f"    错误: {e}")
            return False
    
    def run_basic_tests(self):
        """运行基础测试"""
        print("\n📦 基础功能测试")
        print("-" * 40)
        
        def test_module_imports():
            """测试模块导入"""
            from phantom_traffic_system.core import PhantomTrafficSystem, ConfigManager, phantom_logger
            from phantom_traffic_system.modules.proxy_manager import ProxyManager
            from phantom_traffic_system.modules.fingerprint_engine import FingerprintEngine
            from phantom_traffic_system.modules.behavior_simulator import BehaviorSimulator
            from phantom_traffic_system.modules.session_scheduler import SessionScheduler
            from phantom_traffic_system.modules.browser_controller import BrowserController
            
            assert PhantomTrafficSystem is not None
            assert ConfigManager is not None
            assert phantom_logger is not None
        
        def test_config_manager():
            """测试配置管理器"""
            from phantom_traffic_system.core import ConfigManager
            
            config_manager = ConfigManager()
            config = config_manager.get_config()
            
            assert isinstance(config, dict)
            assert "proxy_manager" in config
            assert "fingerprint_engine" in config
        
        def test_system_initialization():
            """测试系统初始化"""
            from phantom_traffic_system.core import PhantomTrafficSystem
            
            system = PhantomTrafficSystem()
            
            assert hasattr(system, 'proxy_manager')
            assert hasattr(system, 'fingerprint_engine')
            assert hasattr(system, 'behavior_simulator')
        
        def test_fingerprint_generation():
            """测试指纹生成"""
            from phantom_traffic_system.core import PhantomTrafficSystem
            
            system = PhantomTrafficSystem()
            fingerprint = system.fingerprint_engine.generate_complete_fingerprint()
            
            assert fingerprint is not None
            assert hasattr(fingerprint, 'device_persona')
        
        def test_behavior_simulation():
            """测试行为模拟"""
            from phantom_traffic_system.core import PhantomTrafficSystem
            
            system = PhantomTrafficSystem()
            behavior_profile = system.behavior_simulator.start_session()
            
            assert behavior_profile is not None
            assert hasattr(behavior_profile, 'pattern')
        
        async def test_health_check():
            """测试健康检查"""
            from phantom_traffic_system.core import PhantomTrafficSystem
            
            system = PhantomTrafficSystem()
            health = await system.health_check()
            
            assert isinstance(health, dict)
            assert "overall_health" in health
        
        def test_url_management():
            """测试URL管理"""
            from phantom_traffic_system.core import PhantomTrafficSystem
            
            system = PhantomTrafficSystem()
            test_url = "https://example.com"
            
            system.add_target_url(test_url)
            assert test_url in system.session_scheduler.target_urls
            
            system.remove_target_url(test_url)
            assert test_url not in system.session_scheduler.target_urls
        
        # 运行测试
        tests = [
            ("模块导入", test_module_imports),
            ("配置管理器", test_config_manager),
            ("系统初始化", test_system_initialization),
            ("指纹生成", test_fingerprint_generation),
            ("行为模拟", test_behavior_simulation),
            ("健康检查", test_health_check),
            ("URL管理", test_url_management)
        ]
        
        for test_name, test_func in tests:
            self.run_test(test_name, test_func)
    
    def run_integration_tests(self):
        """运行集成测试"""
        print("\n🔗 集成测试")
        print("-" * 40)
        
        async def test_session_workflow():
            """测试会话工作流程"""
            from phantom_traffic_system.core import PhantomTrafficSystem
            
            system = PhantomTrafficSystem()
            
            # 添加目标URL
            system.add_target_url("https://example.com")
            
            # 启动系统
            await system.start()
            
            # 创建会话
            session_id = await system.create_session()
            assert session_id is not None
            
            # 获取会话信息
            session_info = system.get_session_info(session_id)
            assert session_info is not None
            
            # 结束会话
            result = await system.end_session(session_id)
            assert result is True
        
        def test_page_interaction_sequence():
            """测试页面交互序列"""
            from phantom_traffic_system.core import PhantomTrafficSystem
            
            system = PhantomTrafficSystem()
            page_context = {
                "url": "https://example.com",
                "title": "Example",
                "viewport_size": (1920, 1080),
                "page_height": 2000
            }
            sequence = system.behavior_simulator.generate_page_interaction_sequence(page_context)
            
            assert sequence is not None
            assert hasattr(sequence, 'actions')
            assert len(sequence.actions) > 0
        
        def test_traffic_patterns():
            """测试流量模式"""
            from phantom_traffic_system.core import PhantomTrafficSystem
            
            system = PhantomTrafficSystem()
            
            patterns = ["flat", "realistic", "burst"]
            for pattern in patterns:
                system.set_traffic_pattern(pattern)
        
        # 运行测试
        tests = [
            ("会话工作流程", test_session_workflow),
            ("页面交互序列", test_page_interaction_sequence),
            ("流量模式", test_traffic_patterns)
        ]
        
        for test_name, test_func in tests:
            self.run_test(test_name, test_func)
    
    def run_performance_tests(self):
        """运行性能测试"""
        print("\n⚡ 性能测试")
        print("-" * 40)
        
        def test_fingerprint_performance():
            """测试指纹生成性能"""
            from phantom_traffic_system.core import PhantomTrafficSystem
            
            system = PhantomTrafficSystem()
            
            start_time = time.time()
            for _ in range(10):
                system.fingerprint_engine.generate_complete_fingerprint()
            end_time = time.time()
            
            avg_time = (end_time - start_time) / 10
            print(f"    平均指纹生成时间: {avg_time*1000:.2f}ms")
            
            # 性能要求：每次生成应该在100ms以内
            assert avg_time < 0.1
        
        def test_behavior_performance():
            """测试行为模拟性能"""
            from phantom_traffic_system.core import PhantomTrafficSystem
            
            system = PhantomTrafficSystem()
            page_context = {
                "url": "https://example.com",
                "title": "Example",
                "viewport_size": (1920, 1080),
                "page_height": 2000
            }
            
            start_time = time.time()
            for _ in range(5):
                system.behavior_simulator.generate_page_interaction_sequence(page_context)
            end_time = time.time()
            
            avg_time = (end_time - start_time) / 5
            print(f"    平均行为序列生成时间: {avg_time*1000:.2f}ms")
            
            # 性能要求：每次生成应该在50ms以内
            assert avg_time < 0.05
        
        # 运行测试
        tests = [
            ("指纹生成性能", test_fingerprint_performance),
            ("行为模拟性能", test_behavior_performance)
        ]
        
        for test_name, test_func in tests:
            self.run_test(test_name, test_func)
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("🎭 幻影流量系统 - 测试报告")
        print("="*60)
        
        success_rate = (self.results["passed"] / self.results["total"] * 100) if self.results["total"] > 0 else 0
        
        print(f"总测试数: {self.results['total']}")
        print(f"通过测试: {self.results['passed']}")
        print(f"失败测试: {self.results['failed']}")
        print(f"成功率: {success_rate:.1f}%")
        
        if self.results["errors"]:
            print(f"\n❌ 失败的测试:")
            for error in self.results["errors"]:
                print(f"  • {error}")
        
        if success_rate == 100:
            print("\n🎉 所有测试通过！系统功能正常！")
        elif success_rate >= 80:
            print("\n✅ 大部分测试通过，系统基本正常")
        else:
            print("\n⚠️ 多个测试失败，需要检查系统")
        
        return success_rate >= 80
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🎭 幻影流量系统 - 简化测试套件")
        print("="*60)
        
        start_time = time.time()
        
        # 运行各种测试
        self.run_basic_tests()
        self.run_integration_tests()
        self.run_performance_tests()
        
        # 生成报告
        success = self.generate_report()
        
        total_time = time.time() - start_time
        print(f"\n⏱️ 总测试时间: {total_time:.2f}s")
        
        return success


def main():
    """主函数"""
    runner = SimpleTestRunner()
    success = runner.run_all_tests()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
