#!/usr/bin/env python3
"""
幻影流量系统 - 快速启动脚本
演示系统的基本功能和使用方法
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


async def demo_basic_functionality():
    """演示基本功能"""
    print("🎭 幻影流量系统 - 快速启动演示")
    print("="*60)
    
    try:
        # 导入核心模块
        from phantom_traffic_system.core import PhantomTrafficSystem, phantom_logger
        
        print("📦 正在初始化系统...")
        
        # 创建系统实例
        system = PhantomTrafficSystem()
        
        print("✅ 系统初始化成功")
        
        # 演示指纹生成
        print("\n🎭 演示指纹生成...")
        fingerprint = system.fingerprint_engine.generate_complete_fingerprint()
        print(f"  设备类型: {fingerprint.device_persona.device_type}")
        print(f"  操作系统: {fingerprint.device_persona.os_name}")
        print(f"  浏览器版本: {fingerprint.device_persona.browser_version}")
        print(f"  连接类型: {fingerprint.connection_type}")
        print(f"  网络速度: {fingerprint.downlink:.1f} Mbps")
        
        # 演示行为模拟
        print("\n🤖 演示行为模拟...")
        behavior_profile = system.behavior_simulator.start_session()
        print(f"  行为模式: {behavior_profile.pattern}")
        print(f"  鼠标速度范围: {behavior_profile.mouse_speed_range}")
        print(f"  页面停留时间: {behavior_profile.page_load_wait_range}")
        print(f"  注意力持续时间: {behavior_profile.attention_span}秒")
        
        # 演示页面交互序列
        print("\n📄 演示页面交互序列生成...")
        page_context = {
            "url": "https://example.com",
            "title": "Example Domain",
            "viewport_size": (1920, 1080),
            "page_height": 2000
        }
        sequence = system.behavior_simulator.generate_page_interaction_sequence(page_context)
        print(f"  交互动作数量: {len(sequence.actions)}")
        print(f"  预计总时间: {sequence.total_duration:.2f}秒")
        
        # 演示目标URL管理
        print("\n🎯 演示目标URL管理...")
        test_urls = [
            "https://example.com",
            "https://httpbin.org/get",
            "https://jsonplaceholder.typicode.com/posts/1"
        ]
        
        for url in test_urls:
            system.add_target_url(url)
            print(f"  添加目标: {url}")
        
        print(f"  当前目标数量: {len(system.session_scheduler.target_urls)}")
        
        # 演示流量模式设置
        print("\n🔄 演示流量模式设置...")
        patterns = ["flat", "realistic", "burst"]
        for pattern in patterns:
            try:
                system.set_traffic_pattern(pattern)
                print(f"  设置模式: {pattern} ✅")
            except Exception as e:
                print(f"  设置模式: {pattern} ❌ ({e})")
        
        # 演示系统状态
        print("\n📊 演示系统状态...")
        status = system.get_system_status()
        print(f"  系统运行状态: {'运行中' if status['is_running'] else '已停止'}")
        print(f"  活跃会话数: {status['active_sessions']}")
        print(f"  代理池状态: {status['proxy_pool_status']}")
        
        # 演示健康检查
        print("\n🏥 演示健康检查...")
        health = await system.health_check()
        print(f"  整体健康状态: {health.get('overall_health', 'unknown')}")
        print(f"  各模块状态:")
        checks = health.get('checks', {})
        if checks:
            for module, check_result in checks.items():
                if isinstance(check_result, dict):
                    status = check_result.get('status', 'unknown')
                    status_icon = "✅" if status == "healthy" else "⚠️" if status == "warning" else "❌"
                    print(f"    {module}: {status_icon} ({status})")
                else:
                    print(f"    {module}: ✅")
        else:
            print("    所有模块: ✅ (基础功能正常)")
        
        print("\n🎉 演示完成！系统功能正常")
        return True
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        return False


async def demo_session_workflow():
    """演示完整的会话工作流程"""
    print("\n" + "="*60)
    print("🔄 演示完整会话工作流程")
    print("="*60)
    
    try:
        from phantom_traffic_system.core import PhantomTrafficSystem
        
        system = PhantomTrafficSystem()
        
        # 1. 配置目标
        print("1️⃣ 配置目标URL...")
        system.add_target_url("https://httpbin.org/get")
        
        # 2. 启动系统
        print("2️⃣ 启动系统...")
        await system.start()

        # 3. 设置流量模式
        print("3️⃣ 设置流量模式...")
        system.set_traffic_pattern("realistic")

        # 4. 创建会话
        print("4️⃣ 创建新会话...")
        session_id = await system.create_session()
        print(f"   会话ID: {session_id}")

        # 5. 获取会话信息
        print("5️⃣ 获取会话信息...")
        session_info = system.get_session_info(session_id)
        if session_info:
            print(f"   状态: {session_info['status']}")
            print(f"   目标: {session_info['target_url']}")
            print(f"   创建时间: {session_info['created_at']}")

        # 6. 模拟会话执行
        print("6️⃣ 模拟会话执行...")
        print("   [模拟] 正在访问目标页面...")
        print("   [模拟] 正在执行页面交互...")
        print("   [模拟] 正在收集数据...")

        # 7. 结束会话
        print("7️⃣ 结束会话...")
        await system.end_session(session_id)
        print("   会话已结束")
        
        print("\n✅ 会话工作流程演示完成")
        return True
        
    except Exception as e:
        print(f"\n❌ 会话工作流程演示失败: {e}")
        return False


def show_system_info():
    """显示系统信息"""
    print("\n" + "="*60)
    print("📋 系统信息")
    print("="*60)
    
    print("项目名称: 幻影流量系统 (Phantom Traffic System)")
    print("版本: 1.0.0")
    print("作者: Phantom Team")
    print("Python版本:", sys.version.split()[0])
    
    # 检查依赖
    print("\n📦 依赖检查:")
    dependencies = [
        ("aiohttp", "Web框架"),
        ("yaml", "配置管理"),
        ("psutil", "系统监控"),
        ("playwright", "浏览器自动化（可选）"),
        ("numpy", "数值计算（可选）")
    ]
    
    for dep, desc in dependencies:
        try:
            __import__(dep.replace("-", "_"))
            print(f"  ✅ {dep} - {desc}")
        except ImportError:
            print(f"  ❌ {dep} - {desc} (未安装)")
    
    print("\n📁 项目结构:")
    project_files = [
        "phantom_traffic_system/",
        "tests/",
        "requirements.txt",
        "setup.py",
        "README.md",
        "SYSTEM_STATUS.md"
    ]
    
    for file_path in project_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")


async def main():
    """主函数"""
    print("🚀 启动幻影流量系统演示...")
    
    # 显示系统信息
    show_system_info()
    
    # 演示基本功能
    basic_success = await demo_basic_functionality()
    
    # 演示会话工作流程
    workflow_success = await demo_session_workflow()
    
    # 总结
    print("\n" + "="*60)
    print("📊 演示总结")
    print("="*60)

    # 计算成功率
    total_demos = 2
    success_count = (1 if basic_success else 0) + (1 if workflow_success else 0)
    success_rate = (success_count / total_demos) * 100

    print(f"演示成功率: {success_rate:.0f}% ({success_count}/{total_demos})")

    if success_rate >= 100:
        print("🎉 所有演示成功完成！")
        print("✅ 系统核心功能正常")
        print("✅ 会话工作流程正常")
        print("\n🚀 系统已准备就绪，可以开始使用！")
    elif success_rate >= 50:
        print("⚠️ 大部分功能正常，系统基本可用")
        if basic_success:
            print("✅ 系统核心功能正常")
        else:
            print("❌ 系统核心功能异常")
        if workflow_success:
            print("✅ 会话工作流程正常")
        else:
            print("❌ 会话工作流程异常")
        print("\n💡 建议：检查警告信息，但系统可以正常使用")
    else:
        print("❌ 演示过程中遇到问题")
        print("请检查系统配置和依赖")
    
    print("\n📖 更多信息请查看:")
    print("  - README.md: 项目说明")
    print("  - SYSTEM_STATUS.md: 系统状态报告")
    print("  - test_core_integration.py: 核心功能测试")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 演示已中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出现未预期的错误: {e}")
        sys.exit(1)
