#!/usr/bin/env python3
"""
幻影流量系统 - 测试依赖安装脚本
自动安装测试所需的依赖包
"""

import sys
import subprocess
import importlib

def check_package(package_name):
    """检查包是否已安装"""
    try:
        importlib.import_module(package_name.replace("-", "_"))
        return True
    except ImportError:
        return False

def install_package(package_name):
    """安装包"""
    try:
        print(f"正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {package_name} 安装异常: {e}")
        return False

def main():
    """主函数"""
    print("🎭 幻影流量系统 - 测试依赖安装")
    print("="*50)
    
    # 测试依赖列表
    test_dependencies = [
        "pytest",
        "pytest-asyncio",
        "aiohttp-cors",
        "jinja2"
    ]
    
    # 检查并安装依赖
    installed_count = 0
    total_count = len(test_dependencies)
    
    for package in test_dependencies:
        print(f"\n📦 检查 {package}...")
        
        if check_package(package):
            print(f"✅ {package} 已安装")
            installed_count += 1
        else:
            print(f"❌ {package} 未安装")
            if install_package(package):
                installed_count += 1
    
    # 总结
    print(f"\n{'='*50}")
    print("📊 安装总结")
    print(f"{'='*50}")
    print(f"总依赖数: {total_count}")
    print(f"已安装: {installed_count}")
    print(f"成功率: {(installed_count/total_count*100):.1f}%")
    
    if installed_count == total_count:
        print("\n🎉 所有测试依赖安装完成！")
        print("现在可以运行完整的测试套件了")
        return True
    else:
        print(f"\n⚠️ {total_count - installed_count} 个依赖安装失败")
        print("请手动安装缺失的依赖")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
