#!/usr/bin/env python3
"""
幻影流量系统 - 核心集成测试
测试核心功能的集成和工作流程
"""

import sys
import os
import asyncio
import tempfile
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


async def test_core_integration():
    """测试核心集成功能"""
    print("🎭 幻影流量系统 - 核心集成测试")
    print("="*60)
    
    success_count = 0
    total_tests = 0
    
    # 测试1: 模块导入
    print("\n📦 测试模块导入...")
    total_tests += 1
    try:
        from phantom_traffic_system.core import PhantomTrafficSystem, ConfigManager, phantom_logger
        from phantom_traffic_system.modules.proxy_manager import ProxyManager
        from phantom_traffic_system.modules.fingerprint_engine import FingerprintEngine
        from phantom_traffic_system.modules.behavior_simulator import BehaviorSimulator
        from phantom_traffic_system.modules.session_scheduler import SessionScheduler
        from phantom_traffic_system.modules.browser_controller import BrowserController
        
        print("✅ 所有核心模块导入成功")
        success_count += 1
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
    
    # 测试2: 配置管理器
    print("\n⚙️ 测试配置管理器...")
    total_tests += 1
    try:
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        if isinstance(config, dict) and "proxy_manager" in config:
            print("✅ 配置管理器工作正常")
            success_count += 1
        else:
            print("❌ 配置格式错误")
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
    
    # 测试3: 系统初始化
    print("\n🚀 测试系统初始化...")
    total_tests += 1
    try:
        system = PhantomTrafficSystem()
        
        # 验证各个组件
        if (hasattr(system, 'proxy_manager') and 
            hasattr(system, 'fingerprint_engine') and
            hasattr(system, 'behavior_simulator') and
            hasattr(system, 'session_scheduler') and
            hasattr(system, 'browser_controller')):
            print("✅ 系统初始化成功")
            success_count += 1
        else:
            print("❌ 系统组件缺失")
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
    
    # 测试4: 指纹生成
    print("\n🎭 测试指纹生成...")
    total_tests += 1
    try:
        system = PhantomTrafficSystem()
        fingerprint = system.fingerprint_engine.generate_complete_fingerprint()
        
        if fingerprint and hasattr(fingerprint, 'device_persona'):
            print("✅ 指纹生成成功")
            success_count += 1
        else:
            print("❌ 指纹生成失败")
    except Exception as e:
        print(f"❌ 指纹生成测试失败: {e}")
    
    # 测试5: 行为模拟
    print("\n🤖 测试行为模拟...")
    total_tests += 1
    try:
        system = PhantomTrafficSystem()
        behavior_profile = system.behavior_simulator.start_session()
        
        if behavior_profile:
            print("✅ 行为模拟成功")
            success_count += 1
        else:
            print("❌ 行为模拟失败")
    except Exception as e:
        print(f"❌ 行为模拟测试失败: {e}")
    
    # 测试6: 页面交互序列生成
    print("\n📄 测试页面交互序列生成...")
    total_tests += 1
    try:
        system = PhantomTrafficSystem()
        page_context = {
            "url": "https://example.com",
            "title": "Example",
            "viewport_size": (1920, 1080),
            "page_height": 2000
        }
        sequence = system.behavior_simulator.generate_page_interaction_sequence(page_context)
        
        if sequence and hasattr(sequence, 'actions'):
            print("✅ 页面交互序列生成成功")
            success_count += 1
        else:
            print("❌ 页面交互序列生成失败")
    except Exception as e:
        print(f"❌ 页面交互序列生成测试失败: {e}")
    
    # 测试7: 系统状态
    print("\n📊 测试系统状态...")
    total_tests += 1
    try:
        system = PhantomTrafficSystem()
        status = system.get_system_status()
        
        if isinstance(status, dict) and "is_running" in status:
            print("✅ 系统状态获取成功")
            success_count += 1
        else:
            print("❌ 系统状态格式错误")
    except Exception as e:
        print(f"❌ 系统状态测试失败: {e}")
    
    # 测试8: 目标URL管理
    print("\n🎯 测试目标URL管理...")
    total_tests += 1
    try:
        system = PhantomTrafficSystem()
        test_url = "https://example.com"
        
        system.add_target_url(test_url)
        if test_url in system.session_scheduler.target_urls:
            system.remove_target_url(test_url)
            if test_url not in system.session_scheduler.target_urls:
                print("✅ 目标URL管理成功")
                success_count += 1
            else:
                print("❌ URL移除失败")
        else:
            print("❌ URL添加失败")
    except Exception as e:
        print(f"❌ 目标URL管理测试失败: {e}")
    
    # 测试9: 流量模式设置
    print("\n🔄 测试流量模式设置...")
    total_tests += 1
    try:
        system = PhantomTrafficSystem()
        
        patterns = ["flat", "realistic", "burst", "gradual"]
        pattern_success = 0
        
        for pattern in patterns:
            try:
                system.set_traffic_pattern(pattern)
                pattern_success += 1
            except:
                pass
        
        if pattern_success >= 2:  # 至少成功设置2个模式
            print("✅ 流量模式设置成功")
            success_count += 1
        else:
            print("❌ 流量模式设置失败")
    except Exception as e:
        print(f"❌ 流量模式设置测试失败: {e}")
    
    # 测试10: 健康检查
    print("\n🏥 测试健康检查...")
    total_tests += 1
    try:
        system = PhantomTrafficSystem()
        health = await system.health_check()
        
        if isinstance(health, dict) and "overall_health" in health:
            print("✅ 健康检查成功")
            success_count += 1
        else:
            print("❌ 健康检查格式错误")
    except Exception as e:
        print(f"❌ 健康检查测试失败: {e}")
    
    # 生成测试报告
    print(f"\n{'='*60}")
    print("🎭 核心集成测试报告")
    print(f"{'='*60}")
    print(f"总测试数: {total_tests}")
    print(f"成功测试: {success_count}")
    print(f"失败测试: {total_tests - success_count}")
    print(f"成功率: {(success_count / total_tests * 100):.1f}%")
    
    if success_count == total_tests:
        print("\n🎉 所有核心功能测试通过！系统集成成功！")
        return True
    elif success_count >= total_tests * 0.8:
        print("\n✅ 大部分核心功能正常，系统基本可用")
        return True
    else:
        print("\n⚠️ 多个核心功能存在问题，需要修复")
        return False


async def test_performance_basics():
    """测试基本性能"""
    print("\n⚡ 基本性能测试...")
    
    try:
        import time
        from phantom_traffic_system.core import PhantomTrafficSystem
        
        system = PhantomTrafficSystem()
        
        # 测试指纹生成性能
        start_time = time.time()
        for _ in range(10):
            system.fingerprint_engine.generate_complete_fingerprint()
        fingerprint_time = (time.time() - start_time) / 10
        
        print(f"  指纹生成平均时间: {fingerprint_time*1000:.2f}ms")
        
        # 测试行为序列生成性能
        start_time = time.time()
        page_context = {
            "url": "https://example.com",
            "title": "Example",
            "viewport_size": (1920, 1080),
            "page_height": 2000
        }
        for _ in range(5):
            system.behavior_simulator.generate_page_interaction_sequence(page_context)
        behavior_time = (time.time() - start_time) / 5
        
        print(f"  行为序列生成平均时间: {behavior_time*1000:.2f}ms")
        
        if fingerprint_time < 1.0 and behavior_time < 1.0:
            print("✅ 基本性能测试通过")
            return True
        else:
            print("⚠️ 性能可能需要优化")
            return True
            
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("开始核心集成测试...")
    
    # 运行核心集成测试
    core_success = await test_core_integration()
    
    # 运行性能测试
    perf_success = await test_performance_basics()
    
    # 总体结果
    overall_success = core_success and perf_success
    
    if overall_success:
        print("\n🎉 核心引擎集成测试完成！系统准备就绪！")
    else:
        print("\n⚠️ 部分测试失败，但核心功能基本可用")
    
    return overall_success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
