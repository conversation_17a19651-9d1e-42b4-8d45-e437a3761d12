#!/usr/bin/env python3
"""
最终版 DDAI Space Cloudflare 解决器
基于观察到的 Cloudflare 请求模式，使用更稳定的方法
"""

import asyncio
import logging
import json
from datetime import datetime
from playwright.async_api import async_playwright

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FinalDDAISolver:
    def __init__(self):
        self.target_url = "https://app.ddai.space/register"
        self.cloudflare_requests = []
        
    async def solve_with_extended_wait(self):
        """使用扩展等待策略解决 Turnstile"""
        print("🎯 最终版 Cloudflare 解决器")
        print("=" * 60)
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=False,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-dev-shm-usage',
                    '--disable-extensions',
                    '--no-first-run',
                    '--disable-default-apps',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding'
                ]
            )
            
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                viewport={'width': 1920, 'height': 1080},
                java_script_enabled=True,
                ignore_https_errors=True
            )
            
            page = await context.new_page()
            
            # 监听所有网络活动
            async def handle_request(request):
                if any(keyword in request.url.lower() for keyword in ['cloudflare', 'turnstile', 'challenge']):
                    self.cloudflare_requests.append({
                        'type': 'request',
                        'url': request.url,
                        'method': request.method,
                        'timestamp': datetime.now().isoformat()
                    })
                    print(f"🌐 CF请求: {request.method} {request.url}")
            
            async def handle_response(response):
                if any(keyword in response.url.lower() for keyword in ['cloudflare', 'turnstile', 'challenge']):
                    self.cloudflare_requests.append({
                        'type': 'response',
                        'url': response.url,
                        'status': response.status,
                        'timestamp': datetime.now().isoformat()
                    })
                    print(f"📡 CF响应: {response.status} {response.url}")
            
            page.on('request', handle_request)
            page.on('response', handle_response)
            
            try:
                print(f"📍 导航到: {self.target_url}")
                
                # 使用更宽松的等待条件
                await page.goto(self.target_url, wait_until='domcontentloaded', timeout=60000)
                
                print("⏳ 等待页面完全加载...")
                await asyncio.sleep(10)  # 给更多时间让 SPA 加载
                
                # 尝试多种方式查找和处理 Turnstile
                success = await self.handle_turnstile_challenge(page, context)
                
                if success:
                    return success
                
                # 如果第一次尝试失败，再试一次
                print("🔄 第一次尝试失败，重新尝试...")
                await asyncio.sleep(5)
                success = await self.handle_turnstile_challenge(page, context)
                
                return success
                
            except Exception as e:
                print(f"❌ 导航失败: {e}")
                
                # 即使导航失败，也尝试处理可能已经加载的内容
                try:
                    print("🔄 尝试处理已加载的内容...")
                    await asyncio.sleep(5)
                    success = await self.handle_turnstile_challenge(page, context)
                    if success:
                        return success
                except:
                    pass
                
                return None
            finally:
                # 保存请求日志
                if self.cloudflare_requests:
                    with open("final_cf_requests.json", "w", encoding="utf-8") as f:
                        json.dump(self.cloudflare_requests, f, indent=2, ensure_ascii=False)
                    print(f"📝 Cloudflare 请求日志: final_cf_requests.json")
                
                await browser.close()
    
    async def handle_turnstile_challenge(self, page, context):
        """处理 Turnstile 挑战的核心逻辑"""
        print("🔍 开始处理 Turnstile 挑战...")
        
        # 多种选择器策略
        selectors_to_try = [
            # iframe 选择器
            'iframe[src*="challenges.cloudflare.com"]',
            'iframe[src*="turnstile"]',
            
            # 直接元素选择器
            '[data-cf-turnstile]',
            '.cf-turnstile',
            '#cf-turnstile',
            
            # 通用选择器
            'div[id*="turnstile"]',
            'div[class*="turnstile"]',
            'div[data-sitekey]',
            
            # 可能的容器
            '.challenge-container',
            '.verification-container',
            '#challenge',
            '.cloudflare-challenge'
        ]
        
        turnstile_element = None
        found_selector = None
        
        # 尝试查找 Turnstile 元素
        for attempt in range(3):  # 最多尝试 3 轮
            print(f"🔍 第 {attempt + 1} 轮查找...")
            
            for selector in selectors_to_try:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        turnstile_element = elements[0]
                        found_selector = selector
                        print(f"✅ 找到元素: {selector}")
                        break
                except Exception as e:
                    continue
            
            if turnstile_element:
                break
            
            print(f"⏳ 等待 5 秒后重试...")
            await asyncio.sleep(5)
        
        if not turnstile_element:
            print("❌ 未找到 Turnstile 元素")
            
            # 截图当前状态
            await page.screenshot(path="no_turnstile_found.png", full_page=True)
            print("📸 截图保存: no_turnstile_found.png")
            
            # 保存页面内容
            content = await page.content()
            with open("page_content_analysis.html", "w", encoding="utf-8") as f:
                f.write(content)
            print("💾 页面内容: page_content_analysis.html")
            
            return None
        
        # 处理找到的 Turnstile 元素
        print(f"🎯 处理 Turnstile 元素: {found_selector}")
        
        try:
            # 确保元素可见
            await turnstile_element.scroll_into_view_if_needed()
            await asyncio.sleep(2)
            
            # 获取元素信息
            box = await turnstile_element.bounding_box()
            if box:
                print(f"📏 元素位置: {box}")
            
            # 如果是 iframe，需要特殊处理
            if 'iframe' in found_selector:
                print("🖼️ 处理 iframe 中的 Turnstile...")
                
                # 等待 iframe 加载
                await asyncio.sleep(3)
                
                # 尝试点击 iframe 内的挑战
                try:
                    frame = await turnstile_element.content_frame()
                    if frame:
                        # 在 iframe 内查找可点击元素
                        clickable_elements = await frame.query_selector_all('input[type="checkbox"], .cb-i, .recaptcha-checkbox')
                        if clickable_elements:
                            await clickable_elements[0].click()
                            print("✅ 点击了 iframe 内的挑战")
                        else:
                            # 如果没找到特定元素，点击 iframe 中心
                            if box:
                                center_x = box['x'] + box['width'] / 2
                                center_y = box['y'] + box['height'] / 2
                                await page.mouse.click(center_x, center_y)
                                print(f"✅ 点击 iframe 中心: ({center_x}, {center_y})")
                except Exception as e:
                    print(f"⚠️ iframe 处理失败: {e}")
                    # 回退到直接点击 iframe
                    await turnstile_element.click()
            else:
                # 直接点击元素
                await turnstile_element.click()
                print("✅ 点击了 Turnstile 元素")
            
            # 等待验证完成
            return await self.wait_for_verification(page, context)
            
        except Exception as e:
            print(f"❌ 点击失败: {e}")
            return None
    
    async def wait_for_verification(self, page, context):
        """等待验证完成"""
        print("⏳ 等待验证完成...")
        
        for wait_time in range(90):  # 等待最多 90 秒
            await asyncio.sleep(1)
            
            # 检查 cookies
            cookies = await context.cookies()
            cf_clearance = next((c for c in cookies if c['name'] == 'cf_clearance'), None)
            
            if cf_clearance:
                print(f"✅ 验证完成! (耗时 {wait_time + 1} 秒)")
                return cf_clearance
            
            # 每 15 秒报告状态并截图
            if wait_time % 15 == 14:
                print(f"⏳ 等待中... ({wait_time + 1}/90秒)")
                await page.screenshot(path=f"verification_wait_{wait_time + 1}s.png")
                
                # 检查页面是否有变化
                try:
                    title = await page.title()
                    print(f"📄 当前页面标题: {title}")
                except:
                    pass
        
        print("⏰ 等待超时")
        return None
    
    def save_result(self, cookie):
        """保存成功结果"""
        if not cookie:
            return
        
        result = {
            "url": self.target_url,
            "timestamp": datetime.now().isoformat(),
            "method": "Final DDAI Solver",
            "success": True,
            "cookie": {
                "name": cookie['name'],
                "value": cookie['value'],
                "domain": cookie['domain'],
                "path": cookie['path'],
                "expires": cookie.get('expires'),
                "secure": cookie.get('secure'),
                "http_only": cookie.get('httpOnly'),
                "same_site": cookie.get('sameSite')
            },
            "cloudflare_requests": self.cloudflare_requests
        }
        
        filename = f"ddai_final_success_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"💾 成功结果保存: {filename}")
        
        # 显示使用方法
        print(f"\n🎉 成功获取 cf_clearance cookie!")
        print(f"Cookie 值: {cookie['value']}")
        print(f"\n📝 使用方法:")
        print(f"curl -H 'Cookie: cf_clearance={cookie['value']}' '{self.target_url}'")
        print(f"\nPython requests:")
        print(f"headers = {{'Cookie': 'cf_clearance={cookie['value']}'}}")
        print(f"response = requests.get('{self.target_url}', headers=headers)")
    
    async def run(self):
        """运行最终解决器"""
        print("🎯 最终版 DDAI Space Cloudflare 解决器")
        print("=" * 80)
        print(f"🌐 目标: {self.target_url}")
        print(f"⏰ 开始: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        cookie = await self.solve_with_extended_wait()
        
        if cookie:
            self.save_result(cookie)
        else:
            print("\n❌ 未能获取 cf_clearance cookie")
            print("\n💡 调试信息:")
            print("- 检查生成的截图文件")
            print("- 查看 final_cf_requests.json 了解网络请求")
            print("- 检查 page_content_analysis.html 了解页面结构")
            print("- 确认网站当前是否正常运行")
        
        print(f"\n⏰ 结束: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        return cookie

async def main():
    solver = FinalDDAISolver()
    result = await solver.run()
    
    if result:
        print(f"\n🎉 任务完成: 成功获取 cookie")
    else:
        print(f"\n❌ 任务失败: 未能获取 cookie")

if __name__ == "__main__":
    asyncio.run(main())
