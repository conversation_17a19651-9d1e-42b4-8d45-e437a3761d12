import asyncio
import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from playwright.async_api import Page
from camoufox.async_api import AsyncCamoufox
from browserforge.fingerprints import Screen

logger = logging.getLogger(__name__)


@dataclass
class CloudflareCookie:
    """Dataclass representing Cloudflare challenge cookie."""

    name: str
    value: str
    domain: str
    path: str
    expires: int
    http_only: bool
    secure: bool
    same_site: str

    def __post_init__(self) -> None:
        """Validate cookie data."""

        if not self.name or not self.value:
            raise ValueError("Cookie name and value must be set")

    @classmethod
    def from_json(cls, cookie_data: Dict[str, Any]) -> "CloudflareCookie":
        """Create CloudflareCookie from dictionary."""

        return cls(
            name=cookie_data.get("name", ""),
            value=cookie_data.get("value", ""),
            domain=cookie_data.get("domain", ""),
            path=cookie_data.get("path", "/"),
            expires=cookie_data.get("expires", 0),
            http_only=cookie_data.get("httpOnly", False),
            secure=cookie_data.get("secure", False),
            same_site=cookie_data.get("sameSite", "Lax"),
        )


class CloudflareSolver:
    """Solver for Cloudflare anti-bot challenges."""

    def __init__(
        self,
        sleep_time: int = 3,
        headless: bool = True,
        os: Optional[List[str]] = None,
        debug: bool = False,
        retries: int = 30,
    ) -> None:
        """Initialize solver with given parameters."""

        self.cf_clearance: Optional[CloudflareCookie] = None
        self.sleep_time = sleep_time
        self.headless = headless
        self.os = os or ["windows"]
        self.debug = debug
        self.retries = retries

        if debug:
            logging.basicConfig(level=logging.DEBUG)

    async def _find_and_click_challenge_frame(self, page: Page) -> bool:
        """Find Cloudflare challenge frame and click the checkbox."""
        for frame in page.frames:
            if frame.url.startswith("https://challenges.cloudflare.com"):
                try:
                    frame_element = await frame.frame_element()
                    if frame_element is None:
                        logger.debug("Frame element is None")
                        continue

                    bounding_box = await frame_element.bounding_box()
                    if bounding_box is None:
                        logger.debug("Bounding box is None")
                        continue

                    checkbox_x = bounding_box["x"] + bounding_box["width"] / 9
                    checkbox_y = bounding_box["y"] + bounding_box["height"] / 2

                    logger.debug(f"Clicking at coordinates: ({checkbox_x}, {checkbox_y})")
                    await asyncio.sleep(self.sleep_time)
                    await page.mouse.click(x=checkbox_x, y=checkbox_y)

                    return True
                except Exception as e:
                    logger.debug(f"Error in frame processing: {e}")
                    continue
        return False

    async def solve(self, link: str) -> Optional[CloudflareCookie]:
        """Solve Cloudflare challenge and return clearance cookie."""
        try:
            async with AsyncCamoufox(
                headless=self.headless,
                os=self.os,
                screen=Screen(max_width=1920, max_height=1080),
            ) as browser:
                page = await browser.new_page()

                # 设置更真实的用户代理
                await page.set_extra_http_headers({
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
                })

                logger.debug(f"Navigating to: {link}")
                await page.goto(link, wait_until='networkidle')

                # 等待页面完全加载
                await asyncio.sleep(3)

                # 检查是否已经有 cf_clearance cookie（可能不需要挑战）
                initial_cookies = await page.context.cookies()
                existing_cf_cookie = next(
                    (cookie for cookie in initial_cookies if cookie["name"] == "cf_clearance"),
                    None,
                )

                if existing_cf_cookie:
                    logger.debug("cf_clearance cookie already exists")
                    self.cf_clearance = CloudflareCookie.from_json(existing_cf_cookie)
                    return self.cf_clearance

                # 尝试查找并点击挑战
                challenge_found = False
                for attempt in range(self.retries):
                    logger.debug(f"Challenge detection attempt {attempt + 1}/{self.retries}")

                    if await self._find_and_click_challenge_frame(page):
                        challenge_found = True
                        logger.debug("Challenge found and clicked")
                        await asyncio.sleep(3)  # 等待验证完成
                        break
                    await asyncio.sleep(1)

                # 等待验证完成后再检查 cookie
                if challenge_found:
                    await asyncio.sleep(5)  # 额外等待时间

                cookies = await page.context.cookies()
                cf_clearance_cookie = next(
                    (cookie for cookie in cookies if cookie["name"] == "cf_clearance"),
                    None,
                )

                if cf_clearance_cookie:
                    logger.debug("cf_clearance cookie found: %s", cf_clearance_cookie)
                    self.cf_clearance = CloudflareCookie.from_json(cf_clearance_cookie)
                else:
                    logger.debug("cf_clearance cookie not found")
                    # 检查页面内容，看是否真的需要 Cloudflare 验证
                    page_content = await page.content()
                    if "cloudflare" not in page_content.lower() and "challenge" not in page_content.lower():
                        logger.debug("Page doesn't seem to have Cloudflare protection")

                return self.cf_clearance
        except Exception as e:
            logger.error(f"Error solving Cloudflare challenge: {e}")
            return None


if __name__ == "__main__":
    solver = CloudflareSolver(debug=True, headless=False)
    cookie = asyncio.run(solver.solve("https://2ip.pro"))

    if cookie:
        print(f"Cookie name: {cookie.name}")
        print(f"Cookie value: {cookie.value}")
        print(f"Cookie domain: {cookie.domain}")
    else:
        print("Failed to get cookie")
