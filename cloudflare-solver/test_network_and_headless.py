#!/usr/bin/env python3
"""
网络连接测试和无头模式验证脚本

先测试网络连接，然后尝试无头模式运行
"""

import asyncio
import requests
import time
from datetime import datetime
from main import CloudflareSolver

def test_network_connection():
    """测试网络连接"""
    print("🌐 网络连接测试")
    print("=" * 40)
    
    target_url = "https://app.ddai.space/register"
    
    try:
        print(f"📡 测试连接到: {target_url}")
        start_time = time.time()
        
        response = requests.get(target_url, timeout=15)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 连接成功!")
        print(f"   状态码: {response.status_code}")
        print(f"   响应时间: {duration:.2f} 秒")
        print(f"   页面大小: {len(response.text)} 字符")
        
        # 检查是否有 Cloudflare 保护
        content_lower = response.text.lower()
        if 'cloudflare' in content_lower or 'challenge' in content_lower:
            print("🛡️  检测到 Cloudflare 保护")
        else:
            print("ℹ️  未检测到明显的 Cloudflare 保护")
        
        return True
        
    except requests.exceptions.Timeout:
        print("❌ 连接超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误")
        return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

async def test_simple_headless():
    """测试简单的无头模式"""
    print("\n🔇 简单无头模式测试")
    print("=" * 40)
    
    target_url = "https://app.ddai.space/register"
    
    # 使用最简单的无头配置
    solver = CloudflareSolver(
        sleep_time=3,
        headless=True,
        os=["macos"],
        debug=True,
        retries=30
    )
    
    try:
        print("🚀 启动无头模式浏览器...")
        start_time = time.time()
        
        cookie = await solver.solve(target_url)
        
        end_time = time.time()
        duration = end_time - start_time
        
        if cookie:
            print(f"✅ 无头模式成功!")
            print(f"   耗时: {duration:.2f} 秒")
            print(f"   Cookie: {cookie.value[:50]}...")
            
            # 快速验证
            session = requests.Session()
            session.cookies.set(cookie.name, cookie.value, domain=cookie.domain)
            response = session.get(target_url, timeout=10)
            
            if response.status_code == 200:
                print("✅ Cookie 验证成功")
                return True
            else:
                print("⚠️  Cookie 验证失败")
                return False
        else:
            print(f"❌ 无头模式失败 (耗时: {duration:.2f} 秒)")
            return False
            
    except Exception as e:
        print(f"❌ 无头模式错误: {e}")
        return False

async def test_headless_with_different_configs():
    """测试不同配置的无头模式"""
    print("\n🔧 多配置无头模式测试")
    print("=" * 40)
    
    target_url = "https://app.ddai.space/register"
    
    configs = [
        {
            'name': '快速配置',
            'sleep_time': 2,
            'retries': 20,
            'os': ['macos']
        },
        {
            'name': '标准配置',
            'sleep_time': 5,
            'retries': 50,
            'os': ['windows']
        },
        {
            'name': '保守配置',
            'sleep_time': 8,
            'retries': 80,
            'os': ['linux']
        }
    ]
    
    for i, config in enumerate(configs, 1):
        print(f"\n🎯 测试配置 {i}: {config['name']}")
        
        solver = CloudflareSolver(
            sleep_time=config['sleep_time'],
            headless=True,
            os=config['os'],
            debug=False,  # 减少输出
            retries=config['retries']
        )
        
        try:
            start_time = time.time()
            cookie = await solver.solve(target_url)
            end_time = time.time()
            duration = end_time - start_time
            
            if cookie:
                print(f"   ✅ 成功! 耗时: {duration:.2f}s")
                return True
            else:
                print(f"   ❌ 失败 (耗时: {duration:.2f}s)")
                
        except Exception as e:
            print(f"   ❌ 错误: {str(e)[:50]}...")
    
    return False

def main():
    """主函数"""
    print("🧪 网络连接和无头模式综合测试")
    print("=" * 50)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 1. 测试网络连接
    network_ok = test_network_connection()
    
    if not network_ok:
        print("\n❌ 网络连接失败，无法继续测试")
        return
    
    # 2. 测试简单无头模式
    print("\n" + "=" * 50)
    simple_headless_ok = asyncio.run(test_simple_headless())
    
    if simple_headless_ok:
        print("\n🎉 无头模式测试成功!")
        print("✅ 网络连接正常")
        print("✅ 无头模式工作正常")
        print("✅ Cookie 获取和验证成功")
    else:
        # 3. 如果简单配置失败，尝试多种配置
        print("\n" + "=" * 50)
        print("ℹ️  简单配置失败，尝试其他配置...")
        
        multi_config_ok = asyncio.run(test_headless_with_different_configs())
        
        if multi_config_ok:
            print("\n🎉 多配置测试成功!")
            print("✅ 找到了适合的无头模式配置")
        else:
            print("\n❌ 所有无头模式配置都失败了")
            print("💡 可能的原因:")
            print("   • 网站临时不可用")
            print("   • Cloudflare 策略更新")
            print("   • 需要调整配置参数")
            print("💡 建议:")
            print("   • 稍后重试")
            print("   • 使用有头模式调试")
            print("   • 检查网络环境")
    
    print("\n" + "=" * 50)
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
