# Cloudflare Solver 安装和使用指南

## 项目简介

这是一个基于 Python 的异步 Cloudflare Turnstile 挑战解决方案，能够绕过 Cloudflare 的反机器人验证并获取 `cf_clearance` cookie。

## 主要特性

- **异步处理** - 使用 `asyncio` 和 `Playwright` 实现高性能
- **隐蔽性强** - 使用 Camoufox 和 BrowserForge 进行真实的浏览器指纹伪装
- **模块化设计** - 清晰的面向对象设计，使用数据类处理 cookie
- **可配置** - 支持自定义重试次数、延迟、操作系统指纹等
- **调试支持** - 内置调试日志功能

## 安装步骤

### 1. 安装 Python 依赖项

```bash
cd cloudflare-solver
pip install -r requirements.txt
```

依赖项包括：
- `browserforge==1.2.3` - 浏览器指纹伪装
- `camoufox==0.4.11` - 隐蔽的 Firefox 浏览器
- `playwright==1.52.0` - 浏览器自动化框架

### 2. 安装 Playwright 浏览器

```bash
playwright install
```

这将下载并安装 Playwright 所需的浏览器二进制文件。

### 3. 验证安装

运行快速测试：

```bash
python3 quick_test.py
```

## 基本使用方法

### 简单示例

```python
import asyncio
from main import CloudflareSolver

async def main():
    solver = CloudflareSolver(
        headless=True,      # 无头模式
        os=["windows"],     # Windows 指纹
        debug=True          # 启用调试
    )

    cookie = await solver.solve("https://protected-site.com")

    if cookie:
        print(f"Cookie: {cookie.name}={cookie.value}")
    else:
        print("Failed to solve challenge")

asyncio.run(main())
```

### 高级配置

```python
solver = CloudflareSolver(
    sleep_time=5,       # 点击前等待时间（秒）
    headless=False,     # 显示浏览器窗口
    os=["macos"],       # macOS 指纹
    debug=True,         # 启用详细日志
    retries=50          # 增加重试次数
)
```

## API 参考

### CloudflareCookie 数据类

表示 Cloudflare clearance cookie：

- `name`: Cookie 名称（通常是 "cf_clearance"）
- `value`: Cookie 值
- `domain`: Cookie 域名
- `path`: Cookie 路径
- `expires`: 过期时间戳
- `http_only`: HTTP Only 标志
- `secure`: Secure 标志
- `same_site`: SameSite 策略

### CloudflareSolver 类

#### 参数：

- `sleep_time`: 点击挑战前的延迟时间（默认：3）
- `headless`: 无头模式运行（默认：True）
- `os`: 操作系统指纹（默认：["windows"]）
- `debug`: 启用调试日志（默认：False）
- `retries`: 查找挑战的重试次数（默认：30）

#### 方法：

- `solve(link: str)`: 解决指定 URL 的 Cloudflare 挑战，返回 `CloudflareCookie` 或 `None`

## 工作原理

1. 启动带有真实指纹的隐蔽浏览器实例
2. 导航到受保护的 URL
3. 检测 Cloudflare 挑战 iframe
4. 自动点击验证复选框
5. 成功后提取 `cf_clearance` cookie
6. 返回验证过的数据类对象

## 测试文件

- `quick_test.py` - 快速功能测试
- `example_usage_simple.py` - 详细使用示例
- `main.py` - 主程序文件

## 注意事项

⚠️ **重要提醒**：使用此工具时请遵守相关法律法规和网站的服务条款。作者不对使用此工具产生的任何后果负责。

## 故障排除

### 常见问题

1. **"Failed to get cookie"** - 可能原因：
   - 网站没有 Cloudflare 保护
   - 挑战类型不受支持
   - 网络连接问题

2. **"'NoneType' object is not subscriptable"** - 已修复，更新到最新版本

3. **浏览器启动失败** - 确保已运行 `playwright install`

### 调试建议

- 使用 `debug=True` 启用详细日志
- 使用 `headless=False` 观察浏览器行为
- 增加 `retries` 和 `sleep_time` 参数
- 检查网络连接和防火墙设置

## 更新日志

- 修复了 bounding_box 为 None 时的错误
- 添加了更好的错误处理
- 改进了调试输出
- 添加了使用示例和文档
