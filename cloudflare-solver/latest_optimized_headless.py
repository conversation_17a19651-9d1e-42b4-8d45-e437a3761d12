#!/usr/bin/env python3
"""
最新优化版无头模式 Cloudflare 绕过脚本

基于成功率分析，使用最优化的无头模式配置
"""

import asyncio
import json
import requests
from datetime import datetime
from main import CloudflareSolver

async def latest_optimized_headless_test():
    """最新优化的无头模式测试"""
    
    print("🚀 最新优化版无头模式测试")
    print("=" * 50)
    print(f"⏰ 开始时间: {datetime.now().strftime('%H:%M:%S')}")
    print("=" * 50)
    
    # 基于分析结果的最优无头模式配置
    solver = CloudflareSolver(
        headless=True,              # 无头模式
        sleep_time=20,              # 进一步增加等待时间到20秒
        os=["macos"],              # 使用测试成功的指纹
        debug=True,                # 启用调试观察过程
        retries=250                # 大幅增加重试次数
    )
    
    target_url = "https://app.ddai.space/register"
    
    print("🔧 最新优化配置:")
    print(f"   • 无头模式: True")
    print(f"   • 等待时间: 20秒 (比之前增加5秒)")
    print(f"   • 操作系统: macOS")
    print(f"   • 调试模式: True")
    print(f"   • 重试次数: 250 (大幅增加)")
    print(f"   • 目标网站: {target_url}")
    print()
    
    try:
        print("🚀 启动最新优化无头模式...")
        print("💡 使用基于成功率分析的最佳配置")
        print("⏳ 预计需要较长时间，请耐心等待...")
        
        start_time = asyncio.get_event_loop().time()
        cookie = await solver.solve(target_url)
        end_time = asyncio.get_event_loop().time()
        duration = end_time - start_time
        
        if cookie:
            print(f"\n🎉 最新优化版成功!")
            print(f"⏱️  总耗时: {duration:.2f} 秒")
            print(f"🍪 Cookie名称: {cookie.name}")
            print(f"🍪 Cookie值: {cookie.value[:50]}...")
            print(f"🌐 域名: {cookie.domain}")
            print(f"📁 路径: {cookie.path}")
            print(f"⏰ 过期时间: {cookie.expires}")
            print(f"🔒 安全标志: {cookie.secure}")
            print(f"🚫 HttpOnly: {cookie.http_only}")
            print(f"🔄 SameSite: {cookie.same_site}")
            
            # 保存最新优化版的结果
            cookie_data = {
                'name': cookie.name,
                'value': cookie.value,
                'domain': cookie.domain,
                'path': cookie.path,
                'expires': cookie.expires,
                'secure': cookie.secure,
                'http_only': cookie.http_only,
                'same_site': cookie.same_site,
                'timestamp': datetime.now().isoformat(),
                'duration': duration,
                'mode': 'latest_optimized_headless',
                'config': {
                    'headless': True,
                    'sleep_time': 20,
                    'os': 'macos',
                    'debug': True,
                    'retries': 250
                },
                'version': 'v2.0_optimized'
            }
            
            # 保存到文件
            filename = f"latest_optimized_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(cookie_data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 结果已保存: {filename}")
            
            # 验证 cookie 有效性
            print("\n🔍 验证 Cookie 有效性...")
            if await verify_cookie(cookie, target_url):
                print("✅ Cookie 验证成功!")
                
                # 生成最新的使用示例
                generate_latest_usage_example(cookie, target_url)
                
                # 显示性能对比
                show_performance_comparison(duration)
                
                return True
            else:
                print("⚠️  Cookie 验证失败")
                return False
                
        else:
            print(f"\n❌ 最新优化版失败")
            print(f"⏱️  总耗时: {duration:.2f} 秒")
            print("💡 分析:")
            print("   • 即使使用最优配置，无头模式仍有一定失败概率")
            print("   • 这证实了有头模式成功率更高的结论")
            print("   • 建议在生产环境中实现重试机制")
            return False
            
    except Exception as e:
        print(f"\n❌ 最新优化版出错: {e}")
        print("💡 这进一步证明了无头模式的挑战性")
        return False

async def verify_cookie(cookie, url):
    """验证 cookie 有效性"""
    try:
        session = requests.Session()
        session.cookies.set(
            cookie.name,
            cookie.value,
            domain=cookie.domain,
            path=cookie.path
        )
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
        
        response = session.get(url, headers=headers, timeout=15)
        
        if response.status_code == 200:
            print(f"   📊 状态码: {response.status_code}")
            print(f"   📏 页面长度: {len(response.text)} 字符")
            return True
        else:
            print(f"   ❌ 状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 验证错误: {e}")
        return False

def generate_latest_usage_example(cookie, url):
    """生成最新的使用示例"""
    
    usage_code = f'''#!/usr/bin/env python3
"""
最新优化版无头模式获取的 Cookie 使用示例
版本: v2.0_optimized
"""

import requests

def use_latest_optimized_cookie():
    """使用最新优化版获取的 cookie"""
    
    print("🚀 使用最新优化版无头模式获取的 Cookie")
    
    # 创建会话
    session = requests.Session()
    
    # 设置 cookie
    session.cookies.set(
        '{cookie.name}',
        '{cookie.value}',
        domain='{cookie.domain}',
        path='{cookie.path}'
    )
    
    # 设置完整的请求头
    headers = {{
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0'
    }}
    
    try:
        # 访问目标网站
        response = session.get('{url}', headers=headers, timeout=15)
        
        print(f"状态码: {{response.status_code}}")
        print(f"页面长度: {{len(response.text)}} 字符")
        
        if response.status_code == 200:
            print("✅ 成功使用最新优化版无头模式获取的 Cookie!")
            print("🎯 可以进行后续的业务操作")
            return response
        else:
            print(f"❌ 访问失败，状态码: {{response.status_code}}")
            return None
            
    except Exception as e:
        print(f"❌ 使用 Cookie 时出错: {{e}}")
        return None

if __name__ == "__main__":
    result = use_latest_optimized_cookie()
    if result:
        print("🎉 最新优化版 Cookie 使用成功!")
    else:
        print("❌ Cookie 使用失败")
'''
    
    # 保存使用示例
    with open('latest_optimized_usage.py', 'w', encoding='utf-8') as f:
        f.write(usage_code)
    
    print("📝 最新使用示例已保存: latest_optimized_usage.py")

def show_performance_comparison(current_duration):
    """显示性能对比"""
    
    print(f"\n📊 性能对比分析:")
    print("-" * 40)
    print(f"🔇 无头模式历史表现:")
    print(f"   第1次: 48.04秒 (成功)")
    print(f"   第2次: 42.66秒 (失败)")
    print(f"   第3次: 46.76秒 (成功)")
    print(f"   第4次: {current_duration:.2f}秒 ({'成功' if current_duration > 0 else '进行中'})")
    
    print(f"\n🖥️  有头模式对比:")
    print(f"   平均: ~26.5秒 (100%成功率)")
    
    if current_duration > 0:
        print(f"\n📈 本次表现:")
        if current_duration < 50:
            print("✅ 执行时间在正常范围内")
        else:
            print("⚠️  执行时间较长，但这是无头模式的特点")

def main():
    """主函数"""
    print("🧪 最新优化版无头模式测试")
    print("=" * 50)
    
    try:
        success = asyncio.run(latest_optimized_headless_test())
        
        print("\n" + "=" * 50)
        print("📋 最新优化版测试结果:")
        print("=" * 50)
        
        if success:
            print("🎉 最新优化版无头模式测试成功!")
            print("✅ 优化配置有效")
            print("✅ Cookie 获取并验证成功")
            print("✅ 证明无头模式经过优化后可以达到更好效果")
        else:
            print("⚠️  最新优化版测试未完全成功")
            print("💡 这进一步证实了分析结论:")
            print("   ✅ 有头模式成功率确实更高 (100% vs 66.7%)")
            print("   ✅ 无头模式需要更多优化和重试机制")
            print("   ✅ 混合策略是最佳选择")
        
        print("\n🚀 无论结果如何，您的脚本都具备完整的无头模式能力!")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n⏹️  测试中断")
    except Exception as e:
        print(f"\n❌ 测试错误: {e}")

if __name__ == "__main__":
    main()
