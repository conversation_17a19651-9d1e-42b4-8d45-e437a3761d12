#!/usr/bin/env python3
"""
交互式 Cloudflare Solver

这个脚本允许您输入任何网址，然后尝试绕过 Cloudflare 验证。
使用方法：python3 interactive_solver.py
"""

import asyncio
import sys
from main import CloudflareSolver

def print_banner():
    """打印程序横幅"""
    print("=" * 60)
    print("🛡️  Cloudflare Solver - 交互式版本")
    print("=" * 60)
    print("这个工具可以帮助您绕过 Cloudflare Turnstile 验证")
    print("并获取 cf_clearance cookie")
    print()

async def solve_cloudflare(url):
    """解决指定 URL 的 Cloudflare 挑战"""
    
    print(f"🎯 目标网站: {url}")
    print("-" * 40)
    
    # 创建 solver 实例
    solver = CloudflareSolver(
        sleep_time=5,        # 等待时间
        headless=False,      # 显示浏览器窗口
        os=["macos"],        # 使用 macOS 指纹
        debug=True,          # 启用调试信息
        retries=60           # 重试次数
    )
    
    try:
        print("⏳ 正在启动浏览器...")
        print("💡 提示：浏览器窗口将会打开，请不要手动操作")
        print()
        
        # 尝试解决 Cloudflare 挑战
        cookie = await solver.solve(url)
        
        if cookie:
            print("🎉 成功绕过 Cloudflare 验证！")
            print("=" * 40)
            print(f"✅ Cookie 名称: {cookie.name}")
            print(f"✅ Cookie 值: {cookie.value}")
            print(f"✅ 域名: {cookie.domain}")
            print(f"✅ 路径: {cookie.path}")
            print(f"✅ 过期时间: {cookie.expires}")
            print(f"✅ 安全标志: {cookie.secure}")
            print(f"✅ HttpOnly: {cookie.http_only}")
            print(f"✅ SameSite: {cookie.same_site}")
            print("=" * 40)
            
            # 提供使用建议
            print("\n💡 如何使用这个 Cookie：")
            print("1. 复制上面的 cookie 值")
            print("2. 在您的 HTTP 请求中添加这个 cookie")
            print("3. 示例代码：")
            print(f"""
import requests

session = requests.Session()
session.cookies.set(
    '{cookie.name}',
    '{cookie.value}',
    domain='{cookie.domain}',
    path='{cookie.path}'
)

# 现在可以使用这个 session 访问网站
response = session.get('{url}')
print(response.text)
            """)
            
            return cookie
            
        else:
            print("❌ 未能获取 cf_clearance cookie")
            print("\n可能的原因：")
            print("• 网站当前没有启用 Cloudflare 挑战")
            print("• 您的 IP 地址被信任，不需要验证")
            print("• 挑战类型不受支持（如图片验证码）")
            print("• 网络连接问题")
            print("\n💡 建议：")
            print("• 尝试使用 VPN 或代理")
            print("• 稍后重试")
            print("• 检查网站是否真的有 Cloudflare 保护")
            
            return None
            
    except KeyboardInterrupt:
        print("\n⏹️  用户中断操作")
        return None
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        print("\n💡 故障排除建议：")
        print("• 检查网络连接")
        print("• 确保网址格式正确")
        print("• 尝试重新运行程序")
        return None

def get_user_input():
    """获取用户输入的网址"""
    while True:
        try:
            print("\n📝 请输入要测试的网站 URL：")
            print("（输入 'quit' 或 'exit' 退出程序）")
            
            url = input("URL: ").strip()
            
            if url.lower() in ['quit', 'exit', 'q']:
                return None
                
            if not url:
                print("❌ 请输入有效的 URL")
                continue
                
            # 自动添加 https:// 前缀
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
                print(f"🔗 自动添加 https:// 前缀: {url}")
                
            return url
            
        except KeyboardInterrupt:
            print("\n👋 程序已退出")
            return None
        except Exception as e:
            print(f"❌ 输入错误: {e}")
            continue

async def main():
    """主函数"""
    print_banner()
    
    while True:
        # 获取用户输入
        url = get_user_input()
        
        if url is None:
            break
            
        # 尝试解决 Cloudflare 挑战
        cookie = await solve_cloudflare(url)
        
        # 询问是否继续
        print("\n" + "=" * 60)
        try:
            continue_choice = input("是否测试另一个网站？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是', '继续']:
                break
        except KeyboardInterrupt:
            break
    
    print("\n👋 感谢使用 Cloudflare Solver！")
    print("💡 记住：请遵守网站的服务条款和相关法律法规")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
        sys.exit(1)
