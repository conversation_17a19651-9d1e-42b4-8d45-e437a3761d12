#!/usr/bin/env python3
"""
优化的批量测试解决方案

实现浏览器指纹轮换和访问模式优化
"""

import asyncio
import json
import time
import random
from datetime import datetime
from main import CloudflareSolver

class OptimizedBatchSolver:
    """优化的批量测试解决方案"""
    
    def __init__(self):
        self.target_url = "https://app.ddai.space/register"
        self.results = []
        
        # 浏览器指纹配置
        self.fingerprint_configs = [
            {
                "name": "Chrome_Windows",
                "os": ["windows"],
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "viewport": {"width": 1920, "height": 1080},
                "timezone": "America/New_York"
            },
            {
                "name": "Firefox_macOS",
                "os": ["macos"],
                "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0",
                "viewport": {"width": 1440, "height": 900},
                "timezone": "America/Los_Angeles"
            },
            {
                "name": "Safari_macOS",
                "os": ["macos"],
                "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
                "viewport": {"width": 1680, "height": 1050},
                "timezone": "America/Chicago"
            },
            {
                "name": "Edge_Windows",
                "os": ["windows"],
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
                "viewport": {"width": 1366, "height": 768},
                "timezone": "America/Denver"
            },
            {
                "name": "Chrome_Linux",
                "os": ["linux"],
                "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "viewport": {"width": 1600, "height": 900},
                "timezone": "America/Phoenix"
            }
        ]
    
    def get_random_fingerprint(self):
        """获取随机浏览器指纹"""
        return random.choice(self.fingerprint_configs)
    
    def get_random_interval(self, min_seconds=60, max_seconds=300):
        """获取随机间隔时间"""
        return random.randint(min_seconds, max_seconds)
    
    def get_random_delay(self, min_seconds=2, max_seconds=8):
        """获取随机操作延迟"""
        return random.uniform(min_seconds, max_seconds)
    
    async def optimized_single_test(self, test_num, fingerprint):
        """优化的单次测试"""
        
        print(f"\n🚀 第 {test_num} 次测试 - {fingerprint['name']}")
        print(f"   🎭 指纹: {fingerprint['name']}")
        print(f"   📱 系统: {fingerprint['os'][0]}")
        print(f"   🖥️  分辨率: {fingerprint['viewport']['width']}x{fingerprint['viewport']['height']}")
        
        # 随机操作延迟
        pre_delay = self.get_random_delay(1, 3)
        print(f"   ⏳ 预延迟: {pre_delay:.1f}秒")
        await asyncio.sleep(pre_delay)
        
        # 创建带指纹的solver
        solver = CloudflareSolver(
            headless=False,
            sleep_time=random.randint(8, 15),  # 随机等待时间
            os=fingerprint['os'],
            debug=False,
            retries=random.randint(60, 100)  # 随机重试次数
        )
        
        start_time = time.time()
        
        try:
            cookie = await solver.solve(self.target_url)
            end_time = time.time()
            duration = end_time - start_time
            
            # 随机后延迟（模拟用户查看页面）
            post_delay = self.get_random_delay(2, 6)
            await asyncio.sleep(post_delay)
            
            if cookie:
                result = {
                    'test_num': test_num,
                    'success': True,
                    'duration': duration,
                    'fingerprint': fingerprint['name'],
                    'pre_delay': pre_delay,
                    'post_delay': post_delay,
                    'timestamp': datetime.now().isoformat()
                }
                print(f"   ✅ 成功! 耗时: {duration:.2f}s")
            else:
                result = {
                    'test_num': test_num,
                    'success': False,
                    'duration': duration,
                    'fingerprint': fingerprint['name'],
                    'pre_delay': pre_delay,
                    'post_delay': post_delay,
                    'timestamp': datetime.now().isoformat(),
                    'error': 'No cookie obtained'
                }
                print(f"   ❌ 失败! 耗时: {duration:.2f}s")
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            result = {
                'test_num': test_num,
                'success': False,
                'duration': duration,
                'fingerprint': fingerprint['name'],
                'pre_delay': pre_delay,
                'post_delay': 0,
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
            print(f"   ❌ 错误! 耗时: {duration:.2f}s")
        
        return result
    
    async def run_optimized_batch(self, test_count=15):
        """运行优化的批量测试"""
        
        print("🧠 优化批量测试 - 指纹轮换 + 模式随机化")
        print("=" * 60)
        print(f"🎯 测试数量: {test_count}")
        print(f"🎭 指纹配置: {len(self.fingerprint_configs)} 种")
        print(f"⏰ 间隔范围: 60-300秒随机")
        print(f"🎲 操作延迟: 2-8秒随机")
        print("=" * 60)
        
        success_count = 0
        
        for i in range(1, test_count + 1):
            # 随机选择指纹
            fingerprint = self.get_random_fingerprint()
            
            # 执行测试
            result = await self.optimized_single_test(i, fingerprint)
            self.results.append(result)
            
            if result['success']:
                success_count += 1
            
            # 显示当前统计
            current_rate = (success_count / i) * 100
            print(f"   📊 当前成功率: {success_count}/{i} = {current_rate:.1f}%")
            
            # 如果不是最后一次，随机等待
            if i < test_count:
                interval = self.get_random_interval(60, 300)  # 1-5分钟随机
                print(f"   ⏳ 随机等待: {interval}秒 ({interval/60:.1f}分钟)")
                await asyncio.sleep(interval)
        
        return self.results
    
    def analyze_optimized_results(self):
        """分析优化后的结果"""
        
        print(f"\n📊 优化批量测试结果分析")
        print("=" * 60)
        
        total_tests = len(self.results)
        successful_tests = [r for r in self.results if r['success']]
        success_count = len(successful_tests)
        success_rate = (success_count / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"总测试次数: {total_tests}")
        print(f"成功次数: {success_count}")
        print(f"成功率: {success_rate:.1f}%")
        
        if successful_tests:
            durations = [r['duration'] for r in successful_tests]
            avg_duration = sum(durations) / len(durations)
            print(f"平均耗时: {avg_duration:.2f}秒")
        
        # 按指纹分析
        fingerprint_stats = {}
        for result in self.results:
            fp = result['fingerprint']
            if fp not in fingerprint_stats:
                fingerprint_stats[fp] = {'total': 0, 'success': 0}
            fingerprint_stats[fp]['total'] += 1
            if result['success']:
                fingerprint_stats[fp]['success'] += 1
        
        print(f"\n🎭 各指纹表现:")
        for fp, stats in fingerprint_stats.items():
            rate = (stats['success'] / stats['total']) * 100
            print(f"   {fp}: {stats['success']}/{stats['total']} = {rate:.1f}%")
        
        # 保存结果
        filename = f"optimized_batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'test_type': 'optimized_batch_with_fingerprint_rotation',
                'total_tests': total_tests,
                'success_count': success_count,
                'success_rate': success_rate,
                'fingerprint_stats': fingerprint_stats,
                'results': self.results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"💾 结果已保存: {filename}")
        
        return {
            'success_rate': success_rate,
            'total_tests': total_tests,
            'fingerprint_stats': fingerprint_stats
        }
    
    def compare_with_previous_results(self, current_rate):
        """与之前结果对比"""
        
        print(f"\n📈 与之前测试对比:")
        print("=" * 40)
        print(f"原始批量测试: 25.0% (5/20)")
        print(f"优化批量测试: {current_rate:.1f}%")
        
        improvement = current_rate - 25.0
        if improvement > 0:
            print(f"✅ 改善: +{improvement:.1f}个百分点")
        else:
            print(f"❌ 下降: {improvement:.1f}个百分点")
        
        if current_rate >= 40:
            print("🎉 优化效果显著!")
        elif current_rate >= 30:
            print("✅ 优化有一定效果")
        else:
            print("⚠️  优化效果有限")

async def main():
    """主函数"""
    
    print("🚀 优化批量测试 - 指纹轮换版")
    print("=" * 60)
    print("这个版本将使用:")
    print("• 5种不同的浏览器指纹轮换")
    print("• 60-300秒的随机间隔时间")
    print("• 2-8秒的随机操作延迟")
    print("• 随机化的配置参数")
    print("预计进行15次测试，耗时约30-60分钟")
    print("=" * 60)
    
    try:
        confirm = input("开始优化批量测试? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("❌ 测试已取消")
            return
        
        solver = OptimizedBatchSolver()
        results = await solver.run_optimized_batch(15)  # 15次测试
        summary = solver.analyze_optimized_results()
        solver.compare_with_previous_results(summary['success_rate'])
        
        print(f"\n🎉 优化批量测试完成!")
        print(f"🏆 最终成功率: {summary['success_rate']:.1f}%")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())
