#!/usr/bin/env python3
"""
改进版 DDAI Space Cloudflare 解决器
解决页面加载超时问题，专注于 Turnstile 验证处理
"""

import asyncio
import logging
import json
from datetime import datetime
from playwright.async_api import async_playwright

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImprovedDDAISolver:
    def __init__(self):
        self.target_url = "https://app.ddai.space/register"
        self.cloudflare_requests = []
        
    async def solve_turnstile_challenge(self):
        """改进的 Turnstile 挑战解决方案"""
        print("🎯 改进版 DDAI Space Cloudflare 解决器")
        print("=" * 80)
        print(f"🌐 目标: {self.target_url}")
        print(f"⏰ 开始: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=False,  # 显示浏览器便于观察
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-dev-shm-usage',
                    '--disable-extensions',
                    '--no-first-run',
                    '--disable-default-apps',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-features=TranslateUI',
                    '--disable-ipc-flooding-protection'
                ]
            )
            
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                viewport={'width': 1920, 'height': 1080},
                java_script_enabled=True,
                ignore_https_errors=True
            )
            
            page = await context.new_page()
            
            # 监听网络请求
            async def handle_request(request):
                if any(keyword in request.url.lower() for keyword in ['cloudflare', 'turnstile', 'challenge']):
                    self.cloudflare_requests.append({
                        'type': 'request',
                        'url': request.url,
                        'method': request.method,
                        'timestamp': datetime.now().isoformat()
                    })
                    print(f"🌐 CF请求: {request.method} {request.url}")
            
            page.on('request', handle_request)
            
            try:
                print("📍 正在导航到目标页面...")
                
                # 使用更宽松的等待条件，不等待 networkidle
                response = await page.goto(
                    self.target_url, 
                    wait_until='domcontentloaded',  # 只等待 DOM 加载完成
                    timeout=90000  # 增加超时时间到 90 秒
                )
                
                print(f"✅ 页面响应状态: {response.status if response else 'Unknown'}")
                
                # 等待页面基本加载
                print("⏳ 等待页面基本元素加载...")
                await asyncio.sleep(8)
                
                # 尝试处理 Turnstile 挑战
                success = await self.handle_turnstile_with_retry(page, context)
                
                return success
                
            except Exception as e:
                print(f"⚠️ 页面导航异常: {e}")
                print("🔄 尝试在当前状态下处理 Turnstile...")
                
                # 即使导航失败，也尝试处理可能已经加载的内容
                try:
                    await asyncio.sleep(5)
                    success = await self.handle_turnstile_with_retry(page, context)
                    if success:
                        return success
                except Exception as inner_e:
                    print(f"❌ 处理失败: {inner_e}")
                
                return None
            finally:
                # 保存调试信息
                await self.save_debug_info(page)
                await browser.close()
    
    async def handle_turnstile_with_retry(self, page, context):
        """带重试机制的 Turnstile 处理"""
        print("🔍 开始处理 Turnstile 挑战...")
        
        # 多种选择器策略
        selectors = [
            '#cf-turnstile',
            '[data-cf-turnstile]',
            '.cf-turnstile',
            'iframe[src*="challenges.cloudflare.com"]',
            'div[id*="turnstile"]',
            'div[class*="turnstile"]'
        ]
        
        # 尝试多次查找和处理
        for attempt in range(5):
            print(f"🔍 第 {attempt + 1} 次尝试查找 Turnstile...")
            
            turnstile_element = None
            found_selector = None
            
            # 查找 Turnstile 元素
            for selector in selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        turnstile_element = elements[0]
                        found_selector = selector
                        print(f"✅ 找到 Turnstile: {selector}")
                        break
                except Exception as e:
                    continue
            
            if turnstile_element:
                # 尝试处理找到的元素
                success = await self.interact_with_turnstile(page, context, turnstile_element, found_selector)
                if success:
                    return success
            
            # 如果没找到或处理失败，等待后重试
            if attempt < 4:
                print(f"⏳ 等待 8 秒后重试...")
                await asyncio.sleep(8)
        
        print("❌ 所有尝试都失败了")
        return None
    
    async def interact_with_turnstile(self, page, context, element, selector):
        """与 Turnstile 元素交互"""
        print(f"🎯 处理 Turnstile 元素: {selector}")
        
        try:
            # 确保元素可见
            await element.scroll_into_view_if_needed()
            await asyncio.sleep(3)
            
            # 获取元素位置
            box = await element.bounding_box()
            if box:
                print(f"📏 元素位置: x={box['x']}, y={box['y']}, w={box['width']}, h={box['height']}")
            
            # 处理不同类型的元素
            if 'iframe' in selector:
                print("🖼️ 处理 iframe 中的 Turnstile...")
                await self.handle_iframe_turnstile(page, element)
            else:
                print("🖱️ 直接点击 Turnstile 元素...")
                await element.click()
            
            print("✅ 已触发 Turnstile 验证")
            
            # 等待验证完成
            return await self.wait_for_verification_completion(page, context)
            
        except Exception as e:
            print(f"❌ 交互失败: {e}")
            return None
    
    async def handle_iframe_turnstile(self, page, iframe_element):
        """处理 iframe 中的 Turnstile"""
        try:
            # 等待 iframe 加载
            await asyncio.sleep(3)
            
            frame = await iframe_element.content_frame()
            if frame:
                # 在 iframe 内查找可点击元素
                clickable_selectors = [
                    'input[type="checkbox"]',
                    '.cb-i',
                    '.recaptcha-checkbox',
                    'div[role="checkbox"]',
                    'span[role="checkbox"]'
                ]
                
                for selector in clickable_selectors:
                    try:
                        elements = await frame.query_selector_all(selector)
                        if elements:
                            await elements[0].click()
                            print(f"✅ 点击了 iframe 内的元素: {selector}")
                            return
                    except:
                        continue
                
                # 如果没找到特定元素，点击 iframe 中心
                box = await iframe_element.bounding_box()
                if box:
                    center_x = box['x'] + box['width'] / 2
                    center_y = box['y'] + box['height'] / 2
                    await page.mouse.click(center_x, center_y)
                    print(f"✅ 点击 iframe 中心: ({center_x}, {center_y})")
            else:
                # 直接点击 iframe 元素
                await iframe_element.click()
                print("✅ 直接点击了 iframe")
                
        except Exception as e:
            print(f"⚠️ iframe 处理异常: {e}")
            # 回退到直接点击
            await iframe_element.click()
    
    async def wait_for_verification_completion(self, page, context):
        """等待验证完成"""
        print("⏳ 等待验证完成...")
        
        for wait_time in range(120):  # 等待最多 2 分钟
            await asyncio.sleep(1)
            
            # 检查 cookies
            cookies = await context.cookies()
            cf_clearance = next((c for c in cookies if c['name'] == 'cf_clearance'), None)
            
            if cf_clearance:
                print(f"🎉 验证成功! (耗时 {wait_time + 1} 秒)")
                return cf_clearance
            
            # 每 20 秒报告状态
            if wait_time % 20 == 19:
                print(f"⏳ 等待中... ({wait_time + 1}/120秒)")
                
                # 检查页面状态
                try:
                    title = await page.title()
                    print(f"📄 当前标题: {title}")
                except:
                    pass
        
        print("⏰ 验证等待超时")
        return None

    async def save_debug_info(self, page):
        """保存调试信息"""
        try:
            # 保存截图
            await page.screenshot(path="improved_final_screenshot.png", full_page=True)
            print("📸 截图保存: improved_final_screenshot.png")

            # 保存页面内容
            content = await page.content()
            with open("improved_page_content.html", "w", encoding="utf-8") as f:
                f.write(content)
            print("💾 页面内容: improved_page_content.html")

            # 保存网络请求日志
            if self.cloudflare_requests:
                with open("improved_cf_requests.json", "w", encoding="utf-8") as f:
                    json.dump(self.cloudflare_requests, f, indent=2, ensure_ascii=False)
                print("📝 网络日志: improved_cf_requests.json")

        except Exception as e:
            print(f"⚠️ 保存调试信息失败: {e}")

    def save_success_result(self, cookie):
        """保存成功结果"""
        if not cookie:
            return

        result = {
            "url": self.target_url,
            "timestamp": datetime.now().isoformat(),
            "method": "Improved DDAI Solver",
            "success": True,
            "cookie": {
                "name": cookie['name'],
                "value": cookie['value'],
                "domain": cookie['domain'],
                "path": cookie['path'],
                "expires": cookie.get('expires'),
                "secure": cookie.get('secure'),
                "http_only": cookie.get('httpOnly'),
                "same_site": cookie.get('sameSite')
            },
            "cloudflare_requests_count": len(self.cloudflare_requests)
        }

        filename = f"ddai_improved_success_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)

        print(f"\n🎉 成功获取 cf_clearance cookie!")
        print(f"💾 结果保存: {filename}")
        print(f"🍪 Cookie 值: {cookie['value']}")
        print(f"\n📝 使用方法:")
        print(f"curl -H 'Cookie: cf_clearance={cookie['value']}' '{self.target_url}'")
        print(f"\nPython requests:")
        print(f"headers = {{'Cookie': 'cf_clearance={cookie['value']}'}}")
        print(f"response = requests.get('{self.target_url}', headers=headers)")

    async def run(self):
        """运行改进版解决器"""
        cookie = await self.solve_turnstile_challenge()

        if cookie:
            self.save_success_result(cookie)
            print(f"\n✅ 任务完成: 成功获取 cookie")
            return cookie
        else:
            print(f"\n❌ 任务失败: 未能获取 cookie")
            print(f"\n💡 调试建议:")
            print("- 检查 improved_final_screenshot.png 了解最终状态")
            print("- 查看 improved_page_content.html 分析页面结构")
            print("- 检查 improved_cf_requests.json 了解网络活动")
            print("- 尝试手动访问网站确认验证流程")
            return None

async def main():
    solver = ImprovedDDAISolver()
    result = await solver.run()

    if result:
        print(f"\n🎊 最终结果: 成功!")
    else:
        print(f"\n😞 最终结果: 失败")

if __name__ == "__main__":
    asyncio.run(main())
