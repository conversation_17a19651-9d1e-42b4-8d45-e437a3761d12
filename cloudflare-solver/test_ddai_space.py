#!/usr/bin/env python3
"""
专门针对 https://app.ddai.space/register 的 Cloudflare 验证处理脚本
"""

import asyncio
import logging
import json
from datetime import datetime
from main import CloudflareSolver

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_ddai_space_register():
    """测试 ddai.space 注册页面的 Cloudflare 验证"""
    
    target_url = "https://app.ddai.space/register"
    
    print("=" * 80)
    print(f"🎯 目标网站: {target_url}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 配置 CloudflareSolver - 针对这个网站优化参数
    solver = CloudflareSolver(
        headless=False,        # 显示浏览器窗口，便于观察过程
        debug=True,            # 启用详细调试日志
        sleep_time=8,          # 增加等待时间，给验证更多时间
        os=["windows"],        # 使用 Windows 指纹
        retries=60             # 增加重试次数
    )
    
    try:
        print("🚀 正在启动 Cloudflare 验证处理...")
        print("📋 配置信息:")
        print(f"   - 无头模式: {solver.headless}")
        print(f"   - 调试模式: {solver.debug}")
        print(f"   - 等待时间: {solver.sleep_time}秒")
        print(f"   - 操作系统: {solver.os}")
        print(f"   - 重试次数: {solver.retries}")
        print()
        
        # 开始处理
        print("🔄 正在处理 Cloudflare 挑战...")
        cookie = await solver.solve(target_url)
        
        if cookie:
            print("\n" + "=" * 80)
            print("✅ 成功绕过 Cloudflare 验证！")
            print("=" * 80)
            print("🍪 获取到的 Cookie 信息:")
            print(f"   名称: {cookie.name}")
            print(f"   值: {cookie.value}")
            print(f"   域名: {cookie.domain}")
            print(f"   路径: {cookie.path}")
            print(f"   过期时间: {cookie.expires}")
            print(f"   安全标志: {cookie.secure}")
            print(f"   HttpOnly: {cookie.http_only}")
            print(f"   SameSite: {cookie.same_site}")
            
            # 保存 cookie 到文件
            cookie_data = {
                "url": target_url,
                "timestamp": datetime.now().isoformat(),
                "cookie": {
                    "name": cookie.name,
                    "value": cookie.value,
                    "domain": cookie.domain,
                    "path": cookie.path,
                    "expires": cookie.expires,
                    "secure": cookie.secure,
                    "http_only": cookie.http_only,
                    "same_site": cookie.same_site
                }
            }
            
            with open("ddai_space_cookie.json", "w", encoding="utf-8") as f:
                json.dump(cookie_data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Cookie 已保存到: ddai_space_cookie.json")
            
            # 显示如何在后续请求中使用这个 cookie
            print("\n📝 使用示例:")
            print("在后续的 HTTP 请求中，可以这样使用获取到的 cookie:")
            print(f"""
import requests

headers = {{
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'Cookie': '{cookie.name}={cookie.value}'
}}

response = requests.get('{target_url}', headers=headers)
print(response.status_code)
            """)
            
        else:
            print("\n" + "=" * 80)
            print("❌ 未能获取到 cf_clearance cookie")
            print("=" * 80)
            print("可能的原因:")
            print("1. 网站没有 Cloudflare 保护")
            print("2. 验证挑战类型不支持")
            print("3. 网络连接问题")
            print("4. 需要调整配置参数")
            
    except Exception as e:
        print("\n" + "=" * 80)
        print("❌ 处理过程中发生错误")
        print("=" * 80)
        print(f"错误信息: {str(e)}")
        print(f"错误类型: {type(e).__name__}")
        
        # 提供故障排除建议
        print("\n🔧 故障排除建议:")
        print("1. 检查网络连接是否正常")
        print("2. 尝试增加 sleep_time 参数")
        print("3. 尝试不同的 os 指纹配置")
        print("4. 检查目标网站是否可正常访问")
        
    finally:
        print(f"\n⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

async def test_with_different_configs():
    """使用不同配置测试，提高成功率"""
    
    target_url = "https://app.ddai.space/register"
    
    # 不同的配置组合
    configs = [
        {
            "name": "配置1: Windows + 长等待",
            "params": {
                "headless": True,
                "debug": True,
                "sleep_time": 10,
                "os": ["windows"],
                "retries": 40
            }
        },
        {
            "name": "配置2: macOS + 中等待",
            "params": {
                "headless": True,
                "debug": True,
                "sleep_time": 6,
                "os": ["macos"],
                "retries": 50
            }
        },
        {
            "name": "配置3: Linux + 短等待",
            "params": {
                "headless": True,
                "debug": True,
                "sleep_time": 4,
                "os": ["linux"],
                "retries": 35
            }
        }
    ]
    
    print("🔄 开始多配置测试...")
    
    for i, config in enumerate(configs, 1):
        print(f"\n{'='*60}")
        print(f"🧪 测试 {i}/3: {config['name']}")
        print(f"{'='*60}")
        
        solver = CloudflareSolver(**config['params'])
        
        try:
            cookie = await solver.solve(target_url)
            
            if cookie:
                print(f"✅ {config['name']} 成功!")
                print(f"Cookie: {cookie.name}={cookie.value[:50]}...")
                return cookie  # 成功后直接返回
            else:
                print(f"❌ {config['name']} 失败")
                
        except Exception as e:
            print(f"❌ {config['name']} 出错: {e}")
        
        # 配置间等待
        if i < len(configs):
            print("⏳ 等待 5 秒后尝试下一个配置...")
            await asyncio.sleep(5)
    
    print("\n❌ 所有配置都未能成功获取 cookie")
    return None

async def main():
    """主函数"""
    print("🎯 DDAI Space Cloudflare 验证处理工具")
    print("=" * 80)
    
    # 首先尝试标准配置
    print("📋 步骤 1: 使用标准配置测试")
    await test_ddai_space_register()
    
    print("\n" + "="*80)
    
    # 如果需要，可以尝试多配置测试
    user_input = input("是否要尝试多配置测试？(y/n): ").lower().strip()
    
    if user_input == 'y':
        print("\n📋 步骤 2: 多配置测试")
        await test_with_different_configs()
    
    print("\n🎉 测试完成！")
    print("如果成功获取了 cookie，可以在后续的 HTTP 请求中使用它来绕过 Cloudflare 保护。")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
