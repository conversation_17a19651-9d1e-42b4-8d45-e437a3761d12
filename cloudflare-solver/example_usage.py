#!/usr/bin/env python3
"""
Cloudflare Solver 使用示例
这个脚本演示了如何使用 CloudflareSolver 来绕过 Cloudflare 的反机器人验证
"""

import asyncio
import logging
from main import CloudflareSolver

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def basic_example():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 创建 CloudflareSolver 实例
    solver = CloudflareSolver(
        headless=True,  # 无头模式运行
        debug=True,     # 启用调试日志
        sleep_time=5,   # 点击验证前等待5秒
        retries=30      # 最多重试30次
    )
    
    # 测试网站 - 这个网站有 Cloudflare 保护
    test_url = "https://2ip.pro"
    
    try:
        print(f"正在尝试解决 {test_url} 的 Cloudflare 挑战...")
        cookie = await solver.solve(test_url)
        
        if cookie:
            print("✅ 成功获取 cf_clearance cookie!")
            print(f"Cookie 名称: {cookie.name}")
            print(f"Cookie 值: {cookie.value}")
            print(f"域名: {cookie.domain}")
            print(f"路径: {cookie.path}")
            print(f"过期时间: {cookie.expires}")
            print(f"安全标志: {cookie.secure}")
            print(f"HttpOnly: {cookie.http_only}")
            print(f"SameSite: {cookie.same_site}")
        else:
            print("❌ 未能获取 cookie")
            
    except Exception as e:
        print(f"❌ 发生错误: {e}")

async def advanced_example():
    """高级配置示例"""
    print("\n=== 高级配置示例 ===")
    
    # 使用更多配置选项
    solver = CloudflareSolver(
        headless=False,           # 显示浏览器窗口（用于调试）
        debug=True,               # 启用详细日志
        sleep_time=3,             # 等待时间
        os=["macos"],            # 模拟 macOS 系统
        retries=50               # 增加重试次数
    )
    
    test_url = "https://httpbin.org/ip"  # 另一个测试网站
    
    try:
        print(f"正在使用高级配置解决 {test_url}...")
        cookie = await solver.solve(test_url)
        
        if cookie:
            print("✅ 高级配置成功!")
            print(f"获取到的 cookie: {cookie.name}={cookie.value}")
        else:
            print("❌ 高级配置未能获取 cookie")
            
    except Exception as e:
        print(f"❌ 高级配置发生错误: {e}")

async def multiple_sites_example():
    """多个网站示例"""
    print("\n=== 多个网站处理示例 ===")
    
    solver = CloudflareSolver(headless=True, debug=False)
    
    # 要测试的网站列表
    test_sites = [
        "https://httpbin.org/ip",
        "https://httpbin.org/user-agent",
        "https://httpbin.org/headers"
    ]
    
    for site in test_sites:
        try:
            print(f"处理网站: {site}")
            cookie = await solver.solve(site)
            
            if cookie:
                print(f"  ✅ 成功: {cookie.name}")
            else:
                print(f"  ❌ 失败")
                
        except Exception as e:
            print(f"  ❌ 错误: {e}")
        
        # 在处理下一个网站前稍作等待
        await asyncio.sleep(2)

def print_usage_info():
    """打印使用说明"""
    print("=" * 60)
    print("Cloudflare Challenge TurnStile Solver 使用说明")
    print("=" * 60)
    print()
    print("主要功能:")
    print("• 自动绕过 Cloudflare 的 Turnstile 验证挑战")
    print("• 获取 cf_clearance cookie 用于后续请求")
    print("• 支持异步操作，性能优异")
    print("• 使用隐蔽的浏览器指纹，不易被检测")
    print()
    print("配置参数:")
    print("• headless: 是否无头模式运行 (默认: True)")
    print("• debug: 是否启用调试日志 (默认: False)")
    print("• sleep_time: 点击验证前等待时间 (默认: 3秒)")
    print("• os: 操作系统指纹 (默认: ['windows'])")
    print("• retries: 最大重试次数 (默认: 30)")
    print()
    print("使用场景:")
    print("• 网页爬虫需要绕过 Cloudflare 保护")
    print("• 自动化测试需要访问受保护的网站")
    print("• API 调用需要有效的 cf_clearance cookie")
    print()
    print("注意事项:")
    print("• 请遵守网站的使用条款和法律法规")
    print("• 不要用于恶意目的或过度请求")
    print("• 建议在使用前阅读 LEGAL_NOTICE.md")
    print("=" * 60)

async def main():
    """主函数"""
    print_usage_info()
    
    # 运行各种示例
    await basic_example()
    
    # 注释掉高级示例，因为它会显示浏览器窗口
    # await advanced_example()
    
    await multiple_sites_example()
    
    print("\n=== 所有示例运行完成 ===")
    print("如需更多帮助，请查看 README.md 或源代码注释")

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
