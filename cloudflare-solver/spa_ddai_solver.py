#!/usr/bin/env python3
"""
SPA DDAI Space Cloudflare Turnstile 解决器
专门处理单页应用中动态加载的 Turnstile 挑战
"""

import asyncio
import logging
import json
from datetime import datetime
from playwright.async_api import async_playwright

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SPADDAISolver:
    def __init__(self):
        self.target_url = "https://app.ddai.space/register"
        
    async def solve_spa_turnstile(self):
        """处理 SPA 中的 Turnstile 挑战"""
        print("🎭 SPA Turnstile 挑战处理器")
        print("=" * 60)
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=False,  # 显示浏览器便于观察
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-dev-shm-usage',
                    '--disable-extensions',
                    '--no-first-run',
                    '--disable-default-apps'
                ]
            )
            
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                viewport={'width': 1920, 'height': 1080},
                java_script_enabled=True
            )
            
            page = await context.new_page()
            
            # 监听网络请求
            requests_log = []
            
            async def log_request(request):
                if 'cloudflare' in request.url or 'turnstile' in request.url:
                    requests_log.append({
                        'url': request.url,
                        'method': request.method,
                        'timestamp': datetime.now().isoformat()
                    })
                    print(f"🌐 Cloudflare 请求: {request.method} {request.url}")
            
            page.on('request', log_request)
            
            try:
                print(f"📍 导航到: {self.target_url}")
                await page.goto(self.target_url, wait_until='networkidle')
                
                # 等待 SPA 应用加载
                print("⏳ 等待 SPA 应用加载...")
                await asyncio.sleep(5)
                
                # 等待可能的 Turnstile 脚本加载
                print("🔍 等待 Turnstile 脚本加载...")
                
                # 尝试等待 Turnstile 相关元素
                turnstile_selectors = [
                    'iframe[src*="challenges.cloudflare.com"]',
                    '[data-cf-turnstile]',
                    '.cf-turnstile',
                    '#cf-turnstile',
                    'div[id*="turnstile"]',
                    'div[class*="turnstile"]'
                ]
                
                turnstile_found = False
                turnstile_element = None
                
                # 等待最多 30 秒寻找 Turnstile 元素
                for attempt in range(30):
                    for selector in turnstile_selectors:
                        try:
                            elements = await page.query_selector_all(selector)
                            if elements:
                                turnstile_element = elements[0]
                                print(f"✅ 找到 Turnstile 元素: {selector}")
                                turnstile_found = True
                                break
                        except:
                            continue
                    
                    if turnstile_found:
                        break
                    
                    if attempt % 5 == 0:
                        print(f"⏳ 继续寻找 Turnstile... ({attempt+1}/30秒)")
                    
                    await asyncio.sleep(1)
                
                if not turnstile_found:
                    print("❌ 未找到 Turnstile 挑战元素")
                    
                    # 尝试查找其他可能的验证元素
                    print("🔍 查找其他验证元素...")
                    
                    # 检查页面内容
                    content = await page.content()
                    
                    # 保存当前页面内容用于分析
                    with open("spa_page_content.html", "w", encoding="utf-8") as f:
                        f.write(content)
                    print("💾 页面内容已保存到: spa_page_content.html")
                    
                    # 截图当前状态
                    await page.screenshot(path="spa_current_state.png", full_page=True)
                    print("📸 当前状态截图: spa_current_state.png")
                    
                    # 检查是否有其他形式的验证
                    verification_indicators = [
                        'captcha', 'recaptcha', 'hcaptcha', 'verification', 
                        'challenge', 'security', 'protection'
                    ]
                    
                    found_indicators = []
                    for indicator in verification_indicators:
                        if indicator in content.lower():
                            found_indicators.append(indicator)
                    
                    if found_indicators:
                        print(f"🔍 发现可能的验证指示器: {found_indicators}")
                    
                    return None
                
                # 如果找到了 Turnstile 元素
                print("🖱️ 准备与 Turnstile 交互...")
                
                # 确保元素可见
                await turnstile_element.scroll_into_view_if_needed()
                await asyncio.sleep(2)
                
                # 获取元素边界框
                box = await turnstile_element.bounding_box()
                if box:
                    print(f"📏 Turnstile 位置: x={box['x']}, y={box['y']}, width={box['width']}, height={box['height']}")
                
                # 尝试点击 Turnstile
                try:
                    await turnstile_element.click()
                    print("✅ 已点击 Turnstile 挑战")
                except Exception as e:
                    print(f"⚠️ 点击失败，尝试其他方法: {e}")
                    
                    # 尝试使用坐标点击
                    if box:
                        center_x = box['x'] + box['width'] / 2
                        center_y = box['y'] + box['height'] / 2
                        await page.mouse.click(center_x, center_y)
                        print(f"✅ 使用坐标点击: ({center_x}, {center_y})")
                
                # 等待验证处理
                print("⏳ 等待验证处理...")
                
                # 监控验证状态
                verification_completed = False
                
                for wait_time in range(60):  # 最多等待 60 秒
                    await asyncio.sleep(1)
                    
                    # 检查 cookies
                    cookies = await context.cookies()
                    cf_clearance = next((c for c in cookies if c['name'] == 'cf_clearance'), None)
                    
                    if cf_clearance:
                        print(f"✅ 验证完成! (第 {wait_time+1} 秒)")
                        verification_completed = True
                        break
                    
                    # 检查页面变化
                    try:
                        current_url = page.url
                        if current_url != self.target_url:
                            print(f"🔄 页面跳转到: {current_url}")
                    except:
                        pass
                    
                    # 每 10 秒报告一次状态
                    if wait_time % 10 == 9:
                        print(f"⏳ 等待验证... ({wait_time+1}/60秒)")
                        
                        # 截图当前状态
                        await page.screenshot(path=f"verification_progress_{wait_time+1}s.png")
                
                # 获取最终的 cookies
                cookies = await context.cookies()
                
                print(f"\n🍪 最终获取到 {len(cookies)} 个 cookies:")
                cf_clearance = None
                
                for cookie in cookies:
                    print(f"   {cookie['name']}: {cookie['value'][:50]}...")
                    if cookie['name'] == 'cf_clearance':
                        cf_clearance = cookie
                
                # 保存网络请求日志
                if requests_log:
                    with open("cloudflare_requests.json", "w", encoding="utf-8") as f:
                        json.dump(requests_log, f, indent=2, ensure_ascii=False)
                    print(f"📝 网络请求日志已保存: cloudflare_requests.json")
                
                return cf_clearance
                
            except Exception as e:
                print(f"❌ 处理过程中出错: {e}")
                
                # 保存错误时的状态
                try:
                    await page.screenshot(path="error_state.png", full_page=True)
                    print("📸 错误状态截图: error_state.png")
                except:
                    pass
                
                return None
            finally:
                await browser.close()
    
    def save_result(self, cookie):
        """保存结果"""
        if not cookie:
            print("❌ 没有 cookie 可保存")
            return
        
        result = {
            "url": self.target_url,
            "timestamp": datetime.now().isoformat(),
            "method": "SPA Turnstile Solver",
            "cookie": {
                "name": cookie['name'],
                "value": cookie['value'],
                "domain": cookie['domain'],
                "path": cookie['path'],
                "expires": cookie.get('expires'),
                "secure": cookie.get('secure'),
                "http_only": cookie.get('httpOnly'),
                "same_site": cookie.get('sameSite')
            }
        }
        
        filename = f"ddai_spa_success_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"💾 结果已保存到: {filename}")
        
        # 显示使用示例
        print(f"\n📝 使用示例:")
        print(f"Cookie: cf_clearance={cookie['value']}")
        print(f"\ncurl 命令:")
        print(f"curl -H 'Cookie: cf_clearance={cookie['value']}' '{self.target_url}'")
        print(f"\nPython requests:")
        print(f"headers = {{'Cookie': 'cf_clearance={cookie['value']}'}}")
        print(f"response = requests.get('{self.target_url}', headers=headers)")
    
    async def run(self):
        """运行 SPA 解决器"""
        print("🎯 SPA DDAI Space Cloudflare Turnstile 解决器")
        print("=" * 80)
        print(f"🌐 目标: {self.target_url}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        cookie = await self.solve_spa_turnstile()
        
        if cookie:
            print("\n🎉 成功获取 cf_clearance cookie!")
            self.save_result(cookie)
        else:
            print("\n❌ 未能获取 cf_clearance cookie")
            print("\n💡 故障排除建议:")
            print("1. 检查生成的截图文件了解当前状态")
            print("2. 查看 spa_page_content.html 了解页面结构")
            print("3. 检查 cloudflare_requests.json 了解网络请求")
            print("4. 确认网站是否需要登录或其他前置条件")
            print("5. 尝试手动访问网站确认验证流程")
        
        print(f"\n⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

async def main():
    solver = SPADDAISolver()
    await solver.run()

if __name__ == "__main__":
    asyncio.run(main())
