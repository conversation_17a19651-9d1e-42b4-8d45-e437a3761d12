#!/usr/bin/env python3
"""
智能批量测试

基于网络状况智能调整的批量测试方案
"""

import asyncio
import json
import time
import requests
from datetime import datetime
from main import CloudflareSolver

class SmartBatchTest:
    """智能批量测试框架"""
    
    def __init__(self):
        self.target_url = "https://app.ddai.space/register"
        self.results = []
        
    def check_network_status(self):
        """检查网络状态"""
        print("🌐 检查网络状态...")
        
        try:
            start_time = time.time()
            response = requests.get(self.target_url, timeout=10)
            end_time = time.time()
            duration = end_time - start_time
            
            if response.status_code == 200 and duration < 5:
                print(f"✅ 网络状态良好 (响应时间: {duration:.2f}s)")
                return "good"
            elif response.status_code == 200:
                print(f"⚠️  网络状态一般 (响应时间: {duration:.2f}s)")
                return "fair"
            else:
                print(f"❌ 网络状态不佳 (状态码: {response.status_code})")
                return "poor"
                
        except Exception as e:
            print(f"❌ 网络检查失败: {e}")
            return "poor"
    
    async def adaptive_test(self, test_count=20):
        """自适应测试"""
        
        print(f"🧠 智能批量测试 ({test_count}次)")
        print("=" * 50)
        
        # 检查网络状态
        network_status = self.check_network_status()
        
        # 根据网络状态调整参数
        if network_status == "good":
            interval = 10  # 10秒间隔
            timeout_multiplier = 1.0
            print("🚀 网络良好，使用标准配置")
        elif network_status == "fair":
            interval = 30  # 30秒间隔
            timeout_multiplier = 1.5
            print("⚠️  网络一般，使用保守配置")
        else:
            interval = 60  # 60秒间隔
            timeout_multiplier = 2.0
            print("🐌 网络不佳，使用超保守配置")
        
        print(f"⚙️  配置: 间隔{interval}秒, 超时倍数{timeout_multiplier}")
        print("=" * 50)
        
        success_count = 0
        
        for i in range(1, test_count + 1):
            print(f"\n🚀 第 {i}/{test_count} 次测试")
            
            # 创建solver
            solver = CloudflareSolver(
                headless=False,
                sleep_time=int(6 * timeout_multiplier),
                os=["macos"],
                debug=False,
                retries=int(80 * timeout_multiplier)
            )
            
            start_time = time.time()
            
            try:
                cookie = await solver.solve(self.target_url)
                end_time = time.time()
                duration = end_time - start_time
                
                if cookie:
                    success_count += 1
                    print(f"✅ 成功! 耗时: {duration:.2f}s")
                    
                    result = {
                        'test_num': i,
                        'success': True,
                        'duration': duration,
                        'network_status': network_status,
                        'timestamp': datetime.now().isoformat()
                    }
                else:
                    print(f"❌ 失败! 耗时: {duration:.2f}s")
                    result = {
                        'test_num': i,
                        'success': False,
                        'duration': duration,
                        'network_status': network_status,
                        'timestamp': datetime.now().isoformat()
                    }
                
                self.results.append(result)
                
                # 显示当前统计
                current_rate = (success_count / i) * 100
                print(f"📊 当前成功率: {success_count}/{i} = {current_rate:.1f}%")
                
                # 如果不是最后一次，等待间隔时间
                if i < test_count:
                    print(f"⏳ 等待 {interval} 秒...")
                    await asyncio.sleep(interval)
                
            except Exception as e:
                print(f"❌ 错误: {str(e)[:50]}...")
                result = {
                    'test_num': i,
                    'success': False,
                    'duration': 0,
                    'network_status': network_status,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
                self.results.append(result)
        
        return self.results
    
    def analyze_results(self):
        """分析结果"""
        
        print(f"\n📊 测试结果分析")
        print("=" * 50)
        
        total_tests = len(self.results)
        successful_tests = [r for r in self.results if r['success']]
        success_count = len(successful_tests)
        success_rate = (success_count / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"总测试次数: {total_tests}")
        print(f"成功次数: {success_count}")
        print(f"成功率: {success_rate:.1f}%")
        
        if successful_tests:
            durations = [r['duration'] for r in successful_tests]
            avg_duration = sum(durations) / len(durations)
            print(f"平均耗时: {avg_duration:.2f}秒")
            print(f"时间范围: {min(durations):.2f}s - {max(durations):.2f}s")
        
        # 保存结果
        filename = f"smart_batch_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'test_type': 'smart_batch_test',
                'total_tests': total_tests,
                'success_count': success_count,
                'success_rate': success_rate,
                'results': self.results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"💾 结果已保存: {filename}")
        
        return {
            'success_rate': success_rate,
            'avg_duration': avg_duration if successful_tests else 0,
            'total_tests': total_tests
        }

async def main():
    """主函数"""
    
    print("🧠 智能批量测试")
    print("=" * 50)
    print("这个测试会根据网络状况自动调整参数")
    print("预计进行20次测试，耗时10-30分钟")
    print("=" * 50)
    
    try:
        confirm = input("开始智能批量测试? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("❌ 测试已取消")
            return
        
        tester = SmartBatchTest()
        results = await tester.adaptive_test(20)  # 20次测试
        summary = tester.analyze_results()
        
        print(f"\n🎉 智能批量测试完成!")
        print(f"🏆 最终成功率: {summary['success_rate']:.1f}%")
        
        # 与历史数据对比
        print(f"\n📈 与历史数据对比:")
        print(f"历史小规模测试: 100% (5/5)")
        print(f"本次批量测试: {summary['success_rate']:.1f}% ({summary['total_tests']}次)")
        
        if summary['success_rate'] >= 90:
            print("✅ 批量测试验证了有头模式的高可靠性!")
        elif summary['success_rate'] >= 75:
            print("✅ 批量测试显示有头模式表现良好!")
        else:
            print("⚠️  批量测试发现了一些网络相关的问题")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())
