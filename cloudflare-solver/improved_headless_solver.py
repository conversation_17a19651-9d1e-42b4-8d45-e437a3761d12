#!/usr/bin/env python3
"""
改进的无头模式 DDAI Space Cloudflare 绕过脚本

针对无头模式进行了特别优化，提高成功率和稳定性。

使用方法：
    python3 improved_headless_solver.py
"""

import asyncio
import json
import logging
import requests
from datetime import datetime
from main import CloudflareSolver

# 配置详细的日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ImprovedHeadlessSolver:
    """改进的无头模式解决器"""
    
    def __init__(self):
        self.target_url = "https://app.ddai.space/register"
        self.max_attempts = 5  # 增加尝试次数
        
    def print_header(self):
        """打印程序头部"""
        print("🔧 改进的无头模式 DDAI Space Cloudflare 绕过工具")
        print("=" * 60)
        print(f"🎯 目标: {self.target_url}")
        print(f"⏰ 开始: {datetime.now().strftime('%H:%M:%S')}")
        print("🔇 无头模式: 已启用 (改进版)")
        print("=" * 60)
    
    async def solve_with_optimized_headless(self, attempt=1):
        """使用优化的无头模式配置"""
        
        print(f"🚀 第 {attempt} 次尝试 (优化无头模式)...")
        
        # 针对无头模式的特别优化配置
        configs = [
            {
                'name': '标准无头配置',
                'sleep_time': 8,
                'retries': 100,
                'os': ['macos']
            },
            {
                'name': 'Windows指纹配置',
                'sleep_time': 10,
                'retries': 120,
                'os': ['windows']
            },
            {
                'name': '延长等待配置',
                'sleep_time': 12,
                'retries': 150,
                'os': ['linux']
            }
        ]
        
        # 根据尝试次数选择不同配置
        config_index = min(attempt - 1, len(configs) - 1)
        config = configs[config_index]
        
        print(f"📋 使用配置: {config['name']}")
        
        solver = CloudflareSolver(
            sleep_time=config['sleep_time'],
            headless=True,
            os=config['os'],
            debug=True,  # 启用调试以了解失败原因
            retries=config['retries']
        )
        
        try:
            logger.info(f"开始第 {attempt} 次无头模式访问")
            
            start_time = asyncio.get_event_loop().time()
            cookie = await solver.solve(self.target_url)
            end_time = asyncio.get_event_loop().time()
            duration = end_time - start_time
            
            if cookie:
                print(f"✅ 成功! 耗时: {duration:.2f}s")
                print(f"🍪 Cookie: {cookie.value[:50]}...")
                
                # 保存结果
                cookie_data = {
                    'name': cookie.name,
                    'value': cookie.value,
                    'domain': cookie.domain,
                    'path': cookie.path,
                    'expires': cookie.expires,
                    'secure': cookie.secure,
                    'http_only': cookie.http_only,
                    'same_site': cookie.same_site,
                    'timestamp': datetime.now().isoformat(),
                    'duration': duration,
                    'mode': 'improved_headless',
                    'attempt': attempt,
                    'config': config['name']
                }
                
                filename = f"ddai_improved_headless_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(cookie_data, f, indent=2, ensure_ascii=False)
                
                print(f"💾 已保存: {filename}")
                
                # 验证 cookie
                if await self.verify_cookie(cookie):
                    print("✅ Cookie 验证成功")
                    self.generate_usage_script(cookie)
                    return cookie
                else:
                    print("⚠️  Cookie 验证失败，继续尝试...")
                    return None
                
            else:
                print(f"❌ 失败 (耗时: {duration:.2f}s)")
                logger.warning(f"第 {attempt} 次尝试失败，配置: {config['name']}")
                return None
                
        except Exception as e:
            logger.error(f"第 {attempt} 次尝试出错: {e}")
            print(f"❌ 错误: {str(e)}")
            return None
    
    async def verify_cookie(self, cookie):
        """验证 cookie 有效性"""
        try:
            session = requests.Session()
            session.cookies.set(
                cookie.name,
                cookie.value,
                domain=cookie.domain,
                path=cookie.path
            )
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
            
            response = session.get(self.target_url, headers=headers, timeout=15)
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"Cookie 验证错误: {e}")
            return False
    
    def generate_usage_script(self, cookie):
        """生成使用脚本"""
        script_content = f'''#!/usr/bin/env python3
"""
改进无头模式获取的 DDAI Space Cookie 使用脚本
"""

import requests

def use_ddai_cookie():
    session = requests.Session()
    session.cookies.set(
        '{cookie.name}',
        '{cookie.value}',
        domain='{cookie.domain}',
        path='{cookie.path}'
    )
    
    headers = {{
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
    }}
    
    response = session.get('{self.target_url}', headers=headers)
    print(f"状态码: {{response.status_code}}")
    print(f"页面长度: {{len(response.text)}}")
    
    return response

if __name__ == "__main__":
    use_ddai_cookie()
'''
        
        with open('ddai_improved_headless_usage.py', 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print("📝 使用脚本: ddai_improved_headless_usage.py")
    
    async def run(self):
        """运行改进的无头模式主程序"""
        self.print_header()
        
        success = False
        
        for attempt in range(1, self.max_attempts + 1):
            cookie = await self.solve_with_optimized_headless(attempt)
            
            if cookie:
                success = True
                break
            else:
                if attempt < self.max_attempts:
                    wait_time = min(5 + attempt * 2, 15)  # 递增等待时间
                    print(f"⏳ 等待 {wait_time}s 后重试...")
                    await asyncio.sleep(wait_time)
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 改进无头模式任务完成!")
            print("✅ 成功获取有效 Cookie")
            print("✅ 优化配置工作正常")
        else:
            print("❌ 改进无头模式任务失败")
            print("💡 可能的原因:")
            print("   • 网络连接不稳定")
            print("   • Cloudflare 检测策略更新")
            print("   • 需要调整配置参数")
            print("💡 建议:")
            print("   • 检查网络连接")
            print("   • 稍后重试")
            print("   • 尝试使用有头模式调试")
        
        print("=" * 60)

def main():
    """主函数"""
    try:
        solver = ImprovedHeadlessSolver()
        asyncio.run(solver.run())
    except KeyboardInterrupt:
        print("\n⏹️  程序中断")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")

if __name__ == "__main__":
    main()
