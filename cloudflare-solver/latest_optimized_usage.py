#!/usr/bin/env python3
"""
最新优化版无头模式获取的 Cookie 使用示例
版本: v2.0_optimized
"""

import requests

def use_latest_optimized_cookie():
    """使用最新优化版获取的 cookie"""
    
    print("🚀 使用最新优化版无头模式获取的 Cookie")
    
    # 创建会话
    session = requests.Session()
    
    # 设置 cookie
    session.cookies.set(
        'cf_clearance',
        'Xb2rmtlhrFHQ5iS5hMvB3ZGHDzsEMC3OlCVucQja_XM-1752903058-1.2.1.1-BZQ6bq5NXFrHhalFjCteVSCUlXYDwWN5c.oxtSDDvpRvktuvRDe5Noq_GJ.01YUMDj2lxNdjUgLQYpUYW2qqj_kTDQyP3fe3z2A92fCT30OQN6S6MWfYStDeLXWO_274x0P8SguO2U00h14ENoO3a69VZvGg0KfknKaoJ.66U7uLjMqE9En67WR2.kZ6WcpOAn9KxDDDq6s172KSDDIC9zwuckhXW_PMmWh3eANEdhQ',
        domain='.ddai.space',
        path='/'
    )
    
    # 设置完整的请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0'
    }
    
    try:
        # 访问目标网站
        response = session.get('https://app.ddai.space/register', headers=headers, timeout=15)
        
        print(f"状态码: {response.status_code}")
        print(f"页面长度: {len(response.text)} 字符")
        
        if response.status_code == 200:
            print("✅ 成功使用最新优化版无头模式获取的 Cookie!")
            print("🎯 可以进行后续的业务操作")
            return response
        else:
            print(f"❌ 访问失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 使用 Cookie 时出错: {e}")
        return None

if __name__ == "__main__":
    result = use_latest_optimized_cookie()
    if result:
        print("🎉 最新优化版 Cookie 使用成功!")
    else:
        print("❌ Cookie 使用失败")
