#!/usr/bin/env python3
"""
测试真实的 Cloudflare 保护网站

这个脚本用于测试 cloudflare-solver 在真实的 Cloudflare 保护网站上的表现。
"""

import asyncio
import sys
from main import CloudflareSolver

async def test_real_cloudflare_site():
    """测试真实的 Cloudflare 保护网站"""
    
    # 一些可能有 Cloudflare 保护的网站
    test_sites = [
        "https://nowsecure.nl",  # 已知有 Cloudflare 保护的测试网站
        "https://2ip.pro",       # 可能有 Cloudflare 保护
    ]
    
    print("🛡️  测试真实的 Cloudflare 保护网站")
    print("=" * 50)
    
    for site in test_sites:
        print(f"\n🌐 测试网站: {site}")
        print("-" * 30)
        
        # 创建 solver 实例
        solver = CloudflareSolver(
            sleep_time=5,        # 更长的等待时间
            headless=False,      # 显示浏览器以便观察
            os=["macos"],        # 使用 macOS 指纹
            debug=True,          # 启用调试
            retries=60           # 更多重试次数
        )
        
        try:
            print("⏳ 正在尝试解决 Cloudflare 挑战...")
            cookie = await solver.solve(site)
            
            if cookie:
                print("🎉 成功绕过 Cloudflare 保护!")
                print(f"✅ Cookie 名称: {cookie.name}")
                print(f"✅ Cookie 值: {cookie.value[:50]}...")
                print(f"✅ 域名: {cookie.domain}")
                print(f"✅ 过期时间: {cookie.expires}")
                
                # 验证 cookie 是否有效
                print("\n🔍 验证 cookie 有效性...")
                import requests
                
                session = requests.Session()
                session.cookies.set(
                    cookie.name,
                    cookie.value,
                    domain=cookie.domain
                )
                
                try:
                    response = session.get(site, timeout=10)
                    if response.status_code == 200:
                        print("✅ Cookie 验证成功 - 可以正常访问网站")
                    else:
                        print(f"⚠️  Cookie 可能无效 - HTTP 状态码: {response.status_code}")
                except Exception as e:
                    print(f"⚠️  Cookie 验证失败: {e}")
                    
            else:
                print("❌ 未能获取 cf_clearance cookie")
                print("可能原因:")
                print("- 网站当前没有启用 Cloudflare 挑战")
                print("- 挑战类型不受支持")
                print("- 网络或其他技术问题")
                
        except KeyboardInterrupt:
            print("\n⏹️  用户中断测试")
            sys.exit(0)
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
            
        print("\n" + "=" * 50)

async def interactive_test():
    """交互式测试 - 允许用户输入自定义 URL"""
    
    print("\n🔧 交互式测试模式")
    print("请输入要测试的网站 URL（按 Enter 跳过）:")
    
    try:
        custom_url = input("URL: ").strip()
        
        if custom_url:
            if not custom_url.startswith(('http://', 'https://')):
                custom_url = 'https://' + custom_url
                
            print(f"\n🎯 测试自定义网站: {custom_url}")
            
            solver = CloudflareSolver(
                sleep_time=3,
                headless=False,
                debug=True,
                retries=30
            )
            
            cookie = await solver.solve(custom_url)
            
            if cookie:
                print("🎉 自定义网站测试成功!")
                print(f"Cookie: {cookie.name} = {cookie.value[:30]}...")
            else:
                print("❌ 自定义网站测试失败")
        else:
            print("⏭️  跳过自定义测试")
            
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"❌ 自定义测试错误: {e}")

def main():
    """主函数"""
    print("🚀 Cloudflare Solver 真实网站测试")
    print("此测试将尝试访问真实的 Cloudflare 保护网站")
    print("请确保网络连接正常\n")
    
    try:
        # 运行真实网站测试
        asyncio.run(test_real_cloudflare_site())
        
        # 运行交互式测试
        asyncio.run(interactive_test())
        
    except KeyboardInterrupt:
        print("\n👋 测试已中断")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
    
    print("\n✨ 测试完成!")
    print("\n💡 提示:")
    print("- 如果没有获取到 cookie，可能是网站当前没有 Cloudflare 保护")
    print("- 某些网站可能使用更复杂的验证方式")
    print("- 建议在不同时间重试，因为 Cloudflare 保护可能会动态调整")

if __name__ == "__main__":
    main()
