#!/usr/bin/env python3
"""
终极版 DDAI Space Cloudflare 解决器
基于之前的观察，使用最优化的策略
"""

import asyncio
import logging
import json
from datetime import datetime
from playwright.async_api import async_playwright

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UltimateDDAISolver:
    def __init__(self):
        self.target_url = "https://app.ddai.space/register"
        self.success_indicators = []
        
    async def solve_with_patience(self):
        """使用耐心策略解决 Turnstile"""
        print("🎯 终极版 DDAI Space Cloudflare 解决器")
        print("=" * 80)
        print(f"🌐 目标: {self.target_url}")
        print(f"⏰ 开始: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🔥 使用最优化策略，增加成功率")
        print("=" * 80)
        
        async with async_playwright() as p:
            # 使用更稳定的浏览器配置
            browser = await p.chromium.launch(
                headless=False,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-dev-shm-usage',
                    '--disable-extensions',
                    '--no-first-run',
                    '--disable-default-apps',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-features=TranslateUI',
                    '--disable-ipc-flooding-protection',

                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                viewport={'width': 1920, 'height': 1080},
                java_script_enabled=True,
                ignore_https_errors=True,
                extra_http_headers={
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                }
            )
            
            page = await context.new_page()
            
            # 监听网络活动
            cf_requests = []
            
            async def log_request(request):
                if any(keyword in request.url.lower() for keyword in ['cloudflare', 'turnstile', 'challenge']):
                    cf_requests.append({
                        'url': request.url,
                        'method': request.method,
                        'timestamp': datetime.now().isoformat()
                    })
                    print(f"🌐 {request.method} {request.url}")
            
            page.on('request', log_request)
            
            try:
                print("📍 正在访问目标页面...")
                
                # 分步骤加载页面
                await page.goto(self.target_url, wait_until='domcontentloaded', timeout=120000)
                print("✅ DOM 加载完成")
                
                # 等待 JavaScript 执行
                print("⏳ 等待 JavaScript 和 Turnstile 加载...")
                await asyncio.sleep(15)  # 给足够时间让 Turnstile 加载
                
                # 开始处理 Turnstile
                cookie = await self.handle_turnstile_comprehensive(page, context)
                
                if cookie:
                    return cookie
                
                # 如果第一次失败，再试一次
                print("🔄 第一次尝试未成功，重新尝试...")
                await asyncio.sleep(10)
                cookie = await self.handle_turnstile_comprehensive(page, context)
                
                return cookie
                
            except Exception as e:
                print(f"⚠️ 页面加载异常: {e}")
                print("🔄 尝试处理当前状态...")
                
                # 即使有异常，也尝试处理
                try:
                    await asyncio.sleep(10)
                    cookie = await self.handle_turnstile_comprehensive(page, context)
                    return cookie
                except:
                    pass
                
                return None
            finally:
                # 保存最终状态
                try:
                    await page.screenshot(path="ultimate_final_state.png", full_page=True)
                    print("📸 最终状态截图: ultimate_final_state.png")
                    
                    if cf_requests:
                        with open("ultimate_cf_requests.json", "w", encoding="utf-8") as f:
                            json.dump(cf_requests, f, indent=2, ensure_ascii=False)
                        print(f"📝 记录了 {len(cf_requests)} 个 Cloudflare 请求")
                except:
                    pass
                
                await browser.close()
    
    async def handle_turnstile_comprehensive(self, page, context):
        """综合处理 Turnstile 挑战"""
        print("🔍 开始综合处理 Turnstile...")
        
        # 扩展的选择器列表
        selectors = [
            '#cf-turnstile',
            '[data-cf-turnstile]',
            '.cf-turnstile',
            'iframe[src*="challenges.cloudflare.com"]',
            'iframe[src*="turnstile"]',
            'div[id*="turnstile"]',
            'div[class*="turnstile"]',
            'div[data-sitekey]',
            '[data-callback]',
            '.challenge-container',
            '#challenge'
        ]
        
        # 多轮尝试
        for round_num in range(3):
            print(f"🔄 第 {round_num + 1} 轮处理...")
            
            # 查找元素
            turnstile_element = None
            found_selector = None
            
            for selector in selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        # 选择第一个可见的元素
                        for element in elements:
                            is_visible = await element.is_visible()
                            if is_visible:
                                turnstile_element = element
                                found_selector = selector
                                print(f"✅ 找到可见的 Turnstile: {selector}")
                                break
                        if turnstile_element:
                            break
                except:
                    continue
            
            if turnstile_element:
                # 处理找到的元素
                success = await self.interact_with_element(page, context, turnstile_element, found_selector)
                if success:
                    return success
            else:
                print(f"❌ 第 {round_num + 1} 轮未找到 Turnstile 元素")
            
            # 轮次间等待
            if round_num < 2:
                print("⏳ 等待 10 秒后进行下一轮...")
                await asyncio.sleep(10)
        
        return None
    
    async def interact_with_element(self, page, context, element, selector):
        """与元素交互"""
        print(f"🎯 与元素交互: {selector}")
        
        try:
            # 滚动到元素
            await element.scroll_into_view_if_needed()
            await asyncio.sleep(3)
            
            # 获取元素信息
            box = await element.bounding_box()
            if box:
                print(f"📏 元素位置: {box}")
            
            # 根据元素类型采用不同策略
            if 'iframe' in selector.lower():
                await self.handle_iframe_challenge(page, element)
            else:
                # 直接点击
                await element.click()
                print("✅ 点击了元素")
            
            # 等待验证完成 - 使用更长的等待时间
            return await self.wait_for_success(page, context)
            
        except Exception as e:
            print(f"❌ 交互失败: {e}")
            return None
    
    async def handle_iframe_challenge(self, page, iframe_element):
        """处理 iframe 挑战"""
        print("🖼️ 处理 iframe 挑战...")
        
        try:
            await asyncio.sleep(5)  # 等待 iframe 完全加载
            
            frame = await iframe_element.content_frame()
            if frame:
                print("✅ 获取到 iframe 内容")
                
                # 在 iframe 内查找可点击元素
                iframe_selectors = [
                    'input[type="checkbox"]',
                    '.cb-i',
                    '.recaptcha-checkbox',
                    'div[role="checkbox"]',
                    'span[role="checkbox"]',
                    'button',
                    'div[tabindex="0"]'
                ]
                
                for selector in iframe_selectors:
                    try:
                        elements = await frame.query_selector_all(selector)
                        if elements:
                            await elements[0].click()
                            print(f"✅ 点击了 iframe 内的 {selector}")
                            return
                    except:
                        continue
                
                # 如果没找到特定元素，尝试点击 iframe 中心
                box = await iframe_element.bounding_box()
                if box:
                    center_x = box['x'] + box['width'] / 2
                    center_y = box['y'] + box['height'] / 2
                    await page.mouse.click(center_x, center_y)
                    print(f"✅ 点击 iframe 中心: ({center_x}, {center_y})")
            else:
                # 直接点击 iframe
                await iframe_element.click()
                print("✅ 直接点击 iframe")
                
        except Exception as e:
            print(f"⚠️ iframe 处理异常: {e}")
            await iframe_element.click()
    
    async def wait_for_success(self, page, context):
        """等待验证成功"""
        print("⏳ 等待验证成功...")
        
        # 增加等待时间到 3 分钟
        for wait_time in range(180):
            await asyncio.sleep(1)
            
            # 检查 cookies
            cookies = await context.cookies()
            cf_clearance = next((c for c in cookies if c['name'] == 'cf_clearance'), None)
            
            if cf_clearance:
                print(f"🎉 验证成功! 耗时 {wait_time + 1} 秒")
                return cf_clearance
            
            # 每 30 秒报告状态
            if wait_time % 30 == 29:
                print(f"⏳ 持续等待... ({wait_time + 1}/180秒)")
                
                # 检查页面变化
                try:
                    title = await page.title()
                    url = page.url
                    print(f"📄 当前: {title} | {url}")
                except:
                    pass
                
                # 截图记录状态
                try:
                    await page.screenshot(path=f"ultimate_wait_{wait_time + 1}s.png")
                except:
                    pass
        
        print("⏰ 等待超时")
        return None
    
    def save_success(self, cookie):
        """保存成功结果"""
        result = {
            "url": self.target_url,
            "timestamp": datetime.now().isoformat(),
            "method": "Ultimate DDAI Solver",
            "success": True,
            "cookie": {
                "name": cookie['name'],
                "value": cookie['value'],
                "domain": cookie['domain'],
                "path": cookie['path'],
                "expires": cookie.get('expires'),
                "secure": cookie.get('secure'),
                "http_only": cookie.get('httpOnly'),
                "same_site": cookie.get('sameSite')
            }
        }
        
        filename = f"ddai_ultimate_success_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"\n🎊 终极成功!")
        print(f"💾 结果文件: {filename}")
        print(f"🍪 Cookie: {cookie['value']}")
        print(f"\n📋 使用方法:")
        print(f"curl -H 'Cookie: cf_clearance={cookie['value']}' '{self.target_url}'")
        print(f"\nPython:")
        print(f"headers = {{'Cookie': 'cf_clearance={cookie['value']}'}}")
        print(f"requests.get('{self.target_url}', headers=headers)")
    
    async def run(self):
        """运行终极解决器"""
        cookie = await self.solve_with_patience()
        
        if cookie:
            self.save_success(cookie)
            print(f"\n✅ 最终结果: 成功获取 cookie!")
            return cookie
        else:
            print(f"\n❌ 最终结果: 未能获取 cookie")
            print(f"\n💡 建议:")
            print("- 检查 ultimate_final_state.png 了解最终状态")
            print("- 查看 ultimate_cf_requests.json 了解网络活动")
            print("- 网站可能使用了更高级的保护机制")
            print("- 尝试手动访问确认当前验证流程")
            return None

async def main():
    print("🚀 启动终极版 DDAI Space Cloudflare 解决器")
    solver = UltimateDDAISolver()
    result = await solver.run()
    
    if result:
        print(f"\n🎉 任务完成!")
    else:
        print(f"\n😞 任务未完成")

if __name__ == "__main__":
    asyncio.run(main())
