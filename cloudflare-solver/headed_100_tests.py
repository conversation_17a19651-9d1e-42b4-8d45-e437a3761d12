#!/usr/bin/env python3
"""
有头模式100次批量测试

专门用于测试有头模式的大规模表现
预计总耗时: 约50-80分钟
"""

import asyncio
import json
import time
import requests
from datetime import datetime, timedelta
from main import CloudflareSolver

class Headed100Tests:
    """有头模式100次测试框架"""
    
    def __init__(self):
        self.target_url = "https://app.ddai.space/register"
        self.total_tests = 100
        self.results = []
        self.start_time = None
        self.current_test = 0
        
    def print_header(self):
        """打印测试头部"""
        print("🏆 有头模式100次批量测试")
        print("=" * 70)
        print(f"🎯 目标网站: {self.target_url}")
        print(f"📊 测试总数: {self.total_tests}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  预计耗时: 50-80分钟")
        print(f"🎯 预期成功率: 100% (基于历史数据)")
        print("=" * 70)
        
    async def single_test(self, test_num):
        """单次测试"""
        
        self.current_test = test_num
        
        # 每10次显示进度
        if test_num % 10 == 1 or test_num <= 5:
            print(f"\n🚀 第 {test_num}/100 次测试")
            print("-" * 30)
        
        # 创建有头模式solver
        solver = CloudflareSolver(
            headless=False,         # 有头模式
            sleep_time=6,           # 基于历史最佳配置
            os=["macos"],          # macOS指纹
            debug=False,           # 批量测试关闭调试减少输出
            retries=80             # 标准重试次数
        )
        
        test_start_time = time.time()
        
        try:
            cookie = await solver.solve(self.target_url)
            test_end_time = time.time()
            duration = test_end_time - test_start_time
            
            if cookie:
                # 快速验证cookie
                valid = await self.quick_verify_cookie(cookie)
                
                result = {
                    'test_num': test_num,
                    'success': True,
                    'duration': duration,
                    'cookie_length': len(cookie.value),
                    'cookie_valid': valid,
                    'timestamp': datetime.now().isoformat(),
                    'error': None
                }
                
                if test_num % 10 == 1 or test_num <= 5:
                    print(f"✅ 成功! 耗时: {duration:.2f}s, Cookie有效: {'是' if valid else '否'}")
                
            else:
                result = {
                    'test_num': test_num,
                    'success': False,
                    'duration': duration,
                    'cookie_length': 0,
                    'cookie_valid': False,
                    'timestamp': datetime.now().isoformat(),
                    'error': 'No cookie obtained'
                }
                
                print(f"❌ 第{test_num}次失败! 耗时: {duration:.2f}s")
                
        except Exception as e:
            test_end_time = time.time()
            duration = test_end_time - test_start_time
            
            result = {
                'test_num': test_num,
                'success': False,
                'duration': duration,
                'cookie_length': 0,
                'cookie_valid': False,
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
            
            print(f"❌ 第{test_num}次错误! 耗时: {duration:.2f}s, 错误: {str(e)[:30]}...")
        
        return result
    
    async def quick_verify_cookie(self, cookie):
        """快速验证cookie有效性"""
        try:
            session = requests.Session()
            session.cookies.set(cookie.name, cookie.value, domain=cookie.domain)
            response = session.get(self.target_url, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def show_progress(self, test_num):
        """显示进度统计"""
        if test_num % 10 == 0 or test_num <= 5:
            success_count = sum(1 for r in self.results if r['success'])
            success_rate = (success_count / len(self.results)) * 100
            
            avg_duration = sum(r['duration'] for r in self.results) / len(self.results)
            
            elapsed_time = time.time() - self.start_time
            estimated_total = (elapsed_time / test_num) * 100
            remaining_time = estimated_total - elapsed_time
            
            print(f"\n📊 进度报告 ({test_num}/100):")
            print(f"   成功率: {success_count}/{len(self.results)} = {success_rate:.1f}%")
            print(f"   平均耗时: {avg_duration:.2f}s")
            print(f"   已用时间: {elapsed_time/60:.1f}分钟")
            print(f"   预计剩余: {remaining_time/60:.1f}分钟")
            print(f"   预计完成: {(datetime.now() + timedelta(seconds=remaining_time)).strftime('%H:%M:%S')}")
    
    async def run_100_tests(self):
        """运行100次测试"""
        
        self.print_header()
        self.start_time = time.time()
        
        for test_num in range(1, self.total_tests + 1):
            # 执行单次测试
            result = await self.single_test(test_num)
            self.results.append(result)
            
            # 显示进度
            self.show_progress(test_num)
            
            # 保存中间结果 (每25次保存一次)
            if test_num % 25 == 0:
                self.save_intermediate_results(test_num)
            
            # 适当的间隔时间，避免过于频繁的请求
            if test_num < self.total_tests:
                await asyncio.sleep(2)  # 2秒间隔
        
        return self.results
    
    def save_intermediate_results(self, test_num):
        """保存中间结果"""
        filename = f"headed_100_intermediate_{test_num}.json"
        
        summary = self.analyze_results(self.results)
        
        data = {
            'completed_tests': test_num,
            'total_tests': self.total_tests,
            'summary': summary,
            'detailed_results': self.results
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 中间结果已保存: {filename}")
    
    def analyze_results(self, results):
        """分析测试结果"""
        
        total_tests = len(results)
        successful_tests = [r for r in results if r['success']]
        failed_tests = [r for r in results if not r['success']]
        
        success_count = len(successful_tests)
        success_rate = (success_count / total_tests) * 100 if total_tests > 0 else 0
        
        if successful_tests:
            durations = [r['duration'] for r in successful_tests]
            avg_duration = sum(durations) / len(durations)
            min_duration = min(durations)
            max_duration = max(durations)
            
            # 验证有效的cookie数量
            valid_cookies = sum(1 for r in successful_tests if r.get('cookie_valid', False))
            cookie_validity_rate = (valid_cookies / len(successful_tests)) * 100
        else:
            avg_duration = min_duration = max_duration = 0
            cookie_validity_rate = 0
        
        return {
            'total_tests': total_tests,
            'success_count': success_count,
            'success_rate': success_rate,
            'avg_duration': avg_duration,
            'min_duration': min_duration,
            'max_duration': max_duration,
            'cookie_validity_rate': cookie_validity_rate,
            'failed_count': len(failed_tests)
        }
    
    def generate_final_report(self):
        """生成最终报告"""
        
        print(f"\n🏆 有头模式100次测试最终报告")
        print("=" * 70)
        
        summary = self.analyze_results(self.results)
        total_time = time.time() - self.start_time
        
        print(f"📊 测试概况:")
        print(f"   总测试次数: {summary['total_tests']}")
        print(f"   成功次数: {summary['success_count']}")
        print(f"   失败次数: {summary['failed_count']}")
        print(f"   成功率: {summary['success_rate']:.2f}%")
        print(f"   总耗时: {total_time/60:.1f} 分钟 ({total_time/3600:.2f} 小时)")
        
        if summary['success_count'] > 0:
            print(f"\n⏱️  时间统计:")
            print(f"   平均耗时: {summary['avg_duration']:.2f} 秒")
            print(f"   最快时间: {summary['min_duration']:.2f} 秒")
            print(f"   最慢时间: {summary['max_duration']:.2f} 秒")
            print(f"   Cookie有效率: {summary['cookie_validity_rate']:.1f}%")
        
        # 保存最终结果
        final_data = {
            'test_type': 'headed_mode_100_tests',
            'completed_at': datetime.now().isoformat(),
            'total_duration_minutes': total_time / 60,
            'summary': summary,
            'detailed_results': self.results
        }
        
        filename = f"headed_100_final_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(final_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 最终结果已保存: {filename}")
        
        # 与历史数据对比
        self.compare_with_historical_data(summary)
        
        return summary
    
    def compare_with_historical_data(self, summary):
        """与历史数据对比"""
        
        print(f"\n📈 与历史数据对比:")
        print("-" * 40)
        print(f"历史数据 (5次测试):")
        print(f"   成功率: 100% (5/5)")
        print(f"   平均耗时: 30.61秒")
        
        print(f"\n100次测试结果:")
        print(f"   成功率: {summary['success_rate']:.1f}% ({summary['success_count']}/100)")
        print(f"   平均耗时: {summary['avg_duration']:.2f}秒")
        
        if summary['success_rate'] >= 95:
            print("✅ 大规模测试验证了有头模式的高可靠性!")
        elif summary['success_rate'] >= 90:
            print("✅ 大规模测试显示有头模式表现良好!")
        else:
            print("⚠️  大规模测试发现了一些稳定性问题")

async def main():
    """主函数"""
    
    print("🚀 准备开始有头模式100次批量测试")
    print("=" * 70)
    print("⚠️  重要提醒:")
    print("• 这将需要约50-80分钟完成")
    print("• 请确保网络连接稳定")
    print("• 请不要关闭计算机或中断网络")
    print("• 测试过程中会自动保存中间结果")
    print("=" * 70)
    
    try:
        confirm = input("确认开始100次测试? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("❌ 测试已取消")
            return
        
        tester = Headed100Tests()
        results = await tester.run_100_tests()
        summary = tester.generate_final_report()
        
        print(f"\n🎉 100次有头模式测试完成!")
        print(f"🏆 最终成功率: {summary['success_rate']:.1f}%")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  测试被用户中断")
        print(f"📊 已完成 {len(tester.results) if 'tester' in locals() else 0} 次测试")
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())
