#!/usr/bin/env python3
"""
DDAI Space 快速测试脚本

快速测试 https://app.ddai.space/register 的 Cloudflare 绕过功能

使用方法：
    python3 ddai_quick_test.py
"""

import asyncio
from main import CloudflareSolver

async def test_ddai_space():
    """快速测试 DDAI Space"""
    
    target_url = "https://app.ddai.space/register"
    
    print("🤖 DDAI Space 快速测试")
    print("=" * 40)
    print(f"🎯 目标: {target_url}")
    print("=" * 40)
    
    # 创建 solver
    solver = CloudflareSolver(
        sleep_time=5,       # 等待时间
        headless=False,     # 显示浏览器
        os=["macos"],       # macOS 指纹
        debug=True,         # 调试模式
        retries=60          # 重试次数
    )
    
    try:
        print("⏳ 正在尝试绕过 Cloudflare...")
        cookie = await solver.solve(target_url)
        
        if cookie:
            print("\n🎉 成功！")
            print(f"Cookie: {cookie.name}")
            print(f"值: {cookie.value[:50]}...")
            print(f"域名: {cookie.domain}")
            
            # 简单验证
            print("\n🔍 验证 cookie...")
            import requests
            
            session = requests.Session()
            session.cookies.set(cookie.name, cookie.value, domain=cookie.domain)
            
            response = session.get(target_url, timeout=10)
            if response.status_code == 200:
                print("✅ Cookie 有效！")
                
                # 保存结果
                with open('ddai_cookie.txt', 'w') as f:
                    f.write(f"{cookie.name}={cookie.value}")
                print("💾 Cookie 已保存到 ddai_cookie.txt")
            else:
                print(f"⚠️  状态码: {response.status_code}")
        else:
            print("❌ 失败：未获取到 cookie")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    asyncio.run(test_ddai_space())
