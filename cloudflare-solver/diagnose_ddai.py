#!/usr/bin/env python3
"""
DDAI Space 网站诊断工具
详细分析网站的 Cloudflare 保护状态和连接问题
"""

import asyncio
import requests
import socket
import dns.resolver
from urllib.parse import urlparse
from playwright.async_api import async_playwright
import json
from datetime import datetime

class DDaiDiagnostic:
    def __init__(self):
        self.target_url = "https://app.ddai.space/register"
        self.domain = "app.ddai.space"
        
    def test_dns_resolution(self):
        """测试 DNS 解析"""
        print("🔍 DNS 解析测试")
        print("-" * 40)
        
        try:
            # 获取 A 记录
            a_records = dns.resolver.resolve(self.domain, 'A')
            print(f"✅ A 记录:")
            for record in a_records:
                print(f"   {record}")
            
            # 获取 CNAME 记录
            try:
                cname_records = dns.resolver.resolve(self.domain, 'CNAME')
                print(f"✅ CNAME 记录:")
                for record in cname_records:
                    print(f"   {record}")
            except:
                print("❌ 无 CNAME 记录")
            
            return True
            
        except Exception as e:
            print(f"❌ DNS 解析失败: {e}")
            return False
    
    def test_tcp_connection(self):
        """测试 TCP 连接"""
        print("\n🔌 TCP 连接测试")
        print("-" * 40)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((self.domain, 443))
            sock.close()
            
            if result == 0:
                print("✅ TCP 443 端口连接成功")
                return True
            else:
                print(f"❌ TCP 443 端口连接失败: {result}")
                return False
                
        except Exception as e:
            print(f"❌ TCP 连接测试出错: {e}")
            return False
    
    def test_http_requests(self):
        """测试 HTTP 请求"""
        print("\n🌐 HTTP 请求测试")
        print("-" * 40)
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        try:
            print("📡 发送 GET 请求...")
            response = requests.get(self.target_url, headers=headers, timeout=30)
            
            print(f"✅ 状态码: {response.status_code}")
            print(f"✅ 响应大小: {len(response.content)} bytes")
            
            # 分析响应头
            print("\n📋 重要响应头:")
            important_headers = ['server', 'cf-ray', 'cf-cache-status', 'set-cookie', 'location']
            for header in important_headers:
                if header in response.headers:
                    print(f"   {header}: {response.headers[header]}")
            
            # 分析响应内容
            content = response.text.lower()
            
            # 检查 Cloudflare 相关内容
            cf_indicators = {
                'cloudflare_challenge': 'checking your browser' in content,
                'turnstile': 'turnstile' in content or 'cf-turnstile' in content,
                'captcha': 'captcha' in content,
                'blocked': 'access denied' in content or 'blocked' in content,
                'ddos_protection': 'ddos protection' in content,
                'under_attack': 'under attack mode' in content
            }
            
            print("\n🛡️ Cloudflare 保护检测:")
            for indicator, found in cf_indicators.items():
                status = "✅ 检测到" if found else "❌ 未检测到"
                print(f"   {indicator}: {status}")
            
            # 保存响应内容用于分析
            with open("ddai_response.html", "w", encoding="utf-8") as f:
                f.write(response.text)
            print(f"\n💾 响应内容已保存到: ddai_response.html")
            
            return response
            
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
            return None
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 连接错误: {e}")
            return None
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return None
    
    async def test_playwright_access(self):
        """使用 Playwright 测试访问"""
        print("\n🎭 Playwright 访问测试")
        print("-" * 40)
        
        async with async_playwright() as p:
            # 尝试不同的浏览器
            browsers = [
                ("Chromium", p.chromium),
                ("Firefox", p.firefox),
                ("WebKit", p.webkit)
            ]
            
            for browser_name, browser_type in browsers:
                print(f"\n🌐 测试 {browser_name}...")
                
                try:
                    browser = await browser_type.launch(headless=True)
                    context = await browser.new_context(
                        user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                    )
                    page = await context.new_page()
                    
                    # 设置较长的超时时间
                    page.set_default_timeout(60000)
                    
                    print(f"   📍 导航到: {self.target_url}")
                    response = await page.goto(self.target_url, wait_until='domcontentloaded')
                    
                    if response:
                        print(f"   ✅ 响应状态: {response.status}")
                        
                        # 获取页面标题
                        title = await page.title()
                        print(f"   📄 页面标题: {title}")
                        
                        # 检查页面内容
                        content = await page.content()
                        
                        # 检查是否有 Cloudflare 挑战
                        challenge_selectors = [
                            '[data-cf-turnstile]',
                            '.cf-turnstile',
                            '#cf-turnstile',
                            '[name="cf-turnstile-response"]'
                        ]
                        
                        for selector in challenge_selectors:
                            elements = await page.query_selector_all(selector)
                            if elements:
                                print(f"   🔒 发现 Turnstile 挑战: {selector}")
                        
                        # 获取 cookies
                        cookies = await context.cookies()
                        cf_cookies = [c for c in cookies if 'cf_' in c['name']]
                        
                        if cf_cookies:
                            print(f"   🍪 Cloudflare Cookies:")
                            for cookie in cf_cookies:
                                print(f"      {cookie['name']}: {cookie['value'][:30]}...")
                        
                        # 保存页面截图
                        screenshot_path = f"ddai_{browser_name.lower()}_screenshot.png"
                        await page.screenshot(path=screenshot_path)
                        print(f"   📸 截图已保存: {screenshot_path}")
                        
                    await browser.close()
                    return True
                    
                except Exception as e:
                    print(f"   ❌ {browser_name} 访问失败: {e}")
                    if 'browser' in locals():
                        await browser.close()
                    continue
            
            return False
    
    def test_alternative_endpoints(self):
        """测试其他端点"""
        print("\n🔄 测试其他端点")
        print("-" * 40)
        
        endpoints = [
            "https://app.ddai.space/",
            "https://app.ddai.space/login",
            "https://ddai.space/",
            "https://www.ddai.space/"
        ]
        
        for endpoint in endpoints:
            try:
                print(f"📡 测试: {endpoint}")
                response = requests.get(endpoint, timeout=15)
                print(f"   ✅ 状态码: {response.status_code}")
                
                if 'cf-ray' in response.headers:
                    print(f"   🌩️ CF-Ray: {response.headers['cf-ray']}")
                    
            except Exception as e:
                print(f"   ❌ 失败: {e}")
    
    async def run_full_diagnosis(self):
        """运行完整诊断"""
        print("🏥 DDAI Space 完整诊断报告")
        print("=" * 80)
        print(f"🎯 目标: {self.target_url}")
        print(f"⏰ 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # 1. DNS 解析测试
        dns_ok = self.test_dns_resolution()
        
        # 2. TCP 连接测试
        tcp_ok = self.test_tcp_connection()
        
        # 3. HTTP 请求测试
        http_response = self.test_http_requests()
        
        # 4. 测试其他端点
        self.test_alternative_endpoints()
        
        # 5. Playwright 测试
        playwright_ok = await self.test_playwright_access()
        
        # 生成诊断报告
        print("\n" + "=" * 80)
        print("📊 诊断总结")
        print("=" * 80)
        
        print(f"DNS 解析: {'✅ 正常' if dns_ok else '❌ 失败'}")
        print(f"TCP 连接: {'✅ 正常' if tcp_ok else '❌ 失败'}")
        print(f"HTTP 请求: {'✅ 正常' if http_response else '❌ 失败'}")
        print(f"浏览器访问: {'✅ 正常' if playwright_ok else '❌ 失败'}")
        
        if http_response and http_response.status_code == 200:
            print("\n💡 建议:")
            print("1. 网站可以正常访问")
            print("2. 可能没有启用严格的 Cloudflare 保护")
            print("3. 可以尝试直接使用 HTTP 请求")
        else:
            print("\n💡 建议:")
            print("1. 检查网络连接和防火墙设置")
            print("2. 尝试使用 VPN 或代理")
            print("3. 确认网站是否正常运行")
            print("4. 联系网站管理员")

async def main():
    diagnostic = DDaiDiagnostic()
    await diagnostic.run_full_diagnosis()

if __name__ == "__main__":
    asyncio.run(main())
