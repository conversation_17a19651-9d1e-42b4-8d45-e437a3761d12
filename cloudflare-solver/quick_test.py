#!/usr/bin/env python3
"""
快速测试 Cloudflare Solver

这是一个简单的测试脚本，用于验证 cloudflare-solver 是否正常工作。
"""

import asyncio
from main import CloudflareSolver

async def quick_test():
    """快速测试函数"""
    print("🚀 开始快速测试...")
    
    # 创建 solver 实例
    solver = CloudflareSolver(
        sleep_time=3,
        headless=False,  # 显示浏览器窗口以便观察
        debug=True,      # 启用调试信息
        retries=10       # 减少重试次数以便快速测试
    )
    
    # 测试一个简单的网站
    test_url = "https://httpbin.org/"
    
    print(f"📍 测试网站: {test_url}")
    
    try:
        cookie = await solver.solve(test_url)
        
        if cookie:
            print("✅ 测试成功!")
            print(f"获取到 cookie: {cookie.name} = {cookie.value[:30]}...")
        else:
            print("ℹ️  没有获取到 cf_clearance cookie")
            print("这可能是正常的，因为测试网站可能没有 Cloudflare 保护")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(quick_test())
