#!/usr/bin/env python3
"""
DDAI Space 无头模式 Cloudflare 绕过脚本

专门用于无头模式运行，适合服务器环境和自动化部署。

使用方法：
    python3 ddai_headless_solver.py

特点：
- 完全无头模式运行
- 无需图形界面
- 适合服务器部署
- 静默运行，只输出关键信息
"""

import asyncio
import json
import logging
import requests
from datetime import datetime
from main import CloudflareSolver

# 配置简洁的日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class HeadlessDDAISolver:
    """无头模式 DDAI Space 解决器"""
    
    def __init__(self):
        self.target_url = "https://app.ddai.space/register"
        self.max_attempts = 3
        
    def print_header(self):
        """打印简洁的程序头部"""
        print("🤖 DDAI Space 无头模式 Cloudflare 绕过工具")
        print("=" * 50)
        print(f"🎯 目标: {self.target_url}")
        print(f"⏰ 开始: {datetime.now().strftime('%H:%M:%S')}")
        print("🔇 无头模式: 已启用")
        print("=" * 50)
    
    async def solve_with_headless(self, attempt=1):
        """使用无头模式尝试绕过验证"""
        
        print(f"🚀 第 {attempt} 次尝试 (无头模式)...")
        
        # 无头模式优化配置
        solver = CloudflareSolver(
            sleep_time=5,           # 稍长的等待时间
            headless=True,          # 启用无头模式
            os=["macos"],          # 系统指纹
            debug=False,           # 关闭详细调试（减少输出）
            retries=80             # 增加重试次数（无头模式可能需要更多尝试）
        )
        
        try:
            logger.info(f"开始无头模式访问 {self.target_url}")
            
            # 记录开始时间
            start_time = asyncio.get_event_loop().time()
            
            # 尝试解决 Cloudflare 挑战
            cookie = await solver.solve(self.target_url)
            
            # 记录结束时间
            end_time = asyncio.get_event_loop().time()
            duration = end_time - start_time
            
            if cookie:
                print(f"✅ 成功! 耗时: {duration:.2f}s")
                print(f"🍪 Cookie: {cookie.value[:50]}...")
                
                # 保存结果
                cookie_data = {
                    'name': cookie.name,
                    'value': cookie.value,
                    'domain': cookie.domain,
                    'path': cookie.path,
                    'expires': cookie.expires,
                    'secure': cookie.secure,
                    'http_only': cookie.http_only,
                    'same_site': cookie.same_site,
                    'timestamp': datetime.now().isoformat(),
                    'duration': duration,
                    'mode': 'headless'
                }
                
                # 保存到文件
                filename = f"ddai_headless_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(cookie_data, f, indent=2, ensure_ascii=False)
                
                print(f"💾 已保存: {filename}")
                
                # 验证 cookie
                if await self.verify_cookie(cookie):
                    print("✅ Cookie 验证成功")
                    return cookie
                else:
                    print("⚠️  Cookie 验证失败")
                    return None
                
            else:
                print(f"❌ 失败 (耗时: {duration:.2f}s)")
                logger.warning(f"第 {attempt} 次无头模式尝试失败")
                return None
                
        except Exception as e:
            logger.error(f"第 {attempt} 次无头模式尝试出错: {e}")
            print(f"❌ 错误: {str(e)[:50]}...")
            return None
    
    async def verify_cookie(self, cookie):
        """快速验证 cookie 有效性"""
        try:
            session = requests.Session()
            session.cookies.set(
                cookie.name,
                cookie.value,
                domain=cookie.domain,
                path=cookie.path
            )
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
            
            response = session.get(self.target_url, headers=headers, timeout=10)
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"Cookie 验证错误: {e}")
            return False
    
    def generate_usage_script(self, cookie):
        """生成使用脚本"""
        script_content = f'''#!/usr/bin/env python3
"""
无头模式获取的 DDAI Space Cookie 使用脚本
"""

import requests

def use_ddai_cookie():
    session = requests.Session()
    session.cookies.set(
        '{cookie.name}',
        '{cookie.value}',
        domain='{cookie.domain}',
        path='{cookie.path}'
    )
    
    headers = {{
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
    }}
    
    response = session.get('{self.target_url}', headers=headers)
    print(f"状态码: {{response.status_code}}")
    print(f"页面长度: {{len(response.text)}}")
    
    return response

if __name__ == "__main__":
    use_ddai_cookie()
'''
        
        with open('ddai_headless_usage.py', 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print("📝 使用脚本: ddai_headless_usage.py")
    
    async def run(self):
        """运行无头模式主程序"""
        self.print_header()
        
        success = False
        
        for attempt in range(1, self.max_attempts + 1):
            cookie = await self.solve_with_headless(attempt)
            
            if cookie:
                success = True
                self.generate_usage_script(cookie)
                break
            else:
                if attempt < self.max_attempts:
                    print(f"⏳ 等待 3s 后重试...")
                    await asyncio.sleep(3)
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 无头模式任务完成!")
            print("✅ 成功获取有效 Cookie")
            print("✅ 适合服务器环境运行")
        else:
            print("❌ 无头模式任务失败")
            print("💡 建议检查网络连接或稍后重试")
        
        print("=" * 50)

def main():
    """主函数"""
    try:
        solver = HeadlessDDAISolver()
        asyncio.run(solver.run())
    except KeyboardInterrupt:
        print("\n⏹️  程序中断")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")

if __name__ == "__main__":
    main()
