
# DDAI Space Cookie 使用示例
import requests

# 使用获取的 cookie 访问 DDAI Space
session = requests.Session()
session.cookies.set(
    'cf_clearance',
    'GRPEJMghU6ACtJhqPTZVKSqxy8D9KlfDPhBRlqCtHsg-1752903252-*******-k_J.0HLc8OKjrxGx_PY3F4UAxtGAKjLMaR6IlNLqTGrym3oQk9boCFiamtCMf85iaip3u8EOJXsXPKM1b3ecYOhMa_R4BEXQ4Doh_gcprQcIMRQ1SOWoTPr81L1yY8Ru9WTX5LY41otguK5BWgbFl.8vx0Q4J3iZmWfAKH4hFDhM.27QYtQm3uZhrbI0jE.ny._qPNi0ZZ9tvVrHGTY9Lg68E6JGM1QSv87DKPDrUnM',
    domain='.ddai.space',
    path='/'
)

# 设置请求头
headers = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
}

# 访问注册页面
response = session.get('https://app.ddai.space/register', headers=headers)
print("状态码:", response.status_code)
print("页面内容长度:", len(response.text))

# 现在可以进行注册或其他操作
# ...
