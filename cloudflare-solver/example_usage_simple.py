#!/usr/bin/env python3
"""
Cloudflare Solver 使用示例

这个脚本展示了如何使用 cloudflare-solver 来绕过 Cloudflare Turnstile 验证
并获取 cf_clearance cookie。

使用方法:
    python3 example_usage_simple.py

依赖项:
    pip install -r requirements.txt
    playwright install
"""

import asyncio
import logging
from main import CloudflareSolver

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

async def test_cloudflare_solver():
    """测试 Cloudflare Solver 的基本功能"""
    
    # 测试网站列表 - 这些网站可能有 Cloudflare 保护
    test_urls = [
        "https://httpbin.org/",  # 通常没有 Cloudflare 保护，用于基本测试
        "https://2ip.pro",       # 可能有 Cloudflare 保护
        # 您可以添加其他需要测试的网站
    ]
    
    print("🚀 开始测试 Cloudflare Solver...")
    
    for url in test_urls:
        print(f"\n📍 测试网站: {url}")
        
        # 创建 solver 实例
        solver = CloudflareSolver(
            sleep_time=3,        # 点击前等待时间
            headless=False,      # 显示浏览器窗口（调试时有用）
            os=["macos"],        # 使用 macOS 指纹
            debug=True,          # 启用调试日志
            retries=30           # 重试次数
        )
        
        try:
            # 尝试解决 Cloudflare 挑战
            cookie = await solver.solve(url)
            
            if cookie:
                print("✅ 成功获取 cf_clearance cookie!")
                print(f"   Cookie 名称: {cookie.name}")
                print(f"   Cookie 值: {cookie.value[:50]}..." if len(cookie.value) > 50 else f"   Cookie 值: {cookie.value}")
                print(f"   域名: {cookie.domain}")
                print(f"   路径: {cookie.path}")
                print(f"   过期时间: {cookie.expires}")
                print(f"   安全标志: {cookie.secure}")
                print(f"   HttpOnly: {cookie.http_only}")
                print(f"   SameSite: {cookie.same_site}")
            else:
                print("❌ 未能获取 cf_clearance cookie")
                print("   可能原因:")
                print("   - 网站没有 Cloudflare 保护")
                print("   - Cloudflare 挑战类型不支持")
                print("   - 网络连接问题")
                
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            
        print("-" * 60)

async def advanced_usage_example():
    """高级使用示例"""
    
    print("\n🔧 高级使用示例...")
    
    # 创建一个更定制化的 solver
    solver = CloudflareSolver(
        sleep_time=5,           # 更长的等待时间
        headless=True,          # 无头模式（生产环境推荐）
        os=["windows"],         # Windows 指纹
        debug=False,            # 关闭调试日志
        retries=50              # 更多重试次数
    )
    
    url = "https://httpbin.org/"
    
    try:
        cookie = await solver.solve(url)
        
        if cookie:
            print("✅ 高级配置测试成功!")
            
            # 演示如何使用获取的 cookie 进行后续请求
            import requests
            
            session = requests.Session()
            session.cookies.set(
                cookie.name,
                cookie.value,
                domain=cookie.domain,
                path=cookie.path
            )
            
            # 现在可以使用这个 session 进行请求
            print("🌐 可以使用获取的 cookie 进行后续 HTTP 请求")
            
        else:
            print("❌ 高级配置测试失败")
            
    except Exception as e:
        print(f"❌ 高级测试发生错误: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🛡️  Cloudflare Solver 测试程序")
    print("=" * 60)
    
    # 运行基本测试
    asyncio.run(test_cloudflare_solver())
    
    # 运行高级测试
    asyncio.run(advanced_usage_example())
    
    print("\n✨ 测试完成!")
    print("\n💡 使用提示:")
    print("1. 如果遇到问题，请检查网络连接")
    print("2. 某些网站可能不需要 Cloudflare 验证")
    print("3. 调试时建议使用 headless=False 观察浏览器行为")
    print("4. 生产环境建议使用 headless=True")

if __name__ == "__main__":
    main()
