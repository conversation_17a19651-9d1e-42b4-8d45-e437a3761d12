#!/usr/bin/env python3
"""
无头模式 vs 有头模式对比分析

对比两种模式的性能、适用场景和优缺点
"""

import json
import os
from datetime import datetime

def analyze_headless_vs_headed():
    """分析无头模式和有头模式的差异"""
    
    print("🔍 无头模式 vs 有头模式 对比分析")
    print("=" * 60)
    print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 查找最新的无头模式和有头模式结果
    headless_files = [f for f in os.listdir('.') if f.startswith('ddai_headless_') and f.endswith('.json')]
    headed_files = [f for f in os.listdir('.') if f.startswith('ddai_cookie_') and f.endswith('.json')]
    
    if headless_files:
        latest_headless = max(headless_files)
        print(f"📱 无头模式文件: {latest_headless}")
        
        with open(latest_headless, 'r') as f:
            headless_data = json.load(f)
    else:
        print("❌ 未找到无头模式结果文件")
        return
    
    if headed_files:
        latest_headed = max(headed_files)
        print(f"🖥️  有头模式文件: {latest_headed}")
        
        with open(latest_headed, 'r') as f:
            headed_data = json.load(f)
    else:
        print("❌ 未找到有头模式结果文件")
        return
    
    # 性能对比
    print("\n📊 性能对比:")
    print("-" * 40)
    print(f"无头模式执行时间: {headless_data['duration']:.2f} 秒")
    print(f"有头模式执行时间: {headed_data['duration']:.2f} 秒")
    
    time_diff = headless_data['duration'] - headed_data['duration']
    if time_diff > 0:
        print(f"⏱️  无头模式较慢: +{time_diff:.2f} 秒")
    else:
        print(f"⏱️  无头模式较快: {abs(time_diff):.2f} 秒")
    
    # Cookie 质量对比
    print("\n🍪 Cookie 质量对比:")
    print("-" * 40)
    print(f"无头模式 Cookie 长度: {len(headless_data['value'])} 字符")
    print(f"有头模式 Cookie 长度: {len(headed_data['value'])} 字符")
    
    if len(headless_data['value']) == len(headed_data['value']):
        print("✅ Cookie 长度一致")
    else:
        print("⚠️  Cookie 长度不同")
    
    # 功能特性对比
    print("\n🔧 功能特性对比:")
    print("-" * 40)
    
    features_comparison = [
        ("资源占用", "低", "高"),
        ("可视化调试", "无", "有"),
        ("服务器适用性", "优秀", "不适用"),
        ("开发调试", "困难", "容易"),
        ("自动化部署", "优秀", "困难"),
        ("稳定性", "高", "中等"),
        ("检测风险", "低", "中等")
    ]
    
    print(f"{'特性':<12} {'无头模式':<10} {'有头模式':<10}")
    print("-" * 40)
    for feature, headless, headed in features_comparison:
        print(f"{feature:<12} {headless:<10} {headed:<10}")

def generate_usage_recommendations():
    """生成使用建议"""
    
    print("\n💡 使用场景建议:")
    print("=" * 60)
    
    print("🔇 无头模式适用场景:")
    print("✅ 服务器环境部署")
    print("✅ 自动化脚本集成")
    print("✅ 定时任务执行")
    print("✅ Docker 容器运行")
    print("✅ CI/CD 流水线")
    print("✅ 生产环境运行")
    print("✅ 批量处理任务")
    
    print("\n🖥️  有头模式适用场景:")
    print("✅ 开发和调试")
    print("✅ 问题排查")
    print("✅ 功能验证")
    print("✅ 演示和展示")
    print("✅ 学习和理解流程")
    print("✅ 手动干预需求")

def generate_deployment_examples():
    """生成部署示例"""
    
    print("\n🚀 部署示例:")
    print("=" * 60)
    
    print("📱 无头模式 - 服务器部署:")
    print("""
# 1. 基本使用
python3 ddai_headless_solver.py

# 2. 定时任务 (crontab)
0 */6 * * * cd /path/to/cloudflare-solver && python3 ddai_headless_solver.py

# 3. Docker 容器
FROM python:3.9-slim
COPY . /app
WORKDIR /app
RUN pip install -r requirements.txt
RUN playwright install
CMD ["python3", "ddai_headless_solver.py"]

# 4. 系统服务 (systemd)
[Unit]
Description=DDAI Cloudflare Solver
After=network.target

[Service]
Type=oneshot
ExecStart=/usr/bin/python3 /path/to/ddai_headless_solver.py
WorkingDirectory=/path/to/cloudflare-solver

[Install]
WantedBy=multi-user.target
""")
    
    print("\n🖥️  有头模式 - 开发环境:")
    print("""
# 1. 调试模式
python3 ddai_space_solver.py

# 2. 交互式测试
python3 interactive_solver.py

# 3. 问题排查
python3 ddai_space_solver.py --debug --verbose
""")

def generate_performance_tips():
    """生成性能优化建议"""
    
    print("\n⚡ 性能优化建议:")
    print("=" * 60)
    
    print("🔇 无头模式优化:")
    print("• 增加重试次数 (retries=100)")
    print("• 适当延长等待时间 (sleep_time=6-8)")
    print("• 关闭调试日志 (debug=False)")
    print("• 使用合适的系统指纹")
    print("• 配置代理池轮换")
    
    print("\n🖥️  有头模式优化:")
    print("• 使用较少的重试次数 (retries=30-50)")
    print("• 启用调试模式观察过程")
    print("• 适中的等待时间 (sleep_time=3-5)")
    print("• 手动干预异常情况")

def main():
    """主函数"""
    print("🔄 DDAI Space Cloudflare 绕过脚本 - 模式对比分析")
    print("=" * 70)
    
    analyze_headless_vs_headed()
    generate_usage_recommendations()
    generate_deployment_examples()
    generate_performance_tips()
    
    print("\n" + "=" * 70)
    print("🎉 对比分析完成!")
    print("💡 建议: 开发时用有头模式，生产时用无头模式")
    print("=" * 70)

if __name__ == "__main__":
    main()
