#!/usr/bin/env python3
"""
优化无头模式配置获取的 Cookie 使用示例
"""

import requests

def use_optimized_headless_cookie():
    """使用优化无头模式获取的 cookie"""
    
    # 创建会话
    session = requests.Session()
    
    # 设置 cookie
    session.cookies.set(
        'cf_clearance',
        '8jRpF_pY8dCTa4iNZ47k3gRRMtAIhlJWVMnN9Bl2QC8-1752902006-*******-lWem7ULbvvhrkIEnwKXnr1cngUO5E7Dj9n.i.ta8RQ6ViopJyQT1vP1ceHUoLGgE0F3qyqOpXr2dV.Vcw37FtcxaWi9wKpnvmIWI0.F2370WX6ZS1Ut8y_hRwn3CK.LWdFivSlMut3IpS82egaSu9tMRT2fusB1yHwlrHJMc6X2KvczdJ8Px2X0AJ7ul3sZ3vvazUPZKKtSF4OglLFEuIxcw6_4c1iS9Rko7oNINWsg',
        domain='.ddai.space',
        path='/'
    )
    
    # 设置请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    }
    
    try:
        # 访问目标网站
        response = session.get('https://app.ddai.space/register', headers=headers, timeout=15)
        
        print(f"状态码: {response.status_code}")
        print(f"页面长度: {len(response.text)} 字符")
        
        if response.status_code == 200:
            print("✅ 成功使用优化无头模式获取的 Cookie!")
            return response
        else:
            print(f"❌ 访问失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 使用 Cookie 时出错: {e}")
        return None

if __name__ == "__main__":
    result = use_optimized_headless_cookie()
    if result:
        print("🎉 优化无头模式 Cookie 使用成功!")
    else:
        print("❌ Cookie 使用失败")
