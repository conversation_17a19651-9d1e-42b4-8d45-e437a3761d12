#!/usr/bin/env python3
"""
无头模式获取的 DDAI Space Cookie 使用脚本
"""

import requests

def use_ddai_cookie():
    session = requests.Session()
    session.cookies.set(
        'cf_clearance',
        'OBUBOYjfddLBlh_lrnvFWtcGdLBMAgVnqLLQ62J3H1M-1752900253-1.2.1.1-MBociAPNYX1KeiqO7UOxd6cQeRuCcUETbdVZ9Vb4s1aTU5TVBNMC.z8j2XGb1q5rIS5pMitI.aI14kXPuA7rYfwRMqFl26LCPPPtKqm8jWvuGsiaGljt4AGBYwdvgVkFjC8WO_S10M2oIkVDkZY3epDo87MShVXuz07mpnjFStIoxQjoKajHyOMVvIhZTC87phn5hwIg3yuzaZk7EEcwiUeAVRX.yQjKZzkVO3dXBlk',
        domain='.ddai.space',
        path='/'
    )
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
    }
    
    response = session.get('https://app.ddai.space/register', headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"页面长度: {len(response.text)}")
    
    return response

if __name__ == "__main__":
    use_ddai_cookie()
