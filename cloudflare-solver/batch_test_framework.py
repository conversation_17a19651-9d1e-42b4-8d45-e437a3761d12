#!/usr/bin/env python3
"""
批量测试框架

用于批量测试有头模式和无头模式的成功率
支持可配置的测试次数和间隔时间
"""

import asyncio
import json
import time
from datetime import datetime
from main import CloudflareSolver

class BatchTestFramework:
    """批量测试框架"""
    
    def __init__(self):
        self.target_url = "https://app.ddai.space/register"
        self.results = {
            'headed': [],
            'headless': []
        }
        
    def print_header(self, mode, test_count):
        """打印测试头部信息"""
        print(f"🧪 {mode} 批量测试")
        print("=" * 60)
        print(f"🎯 目标网站: {self.target_url}")
        print(f"📊 测试次数: {test_count}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
    
    async def single_test(self, mode, test_num, total_tests):
        """单次测试"""
        
        print(f"\n🚀 {mode} 第 {test_num}/{total_tests} 次测试")
        print("-" * 40)
        
        # 根据模式配置solver
        if mode == "有头模式":
            solver = CloudflareSolver(
                headless=False,
                sleep_time=6,
                os=["macos"],
                debug=False,  # 批量测试时关闭调试减少输出
                retries=80
            )
        else:  # 无头模式
            solver = CloudflareSolver(
                headless=True,
                sleep_time=20,
                os=["macos"],
                debug=False,
                retries=250
            )
        
        start_time = time.time()
        
        try:
            cookie = await solver.solve(self.target_url)
            end_time = time.time()
            duration = end_time - start_time
            
            if cookie:
                result = {
                    'test_num': test_num,
                    'success': True,
                    'duration': duration,
                    'cookie_length': len(cookie.value),
                    'timestamp': datetime.now().isoformat(),
                    'error': None
                }
                print(f"✅ 成功! 耗时: {duration:.2f}s")
            else:
                result = {
                    'test_num': test_num,
                    'success': False,
                    'duration': duration,
                    'cookie_length': 0,
                    'timestamp': datetime.now().isoformat(),
                    'error': 'No cookie obtained'
                }
                print(f"❌ 失败! 耗时: {duration:.2f}s")
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            result = {
                'test_num': test_num,
                'success': False,
                'duration': duration,
                'cookie_length': 0,
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
            print(f"❌ 错误! 耗时: {duration:.2f}s, 错误: {str(e)[:50]}...")
        
        return result
    
    async def batch_test(self, mode, test_count, interval=30):
        """批量测试"""
        
        self.print_header(mode, test_count)
        
        results = []
        
        for i in range(1, test_count + 1):
            # 执行单次测试
            result = await self.single_test(mode, i, test_count)
            results.append(result)
            
            # 显示当前统计
            success_count = sum(1 for r in results if r['success'])
            success_rate = (success_count / len(results)) * 100
            avg_duration = sum(r['duration'] for r in results) / len(results)
            
            print(f"📊 当前统计: {success_count}/{len(results)} = {success_rate:.1f}%, 平均耗时: {avg_duration:.2f}s")
            
            # 如果不是最后一次测试，等待间隔时间
            if i < test_count:
                print(f"⏳ 等待 {interval} 秒后进行下一次测试...")
                await asyncio.sleep(interval)
        
        return results
    
    def analyze_results(self, mode, results):
        """分析测试结果"""
        
        print(f"\n📊 {mode} 测试结果分析")
        print("=" * 60)
        
        total_tests = len(results)
        successful_tests = [r for r in results if r['success']]
        failed_tests = [r for r in results if not r['success']]
        
        success_count = len(successful_tests)
        success_rate = (success_count / total_tests) * 100
        
        print(f"📈 基本统计:")
        print(f"   总测试次数: {total_tests}")
        print(f"   成功次数: {success_count}")
        print(f"   失败次数: {len(failed_tests)}")
        print(f"   成功率: {success_rate:.2f}%")
        
        if successful_tests:
            durations = [r['duration'] for r in successful_tests]
            print(f"\n⏱️  时间统计 (仅成功测试):")
            print(f"   平均耗时: {sum(durations) / len(durations):.2f} 秒")
            print(f"   最快时间: {min(durations):.2f} 秒")
            print(f"   最慢时间: {max(durations):.2f} 秒")
        
        if failed_tests:
            print(f"\n❌ 失败原因分析:")
            error_types = {}
            for test in failed_tests:
                error = test['error'] or 'Unknown'
                error_types[error] = error_types.get(error, 0) + 1
            
            for error, count in error_types.items():
                print(f"   {error}: {count} 次")
        
        return {
            'mode': mode,
            'total_tests': total_tests,
            'success_count': success_count,
            'success_rate': success_rate,
            'avg_duration': sum(r['duration'] for r in successful_tests) / len(successful_tests) if successful_tests else 0,
            'results': results
        }
    
    def save_results(self, analysis_data):
        """保存测试结果"""
        
        filename = f"batch_test_{analysis_data['mode'].replace('模式', '')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 测试结果已保存到: {filename}")
        return filename

async def run_small_batch_test():
    """运行小规模批量测试 (推荐)"""
    
    print("🧪 小规模批量测试 (推荐)")
    print("=" * 70)
    print("这将进行有头模式5次 + 无头模式5次的测试")
    print("预计总耗时: 约15-20分钟")
    print("=" * 70)
    
    framework = BatchTestFramework()
    
    # 测试有头模式 5次
    print("\n🖥️  开始有头模式测试...")
    headed_results = await framework.batch_test("有头模式", 5, interval=10)
    headed_analysis = framework.analyze_results("有头模式", headed_results)
    framework.save_results(headed_analysis)
    
    print("\n" + "="*50)
    print("⏳ 有头模式测试完成，等待60秒后开始无头模式测试...")
    await asyncio.sleep(60)
    
    # 测试无头模式 5次
    print("\n🔇 开始无头模式测试...")
    headless_results = await framework.batch_test("无头模式", 5, interval=15)
    headless_analysis = framework.analyze_results("无头模式", headless_results)
    framework.save_results(headless_analysis)
    
    # 对比分析
    print("\n📊 对比分析:")
    print("=" * 70)
    print(f"🖥️  有头模式: {headed_analysis['success_rate']:.1f}% 成功率, 平均 {headed_analysis['avg_duration']:.1f}s")
    print(f"🔇 无头模式: {headless_analysis['success_rate']:.1f}% 成功率, 平均 {headless_analysis['avg_duration']:.1f}s")
    
    return headed_analysis, headless_analysis

async def run_medium_batch_test():
    """运行中等规模批量测试"""
    
    print("🧪 中等规模批量测试")
    print("=" * 70)
    print("这将进行有头模式10次 + 无头模式10次的测试")
    print("预计总耗时: 约1-2小时")
    print("⚠️  请确保有足够时间和网络稳定")
    print("=" * 70)
    
    framework = BatchTestFramework()
    
    # 测试有头模式 10次
    headed_results = await framework.batch_test("有头模式", 10, interval=20)
    headed_analysis = framework.analyze_results("有头模式", headed_results)
    framework.save_results(headed_analysis)
    
    # 等待更长时间
    print("\n⏳ 等待5分钟后开始无头模式测试...")
    await asyncio.sleep(300)
    
    # 测试无头模式 10次
    headless_results = await framework.batch_test("无头模式", 10, interval=30)
    headless_analysis = framework.analyze_results("无头模式", headless_results)
    framework.save_results(headless_analysis)
    
    return headed_analysis, headless_analysis

def main():
    """主函数"""
    print("🚀 批量测试框架")
    print("=" * 70)
    print("选择测试规模:")
    print("1. 小规模测试 (推荐): 有头5次 + 无头5次, 约15-20分钟")
    print("2. 中等规模测试: 有头10次 + 无头10次, 约1-2小时")
    print("3. 自定义测试")
    print("=" * 70)
    
    try:
        choice = input("请选择 (1/2/3): ").strip()
        
        if choice == "1":
            print("🎯 开始小规模批量测试...")
            asyncio.run(run_small_batch_test())
        elif choice == "2":
            print("🎯 开始中等规模批量测试...")
            asyncio.run(run_medium_batch_test())
        elif choice == "3":
            print("🔧 自定义测试功能开发中...")
            print("💡 建议先使用小规模测试验证效果")
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n⏹️  测试中断")
    except Exception as e:
        print(f"\n❌ 测试错误: {e}")

if __name__ == "__main__":
    main()
