#!/usr/bin/env python3
"""
DDAI Space 自动化 Cloudflare 绕过脚本

专门用于绕过 https://app.ddai.space/register 的 Cloudflare 验证
并获取可用的 cf_clearance cookie。

使用方法：
    python3 ddai_space_solver.py

作者：AI Assistant
日期：2025-01-16
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from main import CloudflareSolver

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ddai_solver.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class DDAISolver:
    """专门用于 DDAI Space 的 Cloudflare 解决器"""
    
    def __init__(self):
        self.target_url = "https://app.ddai.space/register"
        self.max_attempts = 3
        self.success_cookies = []
        
    def print_banner(self):
        """打印程序横幅"""
        print("=" * 70)
        print("🤖 DDAI Space Cloudflare 自动绕过工具")
        print("=" * 70)
        print(f"🎯 目标网站: {self.target_url}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)
        
    async def solve_ddai_cloudflare(self, attempt=1):
        """尝试绕过 DDAI Space 的 Cloudflare 验证"""
        
        print(f"\n🚀 第 {attempt} 次尝试绕过验证...")
        print("-" * 50)
        
        # 创建专门优化的 solver 配置
        solver = CloudflareSolver(
            sleep_time=6,           # 更长的等待时间，确保页面完全加载
            headless=False,         # 显示浏览器窗口便于观察
            os=["macos"],          # 使用 macOS 指纹
            debug=True,            # 启用详细调试
            retries=80             # 增加重试次数
        )
        
        try:
            logger.info(f"开始访问 {self.target_url}")
            print("⏳ 正在启动浏览器并访问网站...")
            print("💡 请不要手动操作浏览器窗口")
            
            # 记录开始时间
            start_time = time.time()
            
            # 尝试解决 Cloudflare 挑战
            cookie = await solver.solve(self.target_url)
            
            # 记录结束时间
            end_time = time.time()
            duration = end_time - start_time
            
            if cookie:
                print("🎉 成功绕过 Cloudflare 验证！")
                print("=" * 50)
                print(f"✅ 耗时: {duration:.2f} 秒")
                print(f"✅ Cookie 名称: {cookie.name}")
                print(f"✅ Cookie 值: {cookie.value}")
                print(f"✅ 域名: {cookie.domain}")
                print(f"✅ 路径: {cookie.path}")
                print(f"✅ 过期时间: {cookie.expires}")
                print(f"✅ 安全标志: {cookie.secure}")
                print("=" * 50)
                
                # 保存成功的 cookie
                cookie_data = {
                    'name': cookie.name,
                    'value': cookie.value,
                    'domain': cookie.domain,
                    'path': cookie.path,
                    'expires': cookie.expires,
                    'secure': cookie.secure,
                    'http_only': cookie.http_only,
                    'same_site': cookie.same_site,
                    'timestamp': datetime.now().isoformat(),
                    'duration': duration
                }
                
                self.success_cookies.append(cookie_data)
                
                # 保存到文件
                self.save_cookie_to_file(cookie_data)
                
                # 验证 cookie 有效性
                await self.verify_cookie(cookie)
                
                logger.info("成功获取并验证 cf_clearance cookie")
                return cookie
                
            else:
                print("❌ 未能获取 cf_clearance cookie")
                print(f"⏱️  尝试耗时: {duration:.2f} 秒")
                
                logger.warning(f"第 {attempt} 次尝试失败")
                return None
                
        except Exception as e:
            logger.error(f"第 {attempt} 次尝试发生错误: {e}")
            print(f"❌ 发生错误: {e}")
            return None
    
    def save_cookie_to_file(self, cookie_data):
        """保存 cookie 到文件"""
        try:
            filename = f"ddai_cookie_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(cookie_data, f, indent=2, ensure_ascii=False)
            print(f"💾 Cookie 已保存到: {filename}")
            logger.info(f"Cookie 保存到文件: {filename}")
        except Exception as e:
            logger.error(f"保存 cookie 失败: {e}")
    
    async def verify_cookie(self, cookie):
        """验证 cookie 的有效性"""
        print("\n🔍 验证 cookie 有效性...")
        
        try:
            import requests
            
            session = requests.Session()
            session.cookies.set(
                cookie.name,
                cookie.value,
                domain=cookie.domain,
                path=cookie.path
            )
            
            # 设置合适的请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
            
            response = session.get(self.target_url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                print("✅ Cookie 验证成功 - 可以正常访问网站")
                logger.info("Cookie 验证成功")
                
                # 检查响应内容
                if "cloudflare" not in response.text.lower() or "challenge" not in response.text.lower():
                    print("✅ 确认已绕过 Cloudflare 保护")
                else:
                    print("⚠️  可能仍有 Cloudflare 保护，但 cookie 有效")
                    
                return True
            else:
                print(f"⚠️  Cookie 可能无效 - HTTP 状态码: {response.status_code}")
                logger.warning(f"Cookie 验证失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"⚠️  Cookie 验证过程出错: {e}")
            logger.error(f"Cookie 验证错误: {e}")
            return False
    
    def generate_usage_example(self, cookie):
        """生成使用示例代码"""
        example_code = f'''
# DDAI Space Cookie 使用示例
import requests

# 使用获取的 cookie 访问 DDAI Space
session = requests.Session()
session.cookies.set(
    '{cookie.name}',
    '{cookie.value}',
    domain='{cookie.domain}',
    path='{cookie.path}'
)

# 设置请求头
headers = {{
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
}}

# 访问注册页面
response = session.get('{self.target_url}', headers=headers)
print("状态码:", response.status_code)
print("页面内容长度:", len(response.text))

# 现在可以进行注册或其他操作
# ...
'''
        
        print("\n📝 使用示例代码:")
        print("=" * 50)
        print(example_code)
        print("=" * 50)
        
        # 保存示例代码到文件
        try:
            with open('ddai_usage_example.py', 'w', encoding='utf-8') as f:
                f.write(example_code)
            print("💾 使用示例已保存到: ddai_usage_example.py")
        except Exception as e:
            logger.error(f"保存示例代码失败: {e}")
    
    async def run(self):
        """运行主程序"""
        self.print_banner()
        
        success = False
        
        for attempt in range(1, self.max_attempts + 1):
            cookie = await self.solve_ddai_cloudflare(attempt)
            
            if cookie:
                success = True
                self.generate_usage_example(cookie)
                break
            else:
                if attempt < self.max_attempts:
                    print(f"\n⏳ 等待 5 秒后进行第 {attempt + 1} 次尝试...")
                    await asyncio.sleep(5)
        
        print("\n" + "=" * 70)
        if success:
            print("🎉 任务完成！成功绕过 DDAI Space 的 Cloudflare 验证")
            print(f"📊 总共尝试: {len([c for c in self.success_cookies])} 次成功")
        else:
            print("❌ 任务失败！未能绕过 Cloudflare 验证")
            print("\n💡 建议:")
            print("• 检查网络连接")
            print("• 稍后重试")
            print("• 尝试使用 VPN")
            print("• 检查网站是否正常运行")
        
        print("=" * 70)
        print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """主函数"""
    try:
        solver = DDAISolver()
        asyncio.run(solver.run())
    except KeyboardInterrupt:
        print("\n⏹️  用户中断程序")
        logger.info("用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        logger.error(f"程序错误: {e}")

if __name__ == "__main__":
    main()
