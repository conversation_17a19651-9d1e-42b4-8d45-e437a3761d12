#!/usr/bin/env python3
"""
简单的无头模式测试脚本

方法3: 在代码中直接设置无头模式
"""

import asyncio
import requests
from datetime import datetime
from main import CloudflareSolver

async def simple_headless_test():
    """简单的无头模式测试"""
    
    print("🔇 方法3: 代码中设置无头模式")
    print("=" * 40)
    print(f"⏰ 开始: {datetime.now().strftime('%H:%M:%S')}")
    
    # 方法3: 在代码中设置
    solver = CloudflareSolver(
        headless=True,          # 无头模式
        sleep_time=8,           # 稍长等待
        os=["macos"],          # macOS指纹
        debug=True,            # 启用调试
        retries=60             # 适中重试次数
    )
    
    target_url = "https://app.ddai.space/register"
    
    try:
        print("🚀 启动无头模式...")
        
        start_time = asyncio.get_event_loop().time()
        cookie = await solver.solve(target_url)
        end_time = asyncio.get_event_loop().time()
        duration = end_time - start_time
        
        if cookie:
            print(f"✅ 成功! 耗时: {duration:.2f}s")
            print(f"🍪 Cookie: {cookie.value[:50]}...")
            
            # 快速验证
            session = requests.Session()
            session.cookies.set(cookie.name, cookie.value, domain=cookie.domain)
            response = session.get(target_url, timeout=10)
            
            if response.status_code == 200:
                print("✅ Cookie 验证成功")
                
                # 保存简单的使用示例
                with open('simple_headless_usage.py', 'w') as f:
                    f.write(f'''#!/usr/bin/env python3
import requests

session = requests.Session()
session.cookies.set('{cookie.name}', '{cookie.value}', domain='{cookie.domain}')

response = session.get('{target_url}')
print(f"状态码: {{response.status_code}}")
print(f"页面长度: {{len(response.text)}}")
''')
                
                print("📝 使用示例: simple_headless_usage.py")
                return True
            else:
                print(f"⚠️  验证失败: {response.status_code}")
                return False
        else:
            print(f"❌ 失败 (耗时: {duration:.2f}s)")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def main():
    """主函数"""
    print("🧪 简单无头模式测试")
    print("=" * 40)
    
    try:
        success = asyncio.run(simple_headless_test())
        
        print("\n" + "=" * 40)
        if success:
            print("🎉 方法3 成功!")
            print("✅ 无头模式正常工作")
        else:
            print("❌ 方法3 失败")
            print("💡 可能需要调整网络或配置")
        print("=" * 40)
        
    except KeyboardInterrupt:
        print("\n⏹️  中断")
    except Exception as e:
        print(f"\n❌ 错误: {e}")

if __name__ == "__main__":
    main()
