#!/usr/bin/env python3
"""
使用保存的 DDAI Token 进行注册
"""

import json
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def load_token():
    """加载保存的 token"""
    try:
        with open('ddai_manual_token_success.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data['token']
    except Exception as e:
        print(f"❌ 加载 token 失败: {e}")
        return None

def register_with_token(email, username, password, token):
    """使用 token 进行注册"""
    
    # Chrome 选项
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        print("🌐 打开注册页面...")
        driver.get("https://app.ddai.space/register")
        
        # 等待页面加载
        time.sleep(3)
        
        print("📝 填写注册信息...")
        
        # 填写邮箱
        email_field = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, '[placeholder*="email"], [type="email"]'))
        )
        email_field.clear()
        email_field.send_keys(email)
        
        # 填写用户名
        username_field = driver.find_element(By.CSS_SELECTOR, '[placeholder*="username"], [placeholder*="User name"]')
        username_field.clear()
        username_field.send_keys(username)
        
        # 填写密码
        password_field = driver.find_element(By.CSS_SELECTOR, '[placeholder*="password"], [type="password"]')
        password_field.clear()
        password_field.send_keys(password)
        
        print("🎯 注入 Turnstile Token...")
        
        # 注入 Token
        inject_script = """
        var tokenField = document.querySelector('[name="cf-turnstile-response"]');
        if (tokenField) {
            tokenField.value = arguments[0];
            console.log('✅ Token 注入成功');
            return true;
        } else {
            console.error('❌ 找不到 token 字段');
            return false;
        }
        """
        
        result = driver.execute_script(inject_script, token)
        
        if result:
            print("✅ Token 注入成功!")
        else:
            print("❌ Token 注入失败!")
            return False
        
        # 等待一下
        time.sleep(2)
        
        print("🚀 提交注册...")
        
        # 提交表单
        submit_button = driver.find_element(By.CSS_SELECTOR, '[type="submit"], .register-btn, button[class*="register"]')
        submit_button.click()
        
        # 等待结果
        print("⏳ 等待注册结果...")
        
        try:
            # 等待成功或错误消息
            WebDriverWait(driver, 15).until(
                EC.any_of(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '.success, [class*="success"]')),
                    EC.presence_of_element_located((By.CSS_SELECTOR, '.error, [class*="error"]')),
                    EC.presence_of_element_located((By.CSS_SELECTOR, '[class*="message"]')),
                    EC.url_contains('dashboard'),
                    EC.url_contains('login')
                )
            )
            
            current_url = driver.current_url
            page_source = driver.page_source
            
            print(f"📍 当前 URL: {current_url}")
            
            if 'dashboard' in current_url:
                print("🎉 注册成功! 已跳转到仪表板")
                return True
            elif 'login' in current_url:
                print("✅ 注册成功! 已跳转到登录页面")
                return True
            else:
                print("⚠️  注册状态未知，请检查页面")
                return False
                
        except Exception as e:
            print(f"⏰ 等待超时: {e}")
            print(f"📍 当前 URL: {driver.current_url}")
            return False
        
    except Exception as e:
        print(f"❌ 注册过程出错: {e}")
        return False
    
    finally:
        input("按回车键关闭浏览器...")
        driver.quit()

def main():
    """主函数"""
    print("🎯 DDAI 自动注册工具")
    print("=" * 60)
    
    # 加载 token
    token = load_token()
    if not token:
        return
    
    print(f"✅ Token 加载成功: {token[:50]}...")
    print()
    
    # 获取注册信息
    email = input("请输入邮箱: ").strip()
    username = input("请输入用户名: ").strip()
    password = input("请输入密码: ").strip()
    
    if not all([email, username, password]):
        print("❌ 请填写完整信息")
        return
    
    print()
    print("=" * 60)
    print("🚀 开始注册...")
    print("=" * 60)
    
    # 执行注册
    success = register_with_token(email, username, password, token)
    
    if success:
        print()
        print("=" * 60)
        print("🎉 注册完成!")
        print("=" * 60)
    else:
        print()
        print("=" * 60)
        print("❌ 注册失败，请检查日志")
        print("=" * 60)

if __name__ == "__main__":
    main()
