#!/usr/bin/env python3
"""
DDAI 手动解决方案
当自动解决困难时的备用方案
"""

import asyncio
import time
from patchright.async_api import async_playwright

async def manual_ddai_solution():
    """
    手动解决 DDAI Turnstile 的方案
    打开浏览器，让用户手动完成验证，然后提取 token
    """
    print("🎯 DDAI 手动解决方案")
    print("=" * 60)
    print("这个方案将:")
    print("1. 打开 DDAI 注册页面")
    print("2. 让您手动完成 Turnstile 验证")
    print("3. 自动提取生成的 token")
    print("4. 保存 token 供后续使用")
    print()
    
    async with async_playwright() as p:
        # 启动浏览器（非无头模式）
        browser = await p.chromium.launch(
            headless=False,
            slow_mo=500,
            args=[
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )
        
        context = await browser.new_context(
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        
        page = await context.new_page()
        
        try:
            print("🌐 正在打开 DDAI 注册页面...")
            await page.goto("https://app.ddai.space/register", wait_until="networkidle")
            
            print("✅ 页面已加载")
            print()
            print("📋 请按以下步骤操作:")
            print("1. 在浏览器中填写注册信息")
            print("2. 手动完成 Turnstile 验证（点击验证框）")
            print("3. 验证完成后，回到终端按 Enter 键")
            print()
            
            # 等待用户手动完成验证
            input("⏳ 请完成验证后按 Enter 键继续...")
            
            print("🔍 正在提取 token...")
            
            # 尝试多种方法提取 token
            token = None
            
            # 方法1: 通过 name 属性查找
            try:
                token_element = await page.query_selector('[name="cf-turnstile-response"]')
                if token_element:
                    token = await token_element.get_attribute('value')
                    if token:
                        print(f"✅ 方法1成功: 通过 name 属性找到 token")
            except:
                pass
            
            # 方法2: 通过 ID 查找
            if not token:
                try:
                    token_element = await page.query_selector('#cf-chl-widget-msovg_response')
                    if token_element:
                        token = await token_element.get_attribute('value')
                        if token:
                            print(f"✅ 方法2成功: 通过 ID 找到 token")
                except:
                    pass
            
            # 方法3: JavaScript 查找
            if not token:
                try:
                    token = await page.evaluate("""
                        () => {
                            // 查找所有可能的 token 字段
                            const selectors = [
                                '[name="cf-turnstile-response"]',
                                '[id*="cf-chl-widget"]',
                                '[name*="turnstile"]',
                                'input[type="hidden"][value*="0."]'
                            ];
                            
                            for (let selector of selectors) {
                                const element = document.querySelector(selector);
                                if (element && element.value && element.value.length > 10) {
                                    return element.value;
                                }
                            }
                            
                            return null;
                        }
                    """)
                    if token:
                        print(f"✅ 方法3成功: 通过 JavaScript 找到 token")
                except:
                    pass
            
            if token and len(token) > 10:
                print(f"🎉 成功提取 token!")
                print(f"🎫 Token: {token}")
                
                # 保存 token
                timestamp = int(time.time())
                filename = f"ddai_manual_token_{timestamp}.json"
                
                import json
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump({
                        'site_url': 'https://app.ddai.space/register',
                        'site_key': '0x4AAAAAABdw7Ezbqw4v6Kr1',
                        'token': token,
                        'method': 'manual',
                        'timestamp': timestamp,
                        'success': True
                    }, f, indent=2, ensure_ascii=False)
                
                print(f"📁 Token 已保存到: {filename}")
                
                print()
                print("=" * 60)
                print("🎉 手动解决成功!")
                print("=" * 60)
                print("📋 使用这个 token 的方法:")
                print()
                print("1. 🔧 在自动化脚本中:")
                print("   ```python")
                print("   from selenium import webdriver")
                print("   driver = webdriver.Chrome()")
                print("   driver.get('https://app.ddai.space/register')")
                print("   ")
                print("   # 填写注册信息")
                print("   # ...")
                print("   ")
                print("   # 注入 token")
                print(f"   token = '{token}'")
                print("   driver.execute_script(f'''")
                print("       document.querySelector('[name=\"cf-turnstile-response\"]').value = '{token}';")
                print("   ''')")
                print("   ")
                print("   # 提交表单")
                print("   submit_btn = driver.find_element(By.CSS_SELECTOR, '[type=\"submit\"]')")
                print("   submit_btn.click()")
                print("   ```")
                print()
                print("2. 🌐 在浏览器控制台中:")
                print(f"   document.querySelector('[name=\"cf-turnstile-response\"]').value = '{token}';")
                print()
                print("3. 📋 直接复制使用:")
                print(f"   {token}")
                
                return token
            else:
                print("❌ 未能提取到有效的 token")
                print("💡 可能的原因:")
                print("   1. 验证尚未完成")
                print("   2. token 字段名称发生了变化")
                print("   3. 需要等待更长时间")
                
                return None
                
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            return None
        finally:
            print("\n⏳ 保持浏览器打开30秒，您可以继续操作...")
            await page.wait_for_timeout(30000)
            await browser.close()

async def main():
    """主函数"""
    print("🎯 DDAI Turnstile 手动解决工具")
    print("=" * 60)
    print("当自动解决遇到困难时，使用这个工具手动完成验证")
    print()
    
    choice = input("是否开始手动解决? (y/n): ").lower().strip()
    
    if choice == 'y':
        token = await manual_ddai_solution()
        
        if token:
            print(f"\n🎉 任务完成! Token 已获取并保存")
            print("您现在可以使用这个 token 完成 DDAI 注册了!")
        else:
            print(f"\n❌ 未能获取 token，请重试")
    else:
        print("操作已取消")

if __name__ == "__main__":
    asyncio.run(main())
