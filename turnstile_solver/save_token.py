#!/usr/bin/env python3
"""
保存和验证 DDAI Token 工具
用于保存手动获取的 Turnstile token
"""

import json
import time
import re

def validate_token(token):
    """验证 token 格式是否正确"""
    if not token:
        return False, "Token 为空"
    
    if len(token) < 20:
        return False, "Token 太短，可能不完整"
    
    # Turnstile token 通常以特定格式开始
    if not (token.startswith('0.') or token.startswith('1.') or len(token) > 100):
        return False, "Token 格式可能不正确"
    
    return True, "Token 格式正确"

def save_token(token):
    """保存 token 到文件"""
    timestamp = int(time.time())
    
    token_data = {
        'site_url': 'https://app.ddai.space/register',
        'site_key': '0x4AAAAAABdw7Ezbqw4v6Kr1',
        'token': token,
        'method': 'manual_extraction',
        'timestamp': timestamp,
        'datetime': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp)),
        'success': True
    }
    
    filename = f"ddai_manual_token_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(token_data, f, indent=2, ensure_ascii=False)
    
    return filename

def generate_usage_code(token):
    """生成使用 token 的代码示例"""
    
    selenium_code = f'''
# Selenium 使用示例
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

driver = webdriver.Chrome()
driver.get("https://app.ddai.space/register")

# 填写注册信息
email_field = driver.find_element(By.CSS_SELECTOR, '[placeholder*="email"], [type="email"]')
email_field.send_keys("<EMAIL>")

username_field = driver.find_element(By.CSS_SELECTOR, '[placeholder*="username"], [placeholder*="User name"]')
username_field.send_keys("your_username")

password_field = driver.find_element(By.CSS_SELECTOR, '[placeholder*="password"], [type="password"]')
password_field.send_keys("your_password")

# 注入 Turnstile token
token = "{token}"
driver.execute_script("""
    var tokenField = document.querySelector('[name="cf-turnstile-response"]');
    if (tokenField) {
        tokenField.value = arguments[0];
        console.log('Token injected:', arguments[0]);
    } else {
        console.error('Token field not found');
    }
""", token)

# 提交表单
submit_button = driver.find_element(By.CSS_SELECTOR, '[type="submit"], .register-btn, button[class*="register"]')
submit_button.click()

# 等待结果
WebDriverWait(driver, 10).until(
    EC.presence_of_element_located((By.CSS_SELECTOR, '.success, .error, [class*="message"]'))
)

print("注册请求已提交")
'''

    playwright_code = f'''
# Playwright 使用示例
from playwright.sync_api import sync_playwright

with sync_playwright() as p:
    browser = p.chromium.launch(headless=False)
    page = browser.new_page()
    
    page.goto("https://app.ddai.space/register")
    
    # 填写注册信息
    page.fill('[placeholder*="email"], [type="email"]', "<EMAIL>")
    page.fill('[placeholder*="username"], [placeholder*="User name"]', "your_username")
    page.fill('[placeholder*="password"], [type="password"]', "your_password")
    
    # 注入 Turnstile token
    token = "{token}"
    page.evaluate("""
        (token) => {
            const tokenField = document.querySelector('[name="cf-turnstile-response"]');
            if (tokenField) {
                tokenField.value = token;
                console.log('Token injected');
            }
        }
    """, token)
    
    # 提交表单
    page.click('[type="submit"], .register-btn, button[class*="register"]')
    
    # 等待结果
    page.wait_for_selector('.success, .error, [class*="message"]', timeout=10000)
    
    print("注册请求已提交")
    browser.close()
'''

    javascript_code = f'''
// 浏览器控制台直接使用
// 1. 在 DDAI 注册页面按 F12 打开控制台
// 2. 粘贴以下代码并回车

// 注入 token
document.querySelector('[name="cf-turnstile-response"]').value = '{token}';

// 验证注入是否成功
console.log('Token injected:', document.querySelector('[name="cf-turnstile-response"]').value);

// 如果需要，可以直接提交表单
// document.querySelector('[type="submit"]').click();
'''

    return selenium_code, playwright_code, javascript_code

def main():
    """主函数"""
    print("🎯 DDAI Token 保存工具")
    print("=" * 60)
    print("请按以下步骤操作:")
    print("1. 在浏览器中打开 https://app.ddai.space/register")
    print("2. 填写注册信息并完成 Turnstile 验证")
    print("3. 按 F12 → Console，输入:")
    print("   document.querySelector('[name=\"cf-turnstile-response\"]').value")
    print("4. 复制显示的 token 并粘贴到下面")
    print()
    
    # 获取用户输入的 token
    token = input("请粘贴您获取的 token: ").strip()
    
    if not token:
        print("❌ 未输入 token")
        return
    
    # 验证 token
    is_valid, message = validate_token(token)
    
    if not is_valid:
        print(f"⚠️  Token 验证警告: {message}")
        confirm = input("是否仍要保存? (y/n): ").lower()
        if confirm != 'y':
            print("操作已取消")
            return
    else:
        print(f"✅ {message}")
    
    # 保存 token
    try:
        filename = save_token(token)
        print(f"📁 Token 已保存到: {filename}")
        
        # 生成使用代码
        selenium_code, playwright_code, javascript_code = generate_usage_code(token)
        
        # 保存使用示例
        examples_filename = f"ddai_usage_examples_{int(time.time())}.py"
        with open(examples_filename, 'w', encoding='utf-8') as f:
            f.write("# DDAI Turnstile Token 使用示例\n\n")
            f.write("# 方法1: Selenium\n")
            f.write(selenium_code)
            f.write("\n\n# 方法2: Playwright\n")
            f.write(playwright_code)
        
        js_filename = f"ddai_browser_usage_{int(time.time())}.js"
        with open(js_filename, 'w', encoding='utf-8') as f:
            f.write(javascript_code)
        
        print(f"📝 使用示例已保存到: {examples_filename}")
        print(f"🌐 浏览器脚本已保存到: {js_filename}")
        
        print()
        print("=" * 60)
        print("🎉 Token 保存成功!")
        print("=" * 60)
        print("📋 使用方法:")
        print()
        print("1. 🔧 在 Python 脚本中:")
        print(f"   token = '{token[:50]}...'")
        print("   driver.execute_script(f'document.querySelector(\"[name=\\\"cf-turnstile-response\\\"]\").value = \"{token}\"')")
        print()
        print("2. 🌐 在浏览器控制台中:")
        print(f"   document.querySelector('[name=\"cf-turnstile-response\"]').value = '{token[:50]}...';")
        print()
        print("3. 📁 查看详细示例:")
        print(f"   cat {examples_filename}")
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")

if __name__ == "__main__":
    main()
