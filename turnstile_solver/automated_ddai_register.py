#!/usr/bin/env python3
"""
完全自动化的 DDAI 注册脚本
集成 Turnstile Solver 服务，无需手动操作
"""

import json
import time
import random
import string
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

class TurnstileSolverClient:
    """Turnstile Solver 客户端"""
    
    def __init__(self, server_url="http://127.0.0.1:8088", secret="jWRN7DH6"):
        self.server_url = server_url
        self.secret = secret
    
    def solve_turnstile(self, site_url, site_key):
        """解决 Turnstile 验证"""
        url = f"{self.server_url}/solve"
        
        headers = {
            'ngrok-skip-browser-warning': '_',
            'secret': self.secret,
            'Content-Type': 'application/json'
        }
        
        json_data = {
            "site_url": site_url,
            "site_key": site_key
        }
        
        try:
            print(f"🔄 请求 Turnstile Token...")
            print(f"   网站: {site_url}")
            print(f"   Site Key: {site_key}")
            
            response = requests.get(
                url=url,
                headers=headers,
                json=json_data,
                timeout=60
            )
            
            response.raise_for_status()
            data = response.json()
            
            if data.get('status') == 'OK' and data.get('token'):
                print(f"✅ Turnstile Token 获取成功!")
                print(f"   耗时: {data.get('elapsed', 'N/A')} 秒")
                print(f"   Token: {data['token'][:50]}...")
                return data['token']
            else:
                print(f"❌ Turnstile 解决失败: {data}")
                return None
                
        except Exception as e:
            print(f"❌ Turnstile Solver 请求失败: {e}")
            return None

def setup_chrome_driver():
    """设置 Chrome 驱动"""
    chrome_options = Options()
    
    # 基本设置
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 设置窗口大小
    chrome_options.add_argument('--window-size=1920,1080')
    
    # 禁用图片加载以提高速度
    prefs = {
        "profile.managed_default_content_settings.images": 2,
        "profile.default_content_setting_values.notifications": 2
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    # 反检测
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def generate_credentials():
    """生成注册凭据"""
    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
    email = f"{username}@mlanm.online"
    password = "Aa88888888."
    return email, username, password

def wait_for_element_and_fill(driver, selector, value, description="字段"):
    """等待元素并填写值"""
    try:
        element = WebDriverWait(driver, 15).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
        )
        element.clear()
        time.sleep(0.5)
        element.send_keys(value)
        print(f"✅ {description}已填写: {value}")
        return True
    except Exception as e:
        print(f"❌ 填写{description}失败: {e}")
        return False

def automated_register():
    """完全自动化注册"""
    print("🎯 DDAI 完全自动化注册脚本")
    print("=" * 60)
    
    # 初始化 Turnstile Solver
    solver = TurnstileSolverClient()
    
    # 生成凭据
    email, username, password = generate_credentials()
    
    print(f"📧 邮箱: {email}")
    print(f"👤 用户名: {username}")
    print(f"🔐 密码: {password}")
    print()
    
    # DDAI 的 Turnstile 配置
    site_url = "https://app.ddai.space/register"
    site_key = "0x4AAAAAABdw7Ezbqw4v6Kr1"  # 从之前的分析中获得
    
    # 获取 Turnstile Token
    print("🔄 获取 Turnstile Token...")
    token = solver.solve_turnstile(site_url, site_key)
    
    if not token:
        print("❌ 无法获取 Turnstile Token，注册失败")
        return False
    
    # 启动浏览器
    print("\n🌐 启动浏览器...")
    driver = setup_chrome_driver()
    
    try:
        # 1. 打开注册页面
        print("📍 打开注册页面...")
        driver.get("https://app.ddai.space/register?ref=Bp48g624")
        
        # 等待页面加载
        print("⏳ 等待页面加载...")
        time.sleep(5)
        
        # 2. 填写表单
        print("📝 开始填写注册表单...")
        
        # 填写邮箱
        if not wait_for_element_and_fill(driver, '[name="email"]', email, "邮箱"):
            return False
        
        # 填写用户名
        if not wait_for_element_and_fill(driver, '[name="username"]', username, "用户名"):
            return False
        
        # 填写密码
        if not wait_for_element_and_fill(driver, '[name="password"]', password, "密码"):
            return False
        
        # 填写确认密码
        if not wait_for_element_and_fill(driver, '[name="confirmPassword"]', password, "确认密码"):
            return False
        
        # 3. 注入 Turnstile Token
        print("\n🎯 注入 Turnstile Token...")
        
        inject_script = """
        var tokenField = document.querySelector('[name="cf-turnstile-response"]');
        if (tokenField) {
            tokenField.value = arguments[0];
            console.log('Token 注入成功:', arguments[0].substring(0, 50) + '...');
            return true;
        } else {
            console.error('找不到 cf-turnstile-response 字段');
            return false;
        }
        """
        
        result = driver.execute_script(inject_script, token)
        
        if result:
            print("✅ Turnstile Token 注入成功!")
        else:
            print("❌ Turnstile Token 注入失败!")
            return False
        
        # 4. 提交表单
        print("\n🚀 提交注册表单...")
        try:
            submit_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"]'))
            )
            
            # 使用 JavaScript 点击
            driver.execute_script("arguments[0].click();", submit_button)
            print("✅ 注册按钮已点击")
            
        except Exception as e:
            print(f"❌ 点击注册按钮失败: {e}")
            return False
        
        # 5. 等待结果
        print("⏳ 等待注册结果...")
        
        # 监控页面变化
        initial_url = driver.current_url
        start_time = time.time()
        
        while time.time() - start_time < 30:
            current_url = driver.current_url
            
            print(f"📍 当前 URL: {current_url}")
            
            # 检查是否跳转
            if current_url != initial_url:
                if 'login' in current_url:
                    print("🎉 已跳转到登录页面，注册可能成功!")
                    
                    # 保存注册信息
                    save_registration_info(email, username, password, current_url, token)
                    
                    # 尝试登录验证
                    return verify_login(driver, username, password)
                
                elif 'dashboard' in current_url:
                    print("🎉 直接跳转到仪表板，注册成功!")
                    save_registration_info(email, username, password, current_url, token)
                    return True
                
                else:
                    print(f"🔍 跳转到其他页面: {current_url}")
            
            # 检查页面内容
            try:
                page_text = driver.execute_script("return document.body.innerText;").lower()
                
                if any(word in page_text for word in ['success', 'welcome', 'dashboard']):
                    print("🎉 检测到成功关键词!")
                    save_registration_info(email, username, password, current_url, token)
                    return True
                
                elif any(word in page_text for word in ['error', 'failed', 'invalid', 'expired']):
                    print("❌ 检测到错误关键词!")
                    print(f"页面内容片段: {page_text[:200]}...")
                    return False
            except:
                pass
            
            time.sleep(3)
        
        print("⏰ 等待超时")
        return False
    
    except Exception as e:
        print(f"❌ 注册过程出错: {e}")
        return False
    
    finally:
        print("\n⏳ 保持浏览器打开 15 秒以便查看结果...")
        time.sleep(15)
        driver.quit()

def save_registration_info(email, username, password, final_url, token):
    """保存注册信息"""
    registration_data = {
        'email': email,
        'username': username,
        'password': password,
        'token': token,
        'registration_url': 'https://app.ddai.space/register?ref=Bp48g624',
        'final_url': final_url,
        'registration_time': time.strftime('%Y-%m-%d %H:%M:%S'),
        'method': 'automated_with_turnstile_solver',
        'success': True
    }
    
    filename = f'automated_registration_{int(time.time())}.json'
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(registration_data, f, indent=2, ensure_ascii=False)
    
    print(f"📁 注册信息已保存到: {filename}")

def verify_login(driver, username, password):
    """验证登录"""
    try:
        print("\n🔐 开始登录验证...")
        
        # 如果不在登录页面，导航到登录页面
        if 'login' not in driver.current_url:
            driver.get("https://app.ddai.space/login")
            time.sleep(3)
        
        # 填写用户名
        if not wait_for_element_and_fill(driver, '[name="usernameOrEmail"]', username, "登录用户名"):
            return False
        
        # 填写密码
        if not wait_for_element_and_fill(driver, '[name="password"]', password, "登录密码"):
            return False
        
        # 点击登录按钮
        try:
            login_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
            driver.execute_script("arguments[0].click();", login_button)
            print("✅ 登录按钮已点击")
        except Exception as e:
            print(f"❌ 点击登录按钮失败: {e}")
            return False
        
        # 等待登录结果
        time.sleep(8)
        
        current_url = driver.current_url
        print(f"📍 登录后 URL: {current_url}")
        
        if 'dashboard' in current_url or ('login' not in current_url and 'register' not in current_url):
            print("🎉 登录成功！注册验证通过！")
            return True
        else:
            print("❌ 登录失败，可能注册未成功")
            return False
    
    except Exception as e:
        print(f"❌ 登录验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 检查 Turnstile Solver 服务状态...")
    
    try:
        response = requests.get("http://127.0.0.1:8088", timeout=5)
        print("✅ Turnstile Solver 服务正在运行")
    except:
        print("❌ Turnstile Solver 服务未运行")
        print("请先启动服务: solver --port 8088 --secret jWRN7DH6")
        return
    
    success = automated_register()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 DDAI 完全自动化注册成功!")
        print("✅ 账户已创建并验证")
        print("🚀 无需任何手动操作!")
    else:
        print("❌ 注册失败")
        print("💡 请检查 Turnstile Solver 服务状态")
    print("=" * 60)

if __name__ == "__main__":
    main()
