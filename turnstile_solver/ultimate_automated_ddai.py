#!/usr/bin/env python3
"""
终极自动化 DDAI 注册脚本
结合 Turnstile Solver 服务 + CloudScraper + 高级技术
"""

import json
import time
import random
import string
import requests
import cloudscraper
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

class UltimateTurnstileSolver:
    """终极 Turnstile 解决器"""
    
    def __init__(self):
        self.solver_url = "http://127.0.0.1:8088"
        self.secret = "jWRN7DH6"
        self.site_key = "0x4AAAAAABdw7Ezbqw4v6Kr1"  # DDAI 的 Turnstile Site Key
    
    def check_solver_service(self):
        """检查 Turnstile Solver 服务状态"""
        try:
            response = requests.get(self.solver_url, timeout=5)
            return True
        except:
            return False
    
    def get_turnstile_token(self, site_url):
        """从 Turnstile Solver 服务获取 Token"""
        if not self.check_solver_service():
            print("❌ Turnstile Solver 服务未运行")
            return None
        
        url = f"{self.solver_url}/solve"
        headers = {
            'ngrok-skip-browser-warning': '_',
            'secret': self.secret,
            'Content-Type': 'application/json'
        }
        
        json_data = {
            "site_url": site_url,
            "site_key": self.site_key
        }
        
        try:
            print(f"🔄 请求 Turnstile Token...")
            response = requests.get(
                url=url,
                headers=headers,
                json=json_data,
                timeout=120  # 增加超时时间
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'OK' and data.get('token'):
                    print(f"✅ Turnstile Token 获取成功!")
                    print(f"   Token: {data['token'][:50]}...")
                    return data['token']
                else:
                    print(f"❌ Turnstile 解决失败: {data}")
                    return None
            else:
                print(f"❌ 服务器响应错误: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Turnstile Solver 请求失败: {e}")
            return None

def generate_credentials():
    """生成注册凭据"""
    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
    email = f"{username}@mlanm.online"
    password = "Aa88888888."
    return email, username, password

def setup_ultimate_driver():
    """设置终极反检测 Chrome 驱动"""
    chrome_options = Options()
    
    # 基本反检测设置
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 高级反检测设置
    chrome_options.add_argument('--disable-plugins-discovery')
    chrome_options.add_argument('--disable-default-apps')
    chrome_options.add_argument('--disable-extensions-file-access-check')
    chrome_options.add_argument('--disable-extensions-http-throttling')
    chrome_options.add_argument('--disable-component-extensions-with-background-pages')
    chrome_options.add_argument('--disable-background-timer-throttling')
    chrome_options.add_argument('--disable-renderer-backgrounding')
    chrome_options.add_argument('--disable-backgrounding-occluded-windows')
    
    # 设置真实的用户代理和其他属性
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    chrome_options.add_argument('--window-size=1920,1080')
    
    # 禁用图片和媒体以提高速度
    prefs = {
        "profile.managed_default_content_settings.images": 2,
        "profile.default_content_setting_values.notifications": 2,
        "profile.default_content_settings.popups": 0,
        "profile.managed_default_content_settings.media_stream": 2,
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    # 执行高级反检测脚本
    ultimate_stealth_script = """
    // 终极反检测脚本
    Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
    Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
    Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']});
    Object.defineProperty(navigator, 'platform', {get: () => 'MacIntel'});
    Object.defineProperty(navigator, 'hardwareConcurrency', {get: () => 8});
    Object.defineProperty(navigator, 'deviceMemory', {get: () => 8});
    
    window.chrome = {
        runtime: {},
        loadTimes: function() {},
        csi: function() {},
        app: {}
    };
    
    // 覆盖 WebGL 指纹
    const getParameter = WebGLRenderingContext.getParameter;
    WebGLRenderingContext.prototype.getParameter = function(parameter) {
        if (parameter === 37445) {
            return 'Intel Inc.';
        }
        if (parameter === 37446) {
            return 'Intel Iris OpenGL Engine';
        }
        return getParameter(parameter);
    };
    
    // 覆盖 Canvas 指纹
    const toDataURL = HTMLCanvasElement.prototype.toDataURL;
    HTMLCanvasElement.prototype.toDataURL = function(type) {
        if (type === 'image/png') {
            return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
        }
        return toDataURL.apply(this, arguments);
    };
    """
    
    driver.execute_script(ultimate_stealth_script)
    
    return driver

def ultimate_automated_register():
    """终极自动化注册"""
    print("🎯 终极自动化 DDAI 注册脚本")
    print("🚀 Turnstile Solver + CloudScraper + 高级反检测")
    print("=" * 60)
    
    # 初始化 Turnstile 解决器
    solver = UltimateTurnstileSolver()
    
    # 生成凭据
    email, username, password = generate_credentials()
    
    print(f"📧 邮箱: {email}")
    print(f"👤 用户名: {username}")
    print(f"🔐 密码: {password}")
    print()
    
    # 1. 使用 Turnstile Solver 获取 Token
    site_url = "https://app.ddai.space/register?ref=Bp48g624"
    print("🔄 获取 Turnstile Token...")
    
    token = solver.get_turnstile_token(site_url)
    
    if not token:
        print("❌ 无法获取 Turnstile Token，注册失败")
        return False
    
    # 2. 使用 CloudScraper 预处理
    print("🌩️  使用 CloudScraper 预处理...")
    scraper = cloudscraper.create_scraper()
    
    try:
        response = scraper.get(site_url)
        cookies = response.cookies
        print("✅ CloudScraper 预处理完成")
    except Exception as e:
        print(f"⚠️  CloudScraper 预处理失败: {e}")
        cookies = None
    
    # 3. 启动终极反检测浏览器
    print("🌐 启动终极反检测浏览器...")
    driver = setup_ultimate_driver()
    
    try:
        # 添加 CloudScraper cookies
        if cookies:
            driver.get("https://app.ddai.space")
            for cookie in cookies:
                try:
                    driver.add_cookie({
                        'name': cookie.name,
                        'value': cookie.value,
                        'domain': cookie.domain,
                        'path': cookie.path
                    })
                except:
                    pass
        
        # 4. 打开注册页面
        print("📍 打开注册页面...")
        driver.get(site_url)
        
        # 等待页面加载
        print("⏳ 等待页面加载...")
        time.sleep(8)
        
        # 5. 填写表单
        print("📝 开始填写注册表单...")
        
        # 等待表单元素
        WebDriverWait(driver, 30).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, '[name="email"]'))
        )
        
        # 填写邮箱
        email_field = driver.find_element(By.CSS_SELECTOR, '[name="email"]')
        email_field.clear()
        time.sleep(0.5)
        email_field.send_keys(email)
        print(f"✅ 邮箱已填写: {email}")
        
        # 填写用户名
        username_field = driver.find_element(By.CSS_SELECTOR, '[name="username"]')
        username_field.clear()
        time.sleep(0.5)
        username_field.send_keys(username)
        print(f"✅ 用户名已填写: {username}")
        
        # 填写密码
        password_field = driver.find_element(By.CSS_SELECTOR, '[name="password"]')
        password_field.clear()
        time.sleep(0.5)
        password_field.send_keys(password)
        print(f"✅ 密码已填写: {password}")
        
        # 填写确认密码
        confirm_password_field = driver.find_element(By.CSS_SELECTOR, '[name="confirmPassword"]')
        confirm_password_field.clear()
        time.sleep(0.5)
        confirm_password_field.send_keys(password)
        print(f"✅ 确认密码已填写: {password}")
        
        # 6. 注入 Turnstile Token
        print("\n🎯 注入 Turnstile Token...")
        
        inject_script = """
        var tokenField = document.querySelector('[name="cf-turnstile-response"]');
        if (tokenField) {
            tokenField.value = arguments[0];
            
            // 触发 change 事件
            var event = new Event('change', { bubbles: true });
            tokenField.dispatchEvent(event);
            
            // 触发 input 事件
            var inputEvent = new Event('input', { bubbles: true });
            tokenField.dispatchEvent(inputEvent);
            
            console.log('Token 注入成功:', arguments[0].substring(0, 50) + '...');
            return true;
        } else {
            console.error('找不到 cf-turnstile-response 字段');
            return false;
        }
        """
        
        result = driver.execute_script(inject_script, token)
        
        if result:
            print("✅ Turnstile Token 注入成功!")
        else:
            print("❌ Turnstile Token 注入失败!")
            return False
        
        # 等待一下确保 Token 被识别
        time.sleep(3)
        
        # 7. 提交表单
        print("\n🚀 提交注册表单...")
        
        submit_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"]'))
        )
        
        # 使用 JavaScript 点击以避免拦截
        driver.execute_script("arguments[0].click();", submit_button)
        print("✅ 注册按钮已点击")
        
        # 8. 等待结果
        print("⏳ 等待注册结果...")
        
        initial_url = driver.current_url
        start_time = time.time()
        
        while time.time() - start_time < 30:
            current_url = driver.current_url
            print(f"📍 当前 URL: {current_url}")
            
            # 检查是否跳转
            if current_url != initial_url:
                if 'login' in current_url:
                    print("🎉 已跳转到登录页面，注册可能成功!")
                    
                    # 保存注册信息
                    save_registration_info(email, username, password, current_url, token)
                    
                    # 尝试登录验证
                    return verify_login(driver, username, password)
                
                elif 'dashboard' in current_url:
                    print("🎉 直接跳转到仪表板，注册成功!")
                    save_registration_info(email, username, password, current_url, token)
                    return True
                
                else:
                    print(f"🔍 跳转到其他页面: {current_url}")
            
            # 检查页面内容
            try:
                page_text = driver.execute_script("return document.body.innerText;").lower()
                
                # 检查失败关键词
                if any(word in page_text for word in ['captcha verification failed', 'turnstile failed', 'verification failed']):
                    print("❌ 检测到验证失败关键词!")
                    print(f"页面内容片段: {page_text[:300]}...")
                    return False
                
                # 检查成功关键词
                elif any(word in page_text for word in ['success', 'welcome', 'dashboard']) and 'failed' not in page_text:
                    print("🎉 检测到成功关键词!")
                    save_registration_info(email, username, password, current_url, token)
                    return True
                
            except:
                pass
            
            time.sleep(3)
        
        print("⏰ 等待超时")
        return False
    
    except Exception as e:
        print(f"❌ 注册过程出错: {e}")
        return False
    
    finally:
        print("\n⏳ 保持浏览器打开 20 秒以便查看结果...")
        time.sleep(20)
        driver.quit()

def save_registration_info(email, username, password, final_url, token):
    """保存注册信息"""
    registration_data = {
        'email': email,
        'username': username,
        'password': password,
        'token': token,
        'registration_url': 'https://app.ddai.space/register?ref=Bp48g624',
        'final_url': final_url,
        'registration_time': time.strftime('%Y-%m-%d %H:%M:%S'),
        'method': 'ultimate_automated_combo',
        'success': True
    }
    
    filename = f'ultimate_registration_{int(time.time())}.json'
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(registration_data, f, indent=2, ensure_ascii=False)
    
    print(f"📁 注册信息已保存到: {filename}")

def verify_login(driver, username, password):
    """验证登录"""
    try:
        print("\n🔐 开始登录验证...")
        
        # 如果不在登录页面，导航到登录页面
        if 'login' not in driver.current_url:
            driver.get("https://app.ddai.space/login")
            time.sleep(3)
        
        # 填写用户名
        username_field = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, '[name="usernameOrEmail"]'))
        )
        username_field.clear()
        username_field.send_keys(username)
        print(f"✅ 登录用户名已填写: {username}")
        
        # 填写密码
        password_field = driver.find_element(By.CSS_SELECTOR, '[name="password"]')
        password_field.clear()
        password_field.send_keys(password)
        print(f"✅ 登录密码已填写")
        
        # 点击登录按钮
        login_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
        driver.execute_script("arguments[0].click();", login_button)
        print("✅ 登录按钮已点击")
        
        # 等待登录结果
        time.sleep(8)
        
        current_url = driver.current_url
        print(f"📍 登录后 URL: {current_url}")
        
        if 'dashboard' in current_url or ('login' not in current_url and 'register' not in current_url):
            print("🎉 登录成功！注册验证通过！")
            return True
        else:
            print("❌ 登录失败，可能注册未成功")
            return False
    
    except Exception as e:
        print(f"❌ 登录验证失败: {e}")
        return False

def main():
    """主函数"""
    success = ultimate_automated_register()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 终极自动化 DDAI 注册成功!")
        print("✅ 账户已创建并验证")
        print("🚀 完全自动化，无需任何手动操作!")
    else:
        print("❌ 注册失败")
        print("💡 请检查 Turnstile Solver 服务状态")
    print("=" * 60)

if __name__ == "__main__":
    main()
