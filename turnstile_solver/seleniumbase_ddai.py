#!/usr/bin/env python3
"""
使用 SeleniumBase 的 DDAI 自动注册脚本
SeleniumBase 有内置的 Cloudflare 绕过功能
"""

import json
import time
import random
import string
from seleniumbase import BaseCase

class DDaiRegistration(BaseCase):
    def generate_credentials(self):
        """生成注册凭据"""
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        email = f"{username}@mlanm.online"
        password = "Aa88888888."
        return email, username, password
    
    def save_registration_info(self, email, username, password, final_url, token=None):
        """保存注册信息"""
        registration_data = {
            'email': email,
            'username': username,
            'password': password,
            'token': token,
            'registration_url': 'https://app.ddai.space/register?ref=Bp48g624',
            'final_url': final_url,
            'registration_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'method': 'seleniumbase_automated',
            'success': True
        }
        
        filename = f'seleniumbase_registration_{int(time.time())}.json'
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(registration_data, f, indent=2, ensure_ascii=False)
        
        print(f"📁 注册信息已保存到: {filename}")
    
    def test_ddai_registration(self):
        """测试 DDAI 注册"""
        print("🎯 SeleniumBase DDAI 自动注册")
        print("🚀 使用内置 Cloudflare 绕过功能")
        print("=" * 60)
        
        # 生成凭据
        email, username, password = self.generate_credentials()
        
        print(f"📧 邮箱: {email}")
        print(f"👤 用户名: {username}")
        print(f"🔐 密码: {password}")
        print()
        
        try:
            # 1. 打开注册页面
            print("📍 打开注册页面...")
            self.open("https://app.ddai.space/register?ref=Bp48g624")
            
            # 等待页面加载
            print("⏳ 等待页面加载...")
            self.sleep(8)
            
            # 2. 检查并处理 Cloudflare 挑战
            print("🔍 检查 Cloudflare 挑战...")
            
            # SeleniumBase 会自动处理 Cloudflare 挑战
            # 我们只需要等待页面完全加载
            self.wait_for_element('[name="email"]', timeout=30)
            
            # 3. 填写表单
            print("📝 开始填写注册表单...")
            
            # 填写邮箱
            print("✏️  填写邮箱...")
            self.type('[name="email"]', email)
            print(f"✅ 邮箱已填写: {email}")
            
            # 填写用户名
            print("✏️  填写用户名...")
            self.type('[name="username"]', username)
            print(f"✅ 用户名已填写: {username}")
            
            # 填写密码
            print("✏️  填写密码...")
            self.type('[name="password"]', password)
            print(f"✅ 密码已填写: {password}")
            
            # 填写确认密码
            print("✏️  填写确认密码...")
            self.type('[name="confirmPassword"]', password)
            print(f"✅ 确认密码已填写: {password}")
            
            # 4. 等待 Turnstile 完成
            print("\n⏳ 等待 Turnstile 验证完成...")
            
            # 等待 Turnstile Token 生成
            token = None
            max_wait = 120
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                try:
                    token_element = self.find_element('[name="cf-turnstile-response"]')
                    token_value = token_element.get_attribute('value')
                    
                    if token_value and len(token_value) > 100:
                        token = token_value
                        print(f"✅ Turnstile Token 已生成: {token[:50]}...")
                        break
                    
                    # 尝试查找并点击 Turnstile 复选框
                    try:
                        turnstile_checkbox = self.find_elements('input[type="checkbox"]')
                        for checkbox in turnstile_checkbox:
                            if checkbox.is_displayed() and checkbox.is_enabled():
                                self.click_with_offset(checkbox, 0, 0)
                                print("🔄 点击了 Turnstile 复选框")
                                self.sleep(3)
                                break
                    except:
                        pass
                    
                    self.sleep(2)
                    
                except Exception as e:
                    self.sleep(2)
            
            if not token:
                print("⚠️  未获取到 Turnstile Token，但继续尝试提交...")
            
            # 5. 提交表单
            print("\n🚀 提交注册表单...")
            
            # 等待提交按钮可点击
            self.wait_for_element('button[type="submit"]', timeout=10)
            
            # 点击提交按钮
            self.click('button[type="submit"]')
            print("✅ 注册按钮已点击")
            
            # 6. 等待结果
            print("⏳ 等待注册结果...")
            
            initial_url = self.get_current_url()
            start_time = time.time()
            
            while time.time() - start_time < 30:
                current_url = self.get_current_url()
                
                print(f"📍 当前 URL: {current_url}")
                
                # 检查是否跳转
                if current_url != initial_url:
                    if 'login' in current_url:
                        print("🎉 已跳转到登录页面，注册可能成功!")
                        
                        # 保存注册信息
                        self.save_registration_info(email, username, password, current_url, token)
                        
                        # 尝试登录验证
                        return self.verify_login(username, password)
                    
                    elif 'dashboard' in current_url:
                        print("🎉 直接跳转到仪表板，注册成功!")
                        self.save_registration_info(email, username, password, current_url, token)
                        return True
                    
                    else:
                        print(f"🔍 跳转到其他页面: {current_url}")
                
                # 检查页面内容
                try:
                    page_text = self.get_text('body').lower()
                    
                    if any(word in page_text for word in ['success', 'welcome', 'dashboard']):
                        print("🎉 检测到成功关键词!")
                        self.save_registration_info(email, username, password, current_url, token)
                        return True
                    
                    elif any(word in page_text for word in ['error', 'failed', 'invalid', 'expired']):
                        print("❌ 检测到错误关键词!")
                        print(f"页面内容片段: {page_text[:200]}...")
                        return False
                except:
                    pass
                
                self.sleep(3)
            
            print("⏰ 等待超时")
            return False
        
        except Exception as e:
            print(f"❌ 注册过程出错: {e}")
            return False
    
    def verify_login(self, username, password):
        """验证登录"""
        try:
            print("\n🔐 开始登录验证...")
            
            # 如果不在登录页面，导航到登录页面
            if 'login' not in self.get_current_url():
                self.open("https://app.ddai.space/login")
                self.sleep(3)
            
            # 填写用户名
            self.type('[name="usernameOrEmail"]', username)
            print(f"✅ 登录用户名已填写: {username}")
            
            # 填写密码
            self.type('[name="password"]', password)
            print(f"✅ 登录密码已填写")
            
            # 点击登录按钮
            self.click('button[type="submit"]')
            print("✅ 登录按钮已点击")
            
            # 等待登录结果
            self.sleep(8)
            
            current_url = self.get_current_url()
            print(f"📍 登录后 URL: {current_url}")
            
            if 'dashboard' in current_url or ('login' not in current_url and 'register' not in current_url):
                print("🎉 登录成功！注册验证通过！")
                return True
            else:
                print("❌ 登录失败，可能注册未成功")
                return False
        
        except Exception as e:
            print(f"❌ 登录验证失败: {e}")
            return False

def main():
    """主函数"""
    # 创建测试实例
    test_case = DDaiRegistration()
    
    # 设置 SeleniumBase 参数
    test_case.setUp()
    
    try:
        # 运行注册测试
        success = test_case.test_ddai_registration()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 SeleniumBase DDAI 注册成功!")
            print("✅ 账户已创建并验证")
            print("🚀 使用 SeleniumBase 成功绕过 Cloudflare!")
        else:
            print("❌ 注册失败")
            print("💡 可能需要进一步优化策略")
        print("=" * 60)
        
    finally:
        # 保持浏览器打开一段时间以便查看结果
        print("⏳ 保持浏览器打开 20 秒以便查看结果...")
        test_case.sleep(20)
        test_case.tearDown()

if __name__ == "__main__":
    main()
