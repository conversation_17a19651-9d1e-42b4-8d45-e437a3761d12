#!/usr/bin/env python3
"""
DDAI网站 Turnstile 分析和绕过脚本
专门用于分析 https://app.ddai.space/register 的 Turnstile 验证
"""

import asyncio
import json
import time
import requests
from patchright.async_api import async_playwright

async def analyze_ddai_site():
    """分析DDAI网站的Turnstile配置"""
    print("🔍 正在分析 DDAI 网站...")
    print("=" * 60)
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context(
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        page = await context.new_page()
        
        try:
            print("📡 正在访问注册页面...")
            await page.goto("https://app.ddai.space/register", wait_until="networkidle")
            
            # 等待页面完全加载
            await page.wait_for_timeout(3000)
            
            print("🔍 正在查找 Turnstile 配置...")
            
            # 方法1: 查找 cf-turnstile 元素
            turnstile_elements = await page.query_selector_all('[class*="cf-turnstile"], [data-sitekey], .cf-turnstile')
            
            site_key = None
            
            if turnstile_elements:
                print(f"✅ 找到 {len(turnstile_elements)} 个 Turnstile 元素")
                
                for i, element in enumerate(turnstile_elements):
                    # 获取 data-sitekey 属性
                    sitekey = await element.get_attribute('data-sitekey')
                    if sitekey:
                        site_key = sitekey
                        print(f"🎯 找到 site_key: {site_key}")
                        break
                    
                    # 获取所有属性
                    attrs = await page.evaluate('(element) => { const attrs = {}; for (let attr of element.attributes) { attrs[attr.name] = attr.value; } return attrs; }', element)
                    print(f"元素 {i+1} 属性:", attrs)
            
            # 方法2: 在页面源码中搜索
            if not site_key:
                print("🔍 在页面源码中搜索 site_key...")
                content = await page.content()
                
                # 搜索常见的 site_key 模式
                import re
                patterns = [
                    r'data-sitekey["\s]*=["\s]*([^"\'>\s]+)',
                    r'sitekey["\s]*:["\s]*["\']([^"\']+)',
                    r'"siteKey"["\s]*:["\s]*["\']([^"\']+)',
                    r'0x[A-Za-z0-9_-]{20,}',
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        site_key = matches[0]
                        print(f"🎯 通过正则表达式找到 site_key: {site_key}")
                        break
            
            # 方法3: 监听网络请求
            if not site_key:
                print("🌐 监听网络请求...")
                
                def handle_request(request):
                    if 'turnstile' in request.url.lower() or 'cloudflare' in request.url.lower():
                        print(f"📡 Turnstile 相关请求: {request.url}")
                
                page.on("request", handle_request)
                
                # 尝试触发 Turnstile 加载
                try:
                    # 查找并点击可能触发 Turnstile 的元素
                    register_button = await page.query_selector('button[type="submit"], .register-btn, [class*="register"]')
                    if register_button:
                        print("🖱️  尝试触发 Turnstile...")
                        await register_button.scroll_into_view_if_needed()
                        await page.wait_for_timeout(2000)
                except Exception as e:
                    print(f"⚠️  触发失败: {e}")
            
            # 方法4: 执行 JavaScript 查找
            if not site_key:
                print("🔧 使用 JavaScript 查找...")
                site_key = await page.evaluate("""
                    () => {
                        // 查找所有可能的 Turnstile 元素
                        const selectors = [
                            '[data-sitekey]',
                            '.cf-turnstile',
                            '[class*="turnstile"]',
                            '[class*="cloudflare"]'
                        ];
                        
                        for (let selector of selectors) {
                            const elements = document.querySelectorAll(selector);
                            for (let el of elements) {
                                const sitekey = el.getAttribute('data-sitekey') || 
                                              el.getAttribute('sitekey') ||
                                              el.dataset.sitekey;
                                if (sitekey) return sitekey;
                            }
                        }
                        
                        // 在全局变量中查找
                        if (window.turnstile && window.turnstile.sitekey) {
                            return window.turnstile.sitekey;
                        }
                        
                        return null;
                    }
                """)
            
            print("\n" + "=" * 60)
            if site_key:
                print(f"🎉 成功找到 site_key: {site_key}")
                return site_key
            else:
                print("❌ 未能找到 site_key")
                print("💡 可能需要手动查找或网站使用了特殊的加载方式")
                return None
                
        except Exception as e:
            print(f"❌ 分析过程中出错: {e}")
            return None
        finally:
            await browser.close()

async def test_turnstile_solve(site_key):
    """测试 Turnstile 解决"""
    if not site_key:
        print("❌ 没有 site_key，无法测试")
        return None
    
    print(f"\n🚀 开始测试 Turnstile 解决...")
    print(f"网站: https://app.ddai.space/register")
    print(f"Site Key: {site_key}")
    
    # 调用本地的 Turnstile Solver
    try:
        response = requests.get(
            url="http://127.0.0.1:8088/solve",
            headers={
                'ngrok-skip-browser-warning': '_',
                'secret': 'jWRN7DH6',
                'Content-Type': 'application/json'
            },
            json={
                "site_url": "https://app.ddai.space/register",
                "site_key": site_key
            },
            timeout=120
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 解决成功!")
            print(f"⏱️  耗时: {data.get('elapsed', 'N/A')} 秒")
            print(f"🎫 Token: {data.get('token', 'N/A')[:50]}...")
            print(f"📝 状态: {data.get('status', 'N/A')}")
            
            # 保存结果
            timestamp = int(time.time())
            filename = f"ddai_turnstile_result_{timestamp}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"📁 结果已保存到: {filename}")
            
            return data
        else:
            print(f"❌ 服务器错误: {response.status_code}")
            print(f"响应: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到 Turnstile Solver 服务器")
        print("请确保服务器正在运行: ./start_solver.sh")
        return None
    except Exception as e:
        print(f"❌ 请求错误: {e}")
        return None

async def main():
    """主函数"""
    print("🎯 DDAI 网站 Turnstile 分析和绕过工具")
    print("=" * 60)
    
    # 第一步: 分析网站获取 site_key
    site_key = await analyze_ddai_site()
    
    if site_key:
        # 第二步: 测试 Turnstile 解决
        result = await test_turnstile_solve(site_key)
        
        if result and result.get('status') == 'OK':
            print("\n🎉 完整流程测试成功!")
            print("您现在可以使用这个 token 进行注册了!")
        else:
            print("\n❌ Turnstile 解决失败")
    else:
        print("\n💡 建议:")
        print("1. 手动在浏览器开发者工具中查找 data-sitekey")
        print("2. 或者提供更多页面信息")

if __name__ == "__main__":
    asyncio.run(main())
