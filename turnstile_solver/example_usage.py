#!/usr/bin/env python3
"""
Turnstile Solver 使用示例
这个脚本演示如何使用 turnstile_solver 来解决 Cloudflare Turnstile CAPTCHA

使用方法:
1. 首先启动 solver 服务器: solver --port 8088 --secret jWRN7DH6
2. 然后运行这个脚本: python3 example_usage.py

作者: 基于 odell0111/turnstile_solver 项目
"""

import requests
import json
import time
import sys

# 服务器配置
SERVER_URL = "http://127.0.0.1:8088"
SECRET = "jWRN7DH6"

def solve_turnstile(site_url, site_key):
    """
    解决 Turnstile CAPTCHA
    
    Args:
        site_url (str): 目标网站URL
        site_key (str): Turnstile site key
        
    Returns:
        dict: 包含token和其他信息的响应
    """
    url = f"{SERVER_URL}/solve"
    
    headers = {
        'ngrok-skip-browser-warning': '_',
        'secret': SECRET,
        'Content-Type': 'application/json'
    }
    
    json_data = {
        "site_url": site_url,
        "site_key": site_key
    }
    
    print(f"正在解决 Turnstile CAPTCHA...")
    print(f"网站: {site_url}")
    print(f"Site Key: {site_key}")
    print("请等待...")
    
    try:
        start_time = time.time()
        response = requests.get(
            url=url,
            headers=headers,
            json=json_data,
            timeout=60  # 60秒超时
        )
        
        response.raise_for_status()
        data = response.json()
        
        elapsed_time = time.time() - start_time
        
        print(f"\n✅ 解决成功!")
        print(f"⏱️  耗时: {elapsed_time:.2f} 秒")
        print(f"📊 服务器报告耗时: {data.get('elapsed', 'N/A')} 秒")
        print(f"🎫 Token: {data.get('token', 'N/A')[:50]}...")
        print(f"📝 状态: {data.get('status', 'N/A')}")
        print(f"💬 消息: {data.get('message', 'N/A')}")
        
        return data
        
    except requests.exceptions.Timeout:
        print("❌ 请求超时，请检查服务器是否正在运行")
        return None
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误，请确保服务器正在运行在 http://127.0.0.1:8088")
        return None
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求错误: {e}")
        return None
    except json.JSONDecodeError:
        print("❌ 响应格式错误")
        return None

def check_server_status():
    """检查服务器状态"""
    try:
        # 尝试访问根路径，403是正常的（需要认证）
        response = requests.get(f"{SERVER_URL}/", timeout=5)
        if response.status_code in [200, 403, 404]:
            print("✅ 服务器正在运行")
            return True
        else:
            print(f"⚠️  服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
        print("启动命令: solver --port 8088 --secret jWRN7DH6")
        return False
    except Exception as e:
        print(f"❌ 检查服务器状态时出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 Turnstile Solver 使用示例")
    print("=" * 60)
    
    # 检查服务器状态
    print("\n1. 检查服务器状态...")
    if not check_server_status():
        print("\n请先启动服务器:")
        print("solver --port 8088 --secret jWRN7DH6")
        sys.exit(1)
    
    # 示例网站和site key (来自项目README)
    test_cases = [
        {
            "name": "Spotify Down",
            "site_url": "https://spotifydown.com",
            "site_key": "0x4AAAAAAAByvC31sFG0MSlp"
        }
    ]
    
    print(f"\n2. 开始测试 {len(test_cases)} 个案例...")
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n--- 测试案例 {i}: {case['name']} ---")
        
        result = solve_turnstile(case['site_url'], case['site_key'])
        
        if result and result.get('status') == 'OK':
            print("🎉 测试成功!")
            
            # 保存结果到文件
            timestamp = int(time.time())
            filename = f"turnstile_result_{timestamp}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"📁 结果已保存到: {filename}")
            
        else:
            print("❌ 测试失败")
        
        # 如果有多个测试案例，等待一下
        if i < len(test_cases):
            print("⏳ 等待 3 秒后继续下一个测试...")
            time.sleep(3)
    
    print("\n" + "=" * 60)
    print("✨ 测试完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()
