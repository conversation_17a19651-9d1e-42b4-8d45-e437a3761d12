#!/usr/bin/env python3
"""
使用 CloudScraper 的完全自动化 DDAI 注册脚本
CloudScraper 专门用于绕过 Cloudflare 保护
"""

import json
import time
import random
import string
import cloudscraper
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def generate_credentials():
    """生成注册凭据"""
    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
    email = f"{username}@mlanm.online"
    password = "Aa88888888."
    return email, username, password

def setup_cloudscraper_session():
    """设置 CloudScraper 会话"""
    scraper = cloudscraper.create_scraper(
        browser={
            'browser': 'chrome',
            'platform': 'darwin',
            'desktop': True
        }
    )
    return scraper

def get_turnstile_token_with_cloudscraper(scraper, site_url):
    """使用 CloudScraper 获取 Turnstile Token"""
    print("🔄 使用 CloudScraper 获取页面...")
    
    try:
        # 访问注册页面
        response = scraper.get(site_url)
        
        if response.status_code == 200:
            print("✅ CloudScraper 成功访问页面")
            
            # 查找 Turnstile site key
            import re
            site_key_match = re.search(r'data-sitekey="([^"]+)"', response.text)
            if site_key_match:
                site_key = site_key_match.group(1)
                print(f"🔍 发现 Turnstile Site Key: {site_key}")
                
                # 尝试获取 Token（这里需要额外的处理）
                # CloudScraper 主要处理 JavaScript 挑战，对于 Turnstile 需要结合其他方法
                return site_key, response.cookies
            else:
                print("⚠️  未找到 Turnstile Site Key")
                return None, response.cookies
        else:
            print(f"❌ CloudScraper 访问失败: {response.status_code}")
            return None, None
            
    except Exception as e:
        print(f"❌ CloudScraper 出错: {e}")
        return None, None

def setup_selenium_with_cookies(cookies):
    """设置带有 CloudScraper cookies 的 Selenium"""
    chrome_options = Options()
    
    # 基本反检测设置
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 设置用户代理
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    # 反检测脚本
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    # 添加 CloudScraper 的 cookies
    if cookies:
        driver.get("https://app.ddai.space")
        for cookie in cookies:
            try:
                driver.add_cookie({
                    'name': cookie.name,
                    'value': cookie.value,
                    'domain': cookie.domain,
                    'path': cookie.path
                })
            except:
                pass
    
    return driver

def advanced_turnstile_solver(driver, max_wait=180):
    """高级 Turnstile 解决器"""
    print("🧠 启动高级 Turnstile 解决器...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        try:
            # 方法1: 检查 Token 是否已生成
            token_field = driver.find_element(By.CSS_SELECTOR, '[name="cf-turnstile-response"]')
            token_value = token_field.get_attribute('value')
            
            if token_value and len(token_value) > 100:
                print(f"✅ Turnstile Token 已生成: {token_value[:50]}...")
                return token_value
            
            # 方法2: 高级 JavaScript 注入
            js_solver = """
            // 高级 Turnstile 解决器
            function solveTurnstile() {
                // 查找所有可能的 Turnstile 元素
                const selectors = [
                    'iframe[src*="turnstile"]',
                    '[id*="turnstile"]',
                    '[class*="turnstile"]',
                    '.cf-turnstile',
                    'input[name="cf-turnstile-response"]'
                ];
                
                for (let selector of selectors) {
                    const elements = document.querySelectorAll(selector);
                    for (let element of elements) {
                        if (element.tagName === 'IFRAME') {
                            try {
                                // 尝试访问 iframe 内容
                                const iframeDoc = element.contentDocument || element.contentWindow.document;
                                const checkbox = iframeDoc.querySelector('input[type="checkbox"]');
                                if (checkbox && checkbox.offsetParent !== null) {
                                    // 模拟点击
                                    const event = new MouseEvent('click', {
                                        view: iframeDoc.defaultView,
                                        bubbles: true,
                                        cancelable: true
                                    });
                                    checkbox.dispatchEvent(event);
                                    console.log('✅ JavaScript 点击了 Turnstile 复选框');
                                    return true;
                                }
                            } catch (e) {
                                console.log('无法访问 iframe:', e);
                            }
                        } else {
                            // 查找元素内的复选框
                            const checkbox = element.querySelector('input[type="checkbox"]');
                            if (checkbox && checkbox.offsetParent !== null) {
                                checkbox.click();
                                console.log('✅ JavaScript 点击了 Turnstile 复选框');
                                return true;
                            }
                        }
                    }
                }
                
                // 尝试直接查找复选框
                const checkboxes = document.querySelectorAll('input[type="checkbox"]');
                for (let checkbox of checkboxes) {
                    if (checkbox.offsetParent !== null && !checkbox.disabled) {
                        checkbox.click();
                        console.log('✅ JavaScript 点击了复选框');
                        return true;
                    }
                }
                
                return false;
            }
            
            return solveTurnstile();
            """
            
            result = driver.execute_script(js_solver)
            if result:
                print("✅ JavaScript 解决器成功执行")
                time.sleep(5)  # 等待验证完成
                continue
            
            # 方法3: 模拟人类行为的点击
            try:
                # 查找所有可能的点击目标
                clickable_elements = driver.find_elements(By.CSS_SELECTOR, 
                    'input[type="checkbox"], '
                    '.cf-turnstile, '
                    '[id*="turnstile"], '
                    '[class*="turnstile"], '
                    'iframe[src*="turnstile"]'
                )
                
                for element in clickable_elements:
                    if element.is_displayed():
                        try:
                            # 滚动到元素
                            driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element)
                            time.sleep(1)
                            
                            # 模拟鼠标移动和点击
                            from selenium.webdriver.common.action_chains import ActionChains
                            actions = ActionChains(driver)
                            actions.move_to_element(element)
                            actions.pause(random.uniform(0.5, 2.0))
                            actions.click()
                            actions.perform()
                            
                            print(f"🔄 点击了元素: {element.tag_name}")
                            time.sleep(3)
                            break
                            
                        except Exception as e:
                            continue
                            
            except Exception as e:
                pass
            
            # 方法4: iframe 处理
            try:
                iframes = driver.find_elements(By.CSS_SELECTOR, 'iframe')
                for iframe in iframes:
                    if 'turnstile' in iframe.get_attribute('src') or '':
                        try:
                            driver.switch_to.frame(iframe)
                            
                            # 在 iframe 中查找复选框
                            checkbox = driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
                            if checkbox.is_displayed():
                                from selenium.webdriver.common.action_chains import ActionChains
                                actions = ActionChains(driver)
                                actions.move_to_element(checkbox)
                                actions.pause(random.uniform(1.0, 2.0))
                                actions.click()
                                actions.perform()
                                
                                print("✅ 在 iframe 中点击了 Turnstile 复选框")
                                
                            driver.switch_to.default_content()
                            time.sleep(5)
                            break
                            
                        except Exception as e:
                            driver.switch_to.default_content()
                            continue
                            
            except Exception as e:
                pass
            
            time.sleep(2)
            
        except Exception as e:
            time.sleep(2)
    
    print("⏰ 高级 Turnstile 解决器超时")
    return None

def cloudscraper_automated_register():
    """使用 CloudScraper 的完全自动化注册"""
    print("🎯 CloudScraper 完全自动化 DDAI 注册")
    print("🌩️  使用 CloudScraper + Selenium 组合")
    print("=" * 60)
    
    # 生成凭据
    email, username, password = generate_credentials()
    
    print(f"📧 邮箱: {email}")
    print(f"👤 用户名: {username}")
    print(f"🔐 密码: {password}")
    print()
    
    # 1. 使用 CloudScraper 预处理
    scraper = setup_cloudscraper_session()
    site_url = "https://app.ddai.space/register?ref=Bp48g624"
    
    site_key, cookies = get_turnstile_token_with_cloudscraper(scraper, site_url)
    
    # 2. 设置 Selenium 并传递 cookies
    print("🌐 启动 Selenium 浏览器...")
    driver = setup_selenium_with_cookies(cookies)
    
    try:
        # 3. 打开注册页面
        print("📍 打开注册页面...")
        driver.get(site_url)
        
        # 等待页面加载
        print("⏳ 等待页面加载...")
        time.sleep(8)
        
        # 4. 填写表单
        print("📝 开始填写注册表单...")
        
        # 等待表单元素
        WebDriverWait(driver, 30).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, '[name="email"]'))
        )
        
        # 填写邮箱
        email_field = driver.find_element(By.CSS_SELECTOR, '[name="email"]')
        email_field.clear()
        email_field.send_keys(email)
        print(f"✅ 邮箱已填写: {email}")
        
        # 填写用户名
        username_field = driver.find_element(By.CSS_SELECTOR, '[name="username"]')
        username_field.clear()
        username_field.send_keys(username)
        print(f"✅ 用户名已填写: {username}")
        
        # 填写密码
        password_field = driver.find_element(By.CSS_SELECTOR, '[name="password"]')
        password_field.clear()
        password_field.send_keys(password)
        print(f"✅ 密码已填写: {password}")
        
        # 填写确认密码
        confirm_password_field = driver.find_element(By.CSS_SELECTOR, '[name="confirmPassword"]')
        confirm_password_field.clear()
        confirm_password_field.send_keys(password)
        print(f"✅ 确认密码已填写: {password}")
        
        # 5. 高级 Turnstile 处理
        token = advanced_turnstile_solver(driver, max_wait=180)
        
        if not token:
            print("⚠️  自动处理失败，但继续尝试提交...")
        
        # 6. 提交表单
        print("\n🚀 提交注册表单...")
        
        submit_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"]'))
        )
        
        driver.execute_script("arguments[0].click();", submit_button)
        print("✅ 注册按钮已点击")
        
        # 7. 等待结果
        print("⏳ 等待注册结果...")
        
        initial_url = driver.current_url
        start_time = time.time()
        
        while time.time() - start_time < 30:
            current_url = driver.current_url
            print(f"📍 当前 URL: {current_url}")
            
            if current_url != initial_url:
                if 'login' in current_url or 'dashboard' in current_url:
                    print("🎉 页面跳转，注册可能成功!")
                    
                    # 保存注册信息
                    save_registration_info(email, username, password, current_url, token)
                    
                    print("\n" + "=" * 60)
                    print("🎉 CloudScraper DDAI 注册成功!")
                    print("✅ 账户已创建")
                    print("🌩️  CloudScraper + Selenium 组合成功!")
                    print("=" * 60)
                    
                    return True
            
            # 检查页面内容
            try:
                page_text = driver.execute_script("return document.body.innerText;").lower()
                
                if any(word in page_text for word in ['success', 'welcome', 'dashboard']):
                    print("🎉 检测到成功关键词!")
                    save_registration_info(email, username, password, current_url, token)
                    return True
                
                elif any(word in page_text for word in ['error', 'failed', 'invalid']):
                    print("❌ 检测到错误关键词!")
                    return False
            except:
                pass
            
            time.sleep(3)
        
        print("⏰ 等待超时")
        return False
    
    except Exception as e:
        print(f"❌ 注册过程出错: {e}")
        return False
    
    finally:
        print("⏳ 保持浏览器打开 20 秒以便查看结果...")
        time.sleep(20)
        driver.quit()

def save_registration_info(email, username, password, final_url, token):
    """保存注册信息"""
    registration_data = {
        'email': email,
        'username': username,
        'password': password,
        'token': token,
        'registration_url': 'https://app.ddai.space/register?ref=Bp48g624',
        'final_url': final_url,
        'registration_time': time.strftime('%Y-%m-%d %H:%M:%S'),
        'method': 'cloudscraper_selenium_combo',
        'success': True
    }
    
    filename = f'cloudscraper_registration_{int(time.time())}.json'
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(registration_data, f, indent=2, ensure_ascii=False)
    
    print(f"📁 注册信息已保存到: {filename}")

def main():
    """主函数"""
    success = cloudscraper_automated_register()
    
    if not success:
        print("\n" + "=" * 60)
        print("❌ 注册失败")
        print("💡 可能需要进一步优化策略")
        print("=" * 60)

if __name__ == "__main__":
    main()
