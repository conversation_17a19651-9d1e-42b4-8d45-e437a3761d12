{"Name": "ddai", "CreationDate": "2025-7-10", "Commands": [{"Command": "open", "Target": "https://app.ddai.space/register?ref=Bp48g624", "Value": "", "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"root\"]/div/div[2]", "Value": "", "Targets": ["xpath=//*[@id=\"root\"]/div/div[2]", "xpath=//body/div/div/div[2]", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC"], "Description": ""}, {"Command": "click", "Target": "name=email", "Value": "", "Targets": ["name=email", "xpath=//*[@id=\"root\"]/div/div[2]/div/form/div/input", "xpath=//input[@name='email']", "xpath=//input", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC > div > form > div:nth-child(1) > input"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"root\"]/div/div[2]/div/form/div", "Value": "", "Targets": ["xpath=//*[@id=\"root\"]/div/div[2]/div/form/div", "xpath=//form/div", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC > div > form > div:nth-child(1)"], "Description": ""}, {"Command": "type", "Target": "name=email", "Value": "<EMAIL>", "Targets": ["name=email", "xpath=//*[@id=\"root\"]/div/div[2]/div/form/div/input", "xpath=//input[@name='email']", "xpath=//input", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC > div > form > div:nth-child(1) > input"], "Description": ""}, {"Command": "click", "Target": "name=username", "Value": "", "Targets": ["name=username", "xpath=//*[@id=\"root\"]/div/div[2]/div/form/div[2]/input", "xpath=//input[@name='username']", "xpath=//div[2]/input", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC > div > form > div:nth-child(2) > input"], "Description": ""}, {"Command": "type", "Target": "name=username", "Value": "azvoec5743", "Targets": ["name=username", "xpath=//*[@id=\"root\"]/div/div[2]/div/form/div[2]/input", "xpath=//input[@name='username']", "xpath=//div[2]/input", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC > div > form > div:nth-child(2) > input"], "Description": ""}, {"Command": "click", "Target": "name=password", "Value": "", "Targets": ["name=password", "xpath=//*[@id=\"root\"]/div/div[2]/div/form/div[3]/input", "xpath=//input[@name='password']", "xpath=//div[3]/input", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC > div > form > div:nth-child(3) > input"], "Description": ""}, {"Command": "type", "Target": "name=password", "Value": "Aa88888888.", "Targets": ["name=password", "xpath=//*[@id=\"root\"]/div/div[2]/div/form/div[3]/input", "xpath=//input[@name='password']", "xpath=//div[3]/input", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC > div > form > div:nth-child(3) > input"], "Description": ""}, {"Command": "click", "Target": "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC > div > form > div:nth-child(3) > button > svg", "Value": "", "Targets": ["xpath=//*[@id=\"root\"]/div/div[2]/div/form/div[3]/button/svg", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC > div > form > div:nth-child(3) > button > svg"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"root\"]/div/div[2]/div", "Value": "", "Targets": ["xpath=//*[@id=\"root\"]/div/div[2]/div", "xpath=//body/div/div/div[2]/div", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC > div"], "Description": ""}, {"Command": "click", "Target": "name=confirmPassword", "Value": "", "Targets": ["name=confirmPassword", "xpath=//*[@id=\"root\"]/div/div[2]/div/form/div[5]/input", "xpath=//input[@name='confirmPassword']", "xpath=//div[5]/input", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC > div > form > div:nth-child(5) > input"], "Description": ""}, {"Command": "click", "Target": "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC > div > form > div:nth-child(5) > button > svg > circle", "Value": "", "Targets": ["xpath=//*[@id=\"root\"]/div/div[2]/div/form/div[5]/button/svg/circle", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC > div > form > div:nth-child(5) > button > svg > circle"], "Description": ""}, {"Command": "click", "Target": "name=confirmPassword", "Value": "", "Targets": ["name=confirmPassword", "xpath=//*[@id=\"root\"]/div/div[2]/div/form/div[5]/input", "xpath=//input[@name='confirmPassword']", "xpath=//div[5]/input", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC > div > form > div:nth-child(5) > input"], "Description": ""}, {"Command": "type", "Target": "name=confirmPassword", "Value": "Aa88888888.", "Targets": ["name=confirmPassword", "xpath=//*[@id=\"root\"]/div/div[2]/div/form/div[5]/input", "xpath=//input[@name='confirmPassword']", "xpath=//div[5]/input", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC > div > form > div:nth-child(5) > input"], "Description": ""}, {"Command": "click", "Target": "xpath=//button[@type='submit']", "Value": "", "Targets": ["xpath=//button[@type='submit']", "xpath=//form/button", "css=button[type='submit']", "xpath=//*[@id=\"root\"]/div/div[2]/div/form/button", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-bZABGC.gXTwnC > div > form > button"], "Description": "Register submit button"}, {"Command": "wait", "Target": "5000", "Value": "", "Description": "Wait for registration to complete and redirect to login"}, {"Command": "click", "Target": "name=usernameOrEmail", "Value": "", "Targets": ["name=usernameOrEmail", "xpath=//*[@id=\"root\"]/div/div[2]/div/div/form/div/input", "xpath=//input[@name='usernameOrEmail']", "xpath=//input", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-leMUev.kHntLl > div > div > form > div:nth-child(1) > input"], "Description": ""}, {"Command": "type", "Target": "name=usernameOrEmail", "Value": "fafafa888665", "Targets": ["name=usernameOrEmail", "xpath=//*[@id=\"root\"]/div/div[2]/div/div/form/div/input", "xpath=//input[@name='usernameOrEmail']", "xpath=//input", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-leMUev.kHntLl > div > div > form > div:nth-child(1) > input"], "Description": ""}, {"Command": "click", "Target": "name=password", "Value": "", "Targets": ["name=password", "xpath=//*[@id=\"root\"]/div/div[2]/div/div/form/div[2]/input", "xpath=//input[@name='password']", "xpath=//div[2]/input", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-leMUev.kHntLl > div > div > form > div:nth-child(2) > input"], "Description": ""}, {"Command": "type", "Target": "name=password", "Value": "", "Targets": ["name=password", "xpath=//*[@id=\"root\"]/div/div[2]/div/div/form/div[2]/input", "xpath=//input[@name='password']", "xpath=//div[2]/input", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-leMUev.kHntLl > div > div > form > div:nth-child(2) > input"], "Description": ""}, {"Command": "type", "Target": "name=password", "Value": "Aa88888888.", "Targets": ["name=password", "xpath=//*[@id=\"root\"]/div/div[2]/div/div/form/div[2]/input", "xpath=//input[@name='password']", "xpath=//div[2]/input", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-leMUev.kHntLl > div > div > form > div:nth-child(2) > input"], "Description": ""}, {"Command": "click", "Target": "xpath=//button[@type='submit']", "Value": "", "Targets": ["xpath=//button[@type='submit']", "xpath=//form/button", "css=button[type='submit']", "xpath=//*[@id=\"root\"]/div/div[2]/div/div/form/button", "xpath=//form/button/font/font", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-leMUev.kHntLl > div > div > form > button"], "Description": "Login submit button with multiple fallback locators"}, {"Command": "wait", "Target": "3000", "Value": "", "Description": "Wait for login to process"}, {"Command": "click", "Target": "xpath=//*[@id=\"root\"]/div/div[2]/div/div/button/font/font", "Value": "", "Targets": ["xpath=//*[@id=\"root\"]/div/div[2]/div/div/button/font/font", "xpath=//button/font/font", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-lmsKoi.OeXlZ > div.sc-Rjrgp.haIJPA > div > button > font > font"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"root\"]/div/div/div[2]/div[3]/button/div/div[2]/p/font/font", "Value": "", "Targets": ["xpath=//*[@id=\"root\"]/div/div/div[2]/div[3]/button/div/div[2]/p/font/font", "xpath=//p/font/font", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-hPGoDJ.bbTFzS > div.container.flex.justify-between.align-center > div.wallet-button-container > button > div > div:nth-child(2) > p > font > font"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"root\"]/div/div/div[2]/div/div[3]/div/div/font/font", "Value": "", "Targets": ["xpath=//*[@id=\"root\"]/div/div/div[2]/div/div[3]/div/div/font/font", "xpath=//div[3]/div/div/font/font", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-hPGoDJ.bbTFzS > div.sc-dSfWjt.cTaEIw > div > div.bottom-buttons > div.sc-geXuza.juHNdf.center > div.content > font > font"], "Description": ""}, {"Command": "click", "Target": "xpath=/html/body/div[3]/div/div/ul/li/button", "Value": "", "Targets": ["xpath=/html/body/div[3]/div/div/ul/li/button", "xpath=//li/button", "css=body > div.wallet-adapter-modal.wallet-adapter-modal-fade-in > div.wallet-adapter-modal-container > div > ul > li:nth-child(1) > button"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"root\"]/div/div/div[2]/div/div/div/div[2]/img", "Value": "", "Targets": ["xpath=//*[@id=\"root\"]/div/div/div[2]/div/div/div/div[2]/img", "xpath=//img", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-hPGoDJ.bbTFzS > div.sc-dSfWjt.cTaEIw > div > div:nth-child(1) > div > div.p-1.hover > img"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"root\"]/div/div/div[2]/div[2]/div[3]/font/font", "Value": "", "Targets": ["xpath=//*[@id=\"root\"]/div/div/div[2]/div[2]/div[3]/font/font", "xpath=//div[3]/font/font", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-hPGoDJ.bbTFzS > div.container.flex.justify-between.align-center > div.menu-list > div:nth-child(3) > font > font"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"root\"]/div/div[2]/div/div[2]/div/div[2]/div/div/div/div/div/div", "Value": "", "Targets": ["xpath=//*[@id=\"root\"]/div/div[2]/div/div[2]/div/div[2]/div/div/div/div/div/div", "xpath=//div[2]/div/div[2]/div/div[2]/div/div/div/div/div/div", "css=#root > div.sc-dHMlHb.dOqsx > div.sc-eDHQDy.gyuVAt > div > div.sc-blHHSb.bJAJFK > div:nth-child(1) > div.sc-blHHSb.bJAJFK > div:nth-child(1) > div > div > div > div > div:nth-child(1)"], "Description": ""}, {"Command": "selectWindow", "Target": "tab=0", "Value": "", "Description": ""}, {"Command": "selectWindow", "Target": "tab=1", "Value": "", "Description": ""}, {"Command": "selectWindow", "Target": "tab=1", "Value": "", "Description": ""}, {"Command": "selectWindow", "Target": "tab=0", "Value": "", "Description": ""}, {"Command": "selectWindow", "Target": "tab=1", "Value": "", "Description": ""}]}