#!/usr/bin/env python3
"""
DDAI 完全自动化注册脚本
使用保存的 Turnstile Token 自动完成注册
"""

import json
import time
import random
import string
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def generate_random_credentials():
    """生成随机注册信息"""
    # 生成随机用户名
    username = 'user_' + ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    
    # 生成随机邮箱
    email = username + '@gmail.com'
    
    # 生成随机密码
    password = ''.join(random.choices(string.ascii_letters + string.digits, k=12)) + '!A1'
    
    return email, username, password

def load_token():
    """加载保存的 token"""
    try:
        with open('ddai_manual_token_success.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data['token']
    except Exception as e:
        print(f"❌ 加载 token 失败: {e}")
        return None

def setup_chrome_driver():
    """设置 Chrome 驱动"""
    chrome_options = Options()
    
    # 反检测设置
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 设置用户代理
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    # 禁用图片加载以提高速度
    prefs = {"profile.managed_default_content_settings.images": 2}
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    # 执行反检测脚本
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def auto_register():
    """自动注册函数"""
    print("🎯 DDAI 完全自动化注册")
    print("=" * 60)
    
    # 加载 token
    token = load_token()
    if not token:
        print("❌ 无法加载 Token，请先运行 save_token.py")
        return False
    
    print(f"✅ Token 加载成功: {token[:50]}...")
    
    # 生成随机注册信息
    email, username, password = generate_random_credentials()
    
    print(f"📧 邮箱: {email}")
    print(f"👤 用户名: {username}")
    print(f"🔐 密码: {password}")
    print()
    
    # 设置浏览器
    print("🌐 启动浏览器...")
    driver = setup_chrome_driver()
    
    try:
        print("📍 打开注册页面...")
        driver.get("https://app.ddai.space/register")
        
        # 等待页面加载
        print("⏳ 等待页面加载...")
        time.sleep(5)
        
        print("📝 填写注册信息...")
        
        # 填写邮箱
        try:
            email_field = WebDriverWait(driver, 15).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '[placeholder*="email"], [type="email"], input[name*="email"]'))
            )
            email_field.clear()
            time.sleep(0.5)
            email_field.send_keys(email)
            print(f"✅ 邮箱已填写: {email}")
        except Exception as e:
            print(f"❌ 填写邮箱失败: {e}")
            return False
        
        # 填写用户名
        try:
            username_selectors = [
                '[placeholder*="username"]',
                '[placeholder*="User name"]', 
                'input[name*="username"]',
                'input[name*="user"]'
            ]
            
            username_field = None
            for selector in username_selectors:
                try:
                    username_field = driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except:
                    continue
            
            if username_field:
                username_field.clear()
                time.sleep(0.5)
                username_field.send_keys(username)
                print(f"✅ 用户名已填写: {username}")
            else:
                print("⚠️  未找到用户名字段")
        except Exception as e:
            print(f"❌ 填写用户名失败: {e}")
        
        # 填写密码
        try:
            password_field = driver.find_element(By.CSS_SELECTOR, '[placeholder*="password"], [type="password"]')
            password_field.clear()
            time.sleep(0.5)
            password_field.send_keys(password)
            print(f"✅ 密码已填写: {password}")
        except Exception as e:
            print(f"❌ 填写密码失败: {e}")
            return False
        
        # 等待 Turnstile 加载
        print("⏳ 等待 Turnstile 加载...")
        time.sleep(3)
        
        print("🎯 注入 Turnstile Token...")
        
        # 注入 Token
        inject_script = """
        var tokenField = document.querySelector('[name="cf-turnstile-response"]');
        if (tokenField) {
            tokenField.value = arguments[0];
            console.log('Token 注入成功');
            return true;
        } else {
            console.error('找不到 token 字段');
            return false;
        }
        """
        
        result = driver.execute_script(inject_script, token)
        
        if result:
            print("✅ Token 注入成功!")
        else:
            print("❌ Token 注入失败!")
            # 尝试查找字段
            fields = driver.execute_script("""
                return Array.from(document.querySelectorAll('input')).map(el => ({
                    name: el.name,
                    id: el.id,
                    type: el.type,
                    placeholder: el.placeholder
                }));
            """)
            print("页面中的输入字段:", fields)
            return False
        
        # 等待一下
        time.sleep(2)
        
        print("🚀 提交注册...")
        
        # 查找并点击提交按钮
        submit_selectors = [
            '[type="submit"]',
            '.register-btn',
            'button[class*="register"]',
            'button[class*="submit"]',
            'button:contains("Register")',
            'button:contains("注册")'
        ]
        
        submit_button = None
        for selector in submit_selectors:
            try:
                submit_button = driver.find_element(By.CSS_SELECTOR, selector)
                break
            except:
                continue
        
        if submit_button:
            driver.execute_script("arguments[0].click();", submit_button)
            print("✅ 注册按钮已点击")
        else:
            print("❌ 未找到注册按钮")
            return False
        
        # 等待结果
        print("⏳ 等待注册结果...")
        
        # 等待页面变化
        time.sleep(10)
        
        current_url = driver.current_url
        page_title = driver.title
        
        print(f"📍 当前 URL: {current_url}")
        print(f"📄 页面标题: {page_title}")
        
        # 检查是否成功
        success_indicators = [
            'dashboard' in current_url.lower(),
            'success' in current_url.lower(),
            'welcome' in current_url.lower(),
            'login' in current_url.lower() and 'register' not in current_url.lower()
        ]
        
        if any(success_indicators):
            print("🎉 注册成功!")
            
            # 保存注册信息
            registration_data = {
                'email': email,
                'username': username,
                'password': password,
                'registration_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'final_url': current_url,
                'success': True
            }
            
            with open(f'ddai_registration_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                json.dump(registration_data, f, indent=2, ensure_ascii=False)
            
            print("📁 注册信息已保存")
            return True
        else:
            print("⚠️  注册状态未知")
            
            # 检查是否有错误消息
            try:
                error_elements = driver.find_elements(By.CSS_SELECTOR, '.error, [class*="error"], .alert, [class*="alert"]')
                if error_elements:
                    for elem in error_elements:
                        if elem.text.strip():
                            print(f"❌ 错误信息: {elem.text}")
            except:
                pass
            
            return False
    
    except Exception as e:
        print(f"❌ 注册过程出错: {e}")
        return False
    
    finally:
        print("⏳ 等待 10 秒后关闭浏览器...")
        time.sleep(10)
        driver.quit()

def main():
    """主函数"""
    success = auto_register()
    
    if success:
        print()
        print("=" * 60)
        print("🎉 DDAI 注册完成!")
        print("=" * 60)
    else:
        print()
        print("=" * 60)
        print("❌ 注册失败，请检查日志")
        print("=" * 60)

if __name__ == "__main__":
    main()
