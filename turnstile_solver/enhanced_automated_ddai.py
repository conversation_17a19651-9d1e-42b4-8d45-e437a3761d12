#!/usr/bin/env python3
"""
增强版自动化 DDAI 注册脚本
结合多种 Turnstile 绕过技术
"""

import json
import time
import random
import string
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains

def generate_credentials():
    """生成注册凭据"""
    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
    email = f"{username}@mlanm.online"
    password = "Aa88888888."
    return email, username, password

def setup_stealth_driver():
    """设置隐蔽模式 Chrome 驱动"""
    chrome_options = Options()
    
    # 基本反检测设置
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 设置真实的用户代理
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    # 设置窗口大小
    chrome_options.add_argument('--window-size=1920,1080')
    
    # 禁用一些可能被检测的功能
    chrome_options.add_argument('--disable-plugins-discovery')
    chrome_options.add_argument('--disable-default-apps')
    chrome_options.add_argument('--disable-extensions-file-access-check')
    chrome_options.add_argument('--disable-extensions-http-throttling')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    # 执行反检测脚本
    stealth_script = """
    Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
    Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
    Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']});
    window.chrome = {runtime: {}};
    """
    
    driver.execute_script(stealth_script)
    
    return driver

def wait_for_element_and_fill(driver, selector, value, description="字段", timeout=15):
    """等待元素并填写值"""
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
        )
        
        # 模拟人类行为
        actions = ActionChains(driver)
        actions.move_to_element(element).click().perform()
        
        element.clear()
        time.sleep(random.uniform(0.5, 1.5))
        
        # 逐字符输入，模拟真实打字
        for char in value:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.15))
        
        print(f"✅ {description}已填写: {value}")
        return True
    except Exception as e:
        print(f"❌ 填写{description}失败: {e}")
        return False

def smart_turnstile_handler(driver, max_wait=180):
    """智能 Turnstile 处理器"""
    print("🧠 启动智能 Turnstile 处理器...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        try:
            # 方法1: 检查 Token 是否已生成
            token_field = driver.find_element(By.CSS_SELECTOR, '[name="cf-turnstile-response"]')
            token_value = token_field.get_attribute('value')
            
            if token_value and len(token_value) > 100:
                print(f"✅ Turnstile Token 已自动生成: {token_value[:50]}...")
                return token_value
            
            # 方法2: 查找并处理 Turnstile iframe
            try:
                iframes = driver.find_elements(By.CSS_SELECTOR, 'iframe[src*="turnstile"]')
                for iframe in iframes:
                    print("🔍 发现 Turnstile iframe，尝试处理...")
                    
                    # 滚动到 iframe 位置
                    driver.execute_script("arguments[0].scrollIntoView(true);", iframe)
                    time.sleep(1)
                    
                    # 切换到 iframe
                    driver.switch_to.frame(iframe)
                    
                    try:
                        # 查找复选框
                        checkbox = driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
                        if checkbox.is_displayed() and checkbox.is_enabled():
                            # 模拟人类点击
                            actions = ActionChains(driver)
                            actions.move_to_element(checkbox)
                            actions.pause(random.uniform(0.5, 1.5))
                            actions.click()
                            actions.perform()
                            
                            print("✅ 在 iframe 中点击了 Turnstile 复选框")
                            
                            # 等待验证完成
                            time.sleep(3)
                    except Exception as e:
                        print(f"⚠️  iframe 内操作失败: {e}")
                    finally:
                        driver.switch_to.default_content()
                        
            except Exception as e:
                pass
            
            # 方法3: 查找页面上的复选框
            try:
                checkboxes = driver.find_elements(By.CSS_SELECTOR, 
                    'input[type="checkbox"], '
                    '.cf-turnstile input, '
                    '[id*="turnstile"] input, '
                    '[class*="turnstile"] input'
                )
                
                for checkbox in checkboxes:
                    if checkbox.is_displayed() and checkbox.is_enabled():
                        # 滚动到元素位置
                        driver.execute_script("arguments[0].scrollIntoView(true);", checkbox)
                        time.sleep(0.5)
                        
                        # 模拟人类点击
                        actions = ActionChains(driver)
                        actions.move_to_element(checkbox)
                        actions.pause(random.uniform(0.5, 1.5))
                        actions.click()
                        actions.perform()
                        
                        print("🔄 点击了 Turnstile 复选框")
                        time.sleep(3)
                        break
                        
            except Exception as e:
                pass
            
            # 方法4: JavaScript 注入尝试
            try:
                js_script = """
                // 尝试查找并点击 Turnstile 复选框
                var turnstileElements = document.querySelectorAll('[id*="turnstile"], [class*="turnstile"], iframe[src*="turnstile"]');
                for (var i = 0; i < turnstileElements.length; i++) {
                    var element = turnstileElements[i];
                    if (element.tagName === 'IFRAME') {
                        try {
                            var iframeDoc = element.contentDocument || element.contentWindow.document;
                            var checkbox = iframeDoc.querySelector('input[type="checkbox"]');
                            if (checkbox && checkbox.offsetParent !== null) {
                                checkbox.click();
                                console.log('Clicked Turnstile checkbox via JavaScript');
                                return true;
                            }
                        } catch (e) {
                            console.log('Cannot access iframe:', e);
                        }
                    } else {
                        var checkbox = element.querySelector('input[type="checkbox"]');
                        if (checkbox && checkbox.offsetParent !== null) {
                            checkbox.click();
                            console.log('Clicked Turnstile checkbox via JavaScript');
                            return true;
                        }
                    }
                }
                return false;
                """
                
                result = driver.execute_script(js_script)
                if result:
                    print("✅ JavaScript 成功点击了 Turnstile 复选框")
                    time.sleep(3)
                    
            except Exception as e:
                pass
            
            time.sleep(2)
            
        except Exception as e:
            time.sleep(2)
    
    print("⏰ Turnstile 处理超时")
    return None

def enhanced_automated_register():
    """增强版自动化注册"""
    print("🎯 增强版自动化 DDAI 注册脚本")
    print("🧠 使用多种 Turnstile 绕过技术")
    print("=" * 60)
    
    # 生成凭据
    email, username, password = generate_credentials()
    
    print(f"📧 邮箱: {email}")
    print(f"👤 用户名: {username}")
    print(f"🔐 密码: {password}")
    print()
    
    # 启动隐蔽模式浏览器
    print("🌐 启动隐蔽模式浏览器...")
    driver = setup_stealth_driver()
    
    try:
        # 1. 打开注册页面
        print("📍 打开注册页面...")
        driver.get("https://app.ddai.space/register?ref=Bp48g624")
        
        # 等待页面加载
        print("⏳ 等待页面加载...")
        time.sleep(random.uniform(5, 8))
        
        # 2. 填写表单
        print("📝 开始填写注册表单...")
        
        # 填写邮箱
        if not wait_for_element_and_fill(driver, '[name="email"]', email, "邮箱"):
            return False
        
        # 填写用户名
        if not wait_for_element_and_fill(driver, '[name="username"]', username, "用户名"):
            return False
        
        # 填写密码
        if not wait_for_element_and_fill(driver, '[name="password"]', password, "密码"):
            return False
        
        # 填写确认密码
        if not wait_for_element_and_fill(driver, '[name="confirmPassword"]', password, "确认密码"):
            return False
        
        # 3. 智能处理 Turnstile
        token = smart_turnstile_handler(driver, max_wait=180)
        
        if not token:
            print("⚠️  自动处理失败，需要手动完成...")
            
            print("\n" + "=" * 40)
            print("🔔 请手动完成 Turnstile 验证")
            print("完成后按回车继续")
            print("=" * 40)
            input("✋ 按回车键继续...")
            
            # 再次检查 Token
            try:
                token_field = driver.find_element(By.CSS_SELECTOR, '[name="cf-turnstile-response"]')
                token = token_field.get_attribute('value')
                
                if token and len(token) > 50:
                    print(f"✅ 获取到 Token: {token[:50]}...")
                else:
                    print("⚠️  未获取到 Token，但继续尝试提交...")
            except:
                print("⚠️  无法检查 Token，但继续尝试提交...")
        
        # 4. 提交表单
        print("\n🚀 提交注册表单...")
        try:
            submit_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"]'))
            )
            
            # 滚动到按钮位置
            driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
            time.sleep(1)
            
            # 模拟人类点击
            actions = ActionChains(driver)
            actions.move_to_element(submit_button)
            actions.pause(random.uniform(0.5, 1.5))
            actions.click()
            actions.perform()
            
            print("✅ 注册按钮已点击")
            
        except Exception as e:
            print(f"❌ 点击注册按钮失败: {e}")
            return False
        
        # 5. 等待结果
        print("⏳ 等待注册结果...")
        
        # 监控页面变化
        initial_url = driver.current_url
        start_time = time.time()
        
        while time.time() - start_time < 30:
            current_url = driver.current_url
            
            print(f"📍 当前 URL: {current_url}")
            
            # 检查是否跳转
            if current_url != initial_url:
                if 'login' in current_url:
                    print("🎉 已跳转到登录页面，注册可能成功!")
                    
                    # 保存注册信息
                    save_registration_info(email, username, password, current_url, token)
                    
                    # 尝试登录验证
                    return verify_login(driver, username, password)
                
                elif 'dashboard' in current_url:
                    print("🎉 直接跳转到仪表板，注册成功!")
                    save_registration_info(email, username, password, current_url, token)
                    return True
                
                else:
                    print(f"🔍 跳转到其他页面: {current_url}")
            
            # 检查页面内容
            try:
                page_text = driver.execute_script("return document.body.innerText;").lower()
                
                if any(word in page_text for word in ['success', 'welcome', 'dashboard']):
                    print("🎉 检测到成功关键词!")
                    save_registration_info(email, username, password, current_url, token)
                    return True
                
                elif any(word in page_text for word in ['error', 'failed', 'invalid', 'expired']):
                    print("❌ 检测到错误关键词!")
                    print(f"页面内容片段: {page_text[:200]}...")
                    return False
            except:
                pass
            
            time.sleep(3)
        
        print("⏰ 等待超时")
        return False
    
    except Exception as e:
        print(f"❌ 注册过程出错: {e}")
        return False
    
    finally:
        print("\n⏳ 保持浏览器打开 30 秒以便查看结果...")
        time.sleep(30)
        driver.quit()

def save_registration_info(email, username, password, final_url, token):
    """保存注册信息"""
    registration_data = {
        'email': email,
        'username': username,
        'password': password,
        'token': token,
        'registration_url': 'https://app.ddai.space/register?ref=Bp48g624',
        'final_url': final_url,
        'registration_time': time.strftime('%Y-%m-%d %H:%M:%S'),
        'method': 'enhanced_automated_stealth',
        'success': True
    }
    
    filename = f'enhanced_registration_{int(time.time())}.json'
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(registration_data, f, indent=2, ensure_ascii=False)
    
    print(f"📁 注册信息已保存到: {filename}")

def verify_login(driver, username, password):
    """验证登录"""
    try:
        print("\n🔐 开始登录验证...")
        
        # 如果不在登录页面，导航到登录页面
        if 'login' not in driver.current_url:
            driver.get("https://app.ddai.space/login")
            time.sleep(3)
        
        # 填写用户名
        if not wait_for_element_and_fill(driver, '[name="usernameOrEmail"]', username, "登录用户名"):
            return False
        
        # 填写密码
        if not wait_for_element_and_fill(driver, '[name="password"]', password, "登录密码"):
            return False
        
        # 点击登录按钮
        try:
            login_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
            
            # 模拟人类点击
            actions = ActionChains(driver)
            actions.move_to_element(login_button)
            actions.pause(random.uniform(0.5, 1.5))
            actions.click()
            actions.perform()
            
            print("✅ 登录按钮已点击")
        except Exception as e:
            print(f"❌ 点击登录按钮失败: {e}")
            return False
        
        # 等待登录结果
        time.sleep(8)
        
        current_url = driver.current_url
        print(f"📍 登录后 URL: {current_url}")
        
        if 'dashboard' in current_url or ('login' not in current_url and 'register' not in current_url):
            print("🎉 登录成功！注册验证通过！")
            return True
        else:
            print("❌ 登录失败，可能注册未成功")
            return False
    
    except Exception as e:
        print(f"❌ 登录验证失败: {e}")
        return False

def main():
    """主函数"""
    success = enhanced_automated_register()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 增强版自动化 DDAI 注册成功!")
        print("✅ 账户已创建并验证")
        print("🧠 使用多种技术成功绕过检测!")
    else:
        print("❌ 注册失败")
        print("💡 可能需要进一步优化策略")
    print("=" * 60)

if __name__ == "__main__":
    main()
