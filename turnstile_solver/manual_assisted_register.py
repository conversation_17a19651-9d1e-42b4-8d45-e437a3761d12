#!/usr/bin/env python3
"""
手动辅助注册脚本
用户手动完成 Turnstile 验证，脚本自动填写其他信息并提交
"""

import json
import time
import random
import string
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def generate_random_credentials():
    """生成随机注册信息"""
    username = 'user_' + ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    email = username + '@gmail.com'
    password = ''.join(random.choices(string.ascii_letters + string.digits, k=12)) + '!A1'
    return email, username, password

def setup_chrome_driver():
    """设置 Chrome 驱动 - 显示浏览器窗口"""
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    # 不使用 headless 模式，让用户可以看到浏览器
    
    driver = webdriver.Chrome(options=chrome_options)
    return driver

def manual_assisted_register():
    """手动辅助注册"""
    print("🎯 DDAI 手动辅助注册")
    print("=" * 60)
    
    # 生成随机注册信息
    email, username, password = generate_random_credentials()
    print(f"📧 邮箱: {email}")
    print(f"👤 用户名: {username}")
    print(f"🔐 密码: {password}")
    print()
    
    # 设置浏览器
    print("🌐 启动浏览器...")
    driver = setup_chrome_driver()
    
    try:
        print("📍 打开注册页面...")
        driver.get("https://app.ddai.space/register")
        
        print("⏳ 等待页面加载...")
        time.sleep(5)
        
        print("📝 自动填写注册信息...")
        
        # 填写邮箱
        try:
            email_field = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '[name="email"]'))
            )
            email_field.clear()
            email_field.send_keys(email)
            print(f"✅ 邮箱已填写: {email}")
        except Exception as e:
            print(f"❌ 填写邮箱失败: {e}")
            return False
        
        # 填写用户名
        try:
            username_field = driver.find_element(By.CSS_SELECTOR, '[name="username"]')
            username_field.clear()
            username_field.send_keys(username)
            print(f"✅ 用户名已填写: {username}")
        except Exception as e:
            print(f"❌ 填写用户名失败: {e}")
            return False
        
        # 填写密码
        try:
            password_field = driver.find_element(By.CSS_SELECTOR, '[name="password"]')
            password_field.clear()
            password_field.send_keys(password)
            print(f"✅ 密码已填写: {password}")
        except Exception as e:
            print(f"❌ 填写密码失败: {e}")
            return False
        
        # 填写确认密码
        try:
            confirm_password_field = driver.find_element(By.CSS_SELECTOR, '[name="confirmPassword"]')
            confirm_password_field.clear()
            confirm_password_field.send_keys(password)
            print(f"✅ 确认密码已填写: {password}")
        except Exception as e:
            print(f"❌ 填写确认密码失败: {e}")
            return False
        
        print()
        print("=" * 60)
        print("🔔 重要提示:")
        print("1. 请在浏览器中手动完成 Turnstile 验证（点击复选框）")
        print("2. 看到绿色勾号后，回到这里按回车键继续")
        print("3. 不要点击注册按钮，脚本会自动处理")
        print("=" * 60)
        
        # 等待用户手动完成 Turnstile 验证
        input("✋ 完成 Turnstile 验证后，按回车键继续...")
        
        # 检查 Token 是否已生成
        print("🔍 检查 Turnstile Token...")
        try:
            token_field = driver.find_element(By.CSS_SELECTOR, '[name="cf-turnstile-response"]')
            token_value = token_field.get_attribute('value')
            
            if token_value and len(token_value) > 100:
                print(f"✅ 检测到有效 Token: {token_value[:50]}...")
                print(f"📏 Token 长度: {len(token_value)}")
            else:
                print("❌ 未检测到有效 Token，请确保已完成验证")
                retry = input("是否重试？(y/n): ").lower()
                if retry != 'y':
                    return False
                
                input("请重新完成验证后按回车...")
                token_value = driver.find_element(By.CSS_SELECTOR, '[name="cf-turnstile-response"]').get_attribute('value')
                
                if not token_value or len(token_value) < 100:
                    print("❌ 仍然没有有效 Token")
                    return False
        
        except Exception as e:
            print(f"❌ 检查 Token 失败: {e}")
            return False
        
        print("🚀 自动提交注册...")
        
        # 提交表单
        try:
            submit_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
            driver.execute_script("arguments[0].click();", submit_button)
            print("✅ 注册按钮已点击")
        except Exception as e:
            print(f"⚠️  点击按钮失败，尝试表单提交: {e}")
            try:
                driver.execute_script("document.querySelector('form').submit();")
                print("✅ 表单已提交")
            except Exception as e2:
                print(f"❌ 表单提交也失败: {e2}")
                return False
        
        # 等待结果
        print("⏳ 等待注册结果...")
        
        # 监控 URL 变化和页面内容
        start_time = time.time()
        initial_url = driver.current_url
        
        while time.time() - start_time < 30:
            current_url = driver.current_url
            
            print(f"📍 当前 URL: {current_url}")
            
            # 检查 URL 是否发生有意义的变化
            if current_url != initial_url and 'register' not in current_url:
                print("🎉 URL 已跳转到其他页面!")
                
                # 保存注册信息
                success_data = {
                    'email': email,
                    'username': username,
                    'password': password,
                    'token': token_value,
                    'final_url': current_url,
                    'registration_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'method': 'manual_assisted',
                    'success': True
                }
                
                with open(f'manual_assisted_success_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                    json.dump(success_data, f, indent=2, ensure_ascii=False)
                
                print("📁 注册信息已保存")
                
                print()
                print("=" * 60)
                print("🎉 注册可能成功!")
                print(f"📧 邮箱: {email}")
                print(f"👤 用户名: {username}")
                print(f"🔐 密码: {password}")
                print("=" * 60)
                
                input("按回车键关闭浏览器...")
                return True
            
            # 检查页面内容
            try:
                page_text = driver.execute_script("return document.body.innerText;")
                
                if any(word in page_text.lower() for word in ['success', 'welcome', 'dashboard']):
                    print("🎉 检测到成功关键词!")
                    input("按回车键关闭浏览器...")
                    return True
                elif any(word in page_text.lower() for word in ['error', 'failed', 'invalid']):
                    print("❌ 检测到错误关键词!")
                    print(f"错误信息: {page_text[:200]}...")
                    input("按回车键关闭浏览器...")
                    return False
            except:
                pass
            
            time.sleep(3)
        
        print("⏰ 等待超时")
        input("按回车键关闭浏览器...")
        return False
    
    except Exception as e:
        print(f"❌ 注册过程出错: {e}")
        input("按回车键关闭浏览器...")
        return False
    
    finally:
        driver.quit()

def main():
    """主函数"""
    success = manual_assisted_register()
    
    if success:
        print("🎉 手动辅助注册完成!")
    else:
        print("❌ 注册失败")

if __name__ == "__main__":
    main()
