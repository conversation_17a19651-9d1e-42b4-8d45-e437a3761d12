services:
  solver:
    container_name: solver
    hostname: solver
    build:
      context: ./docker/services/solver
      dockerfile: ./solver.Dockerfile
      platforms:
        - linux/amd64
        - linux/arm64
      args:
        BASE_IMAGE: ${BASE_IMAGE:-ubuntu:latest}
    restart: always
    ports:
      - ${SOLVER_SERVER_PORT:-8088}:${SOLVER_SERVER_PORT:-8088}
      - ${VNC_PORT:-5901}:${VNC_PORT:-5901}
      - ${XRDP_PORT:-3389}:${XRDP_PORT:-3389}
    networks:
      - solver
    environment:
      - DEBIAN_FRONTEND=noninteractive
      - LANG=en_US.UTF-8
      - LANGUAGE=en_US:en
      - LC_ALL=en_US.UTF-8
      - TZ=${TZ:-America/New_York}

      - SOLVER_SERVER_PORT=${SOLVER_SERVER_PORT:-8088}
      - SOLVER_BROWSER=${SOLVER_BROWSER:-chrome}
      - START_SERVER=${START_SERVER:-false}

      - REMOTE_DESKTOP_PROTOCOL=${REMOTE_DESKTOP_PROTOCOL:-VNC}
      - USER=${USER:-Perico} # Needed by VNC Server
      # VNC Server
      - VNC_PORT=${VNC_PORT:-5901}
      - VNC_PASSWORD=${VNC_PASSWORD:-123456789}
      - VNC_GEOMETRY=${VNC_GEOMETRY:-1280x720}
      - VNC_DPI=${VNC_DPI:-70}
      - VNC_DEPTH=${VNC_DEPTH:-24}
      - VNC_DISPLAY=${VNC_DISPLAY:-:1}
      # Xrdp
      - XRDP_PORT=${XRDP_PORT:-3389}
networks:
  solver:
    # name: solver # Explicit name declaration can cause conflicts, let docker manage names unless required
    driver: bridge
