#!/usr/bin/env python3
"""
简单的 DDAI Token 保存工具
"""

import json
import time

def main():
    print("🎯 DDAI Token 保存工具")
    print("=" * 60)
    print("请按以下步骤操作:")
    print("1. 在浏览器中打开 https://app.ddai.space/register")
    print("2. 填写注册信息并完成 Turnstile 验证")
    print("3. 按 F12 → Console，输入:")
    print('   document.querySelector(\'[name="cf-turnstile-response"]\').value')
    print("4. 复制显示的 token 并粘贴到下面")
    print()
    
    # 获取用户输入的 token
    token = input("请粘贴您获取的 token: ").strip()
    
    if not token:
        print("❌ 未输入 token")
        return
    
    if len(token) < 20:
        print("⚠️  Token 似乎太短，请确认是否完整")
        confirm = input("是否仍要保存? (y/n): ").lower()
        if confirm != 'y':
            return
    
    # 保存 token
    timestamp = int(time.time())
    
    token_data = {
        'site_url': 'https://app.ddai.space/register',
        'site_key': '0x4AAAAAABdw7Ezbqw4v6Kr1',
        'token': token,
        'method': 'manual_extraction',
        'timestamp': timestamp,
        'datetime': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp)),
        'success': True
    }
    
    filename = f"ddai_manual_token_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(token_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Token 已保存到: {filename}")
        
        # 创建使用示例文件
        usage_example = f'''# DDAI Turnstile Token 使用示例

# 您的 Token:
TOKEN = "{token}"

# 方法1: 在浏览器控制台中使用
# 打开 https://app.ddai.space/register
# 按 F12 → Console，输入:
# document.querySelector('[name="cf-turnstile-response"]').value = "{token}";

# 方法2: Selenium 自动化
from selenium import webdriver
from selenium.webdriver.common.by import By

driver = webdriver.Chrome()
driver.get("https://app.ddai.space/register")

# 填写注册信息
driver.find_element(By.CSS_SELECTOR, '[type="email"]').send_keys("<EMAIL>")
driver.find_element(By.CSS_SELECTOR, '[placeholder*="username"]').send_keys("your_username")
driver.find_element(By.CSS_SELECTOR, '[type="password"]').send_keys("your_password")

# 注入 token
driver.execute_script("""
    document.querySelector('[name="cf-turnstile-response"]').value = arguments[0];
""", "{token}")

# 提交表单
driver.find_element(By.CSS_SELECTOR, '[type="submit"]').click()

# 方法3: Playwright 自动化
from playwright.sync_api import sync_playwright

with sync_playwright() as p:
    browser = p.chromium.launch(headless=False)
    page = browser.new_page()
    page.goto("https://app.ddai.space/register")
    
    # 填写信息
    page.fill('[type="email"]', "<EMAIL>")
    page.fill('[placeholder*="username"]', "your_username")
    page.fill('[type="password"]', "your_password")
    
    # 注入 token
    page.evaluate("(token) => document.querySelector('[name=\\"cf-turnstile-response\\"]').value = token", "{token}")
    
    # 提交
    page.click('[type="submit"]')
    browser.close()
'''
        
        usage_filename = f"ddai_usage_example_{timestamp}.py"
        with open(usage_filename, 'w', encoding='utf-8') as f:
            f.write(usage_example)
        
        print(f"📝 使用示例已保存到: {usage_filename}")
        
        print()
        print("=" * 60)
        print("🎉 Token 保存成功!")
        print("=" * 60)
        print("📋 快速使用方法:")
        print()
        print("1. 🌐 在浏览器控制台中:")
        print(f'   document.querySelector(\'[name="cf-turnstile-response"]\').value = "{token[:50]}...";')
        print()
        print("2. 🔧 在 Python 脚本中:")
        print(f'   token = "{token[:50]}..."')
        print('   driver.execute_script("document.querySelector(\'[name=\\"cf-turnstile-response\\"]\').value = arguments[0];", token)')
        print()
        print("3. 📁 查看完整示例:")
        print(f"   cat {usage_filename}")
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")

if __name__ == "__main__":
    main()
