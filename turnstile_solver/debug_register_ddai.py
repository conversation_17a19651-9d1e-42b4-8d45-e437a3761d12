#!/usr/bin/env python3
"""
DDAI 调试版自动注册脚本
增加更多调试信息和错误处理
"""

import json
import time
import random
import string
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def generate_random_credentials():
    """生成随机注册信息"""
    username = 'user_' + ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    email = username + '@gmail.com'
    password = ''.join(random.choices(string.ascii_letters + string.digits, k=12)) + '!A1'
    return email, username, password

def load_token():
    """加载保存的 token"""
    try:
        with open('ddai_manual_token_success.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data['token']
    except Exception as e:
        print(f"❌ 加载 token 失败: {e}")
        return None

def setup_chrome_driver():
    """设置 Chrome 驱动"""
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    return driver

def debug_register():
    """调试版注册函数"""
    print("🎯 DDAI 调试版自动注册")
    print("=" * 60)
    
    # 加载 token
    token = load_token()
    if not token:
        print("❌ 无法加载 Token")
        return False
    
    print(f"✅ Token 加载成功: {token[:50]}...")
    print(f"📏 Token 长度: {len(token)}")
    
    # 生成随机注册信息
    email, username, password = generate_random_credentials()
    print(f"📧 邮箱: {email}")
    print(f"👤 用户名: {username}")
    print(f"🔐 密码: {password}")
    print()
    
    # 设置浏览器
    print("🌐 启动浏览器...")
    driver = setup_chrome_driver()
    
    try:
        print("📍 打开注册页面...")
        driver.get("https://app.ddai.space/register")
        
        print("⏳ 等待页面加载...")
        time.sleep(8)
        
        # 调试：检查页面状态
        print(f"📍 当前 URL: {driver.current_url}")
        print(f"📄 页面标题: {driver.title}")
        
        # 检查页面中的所有输入字段
        print("🔍 检查页面输入字段...")
        fields = driver.execute_script("""
            return Array.from(document.querySelectorAll('input')).map(el => ({
                name: el.name,
                id: el.id,
                type: el.type,
                placeholder: el.placeholder,
                className: el.className
            }));
        """)
        
        for i, field in enumerate(fields):
            print(f"  字段 {i+1}: {field}")
        
        print("📝 开始填写注册信息...")
        
        # 填写邮箱
        try:
            email_field = WebDriverWait(driver, 15).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '[type="email"], input[placeholder*="email" i]'))
            )
            email_field.clear()
            time.sleep(1)
            email_field.send_keys(email)
            print(f"✅ 邮箱已填写: {email}")
        except Exception as e:
            print(f"❌ 填写邮箱失败: {e}")
            return False
        
        # 填写用户名
        try:
            username_field = driver.find_element(By.CSS_SELECTOR, 'input[placeholder*="username" i], input[placeholder*="user name" i]')
            username_field.clear()
            time.sleep(1)
            username_field.send_keys(username)
            print(f"✅ 用户名已填写: {username}")
        except Exception as e:
            print(f"❌ 填写用户名失败: {e}")
        
        # 填写密码
        try:
            password_field = driver.find_element(By.CSS_SELECTOR, '[name="password"]')
            password_field.clear()
            time.sleep(1)
            password_field.send_keys(password)
            print(f"✅ 密码已填写: {password}")
        except Exception as e:
            print(f"❌ 填写密码失败: {e}")
            return False

        # 填写确认密码
        try:
            confirm_password_field = driver.find_element(By.CSS_SELECTOR, '[name="confirmPassword"]')
            confirm_password_field.clear()
            time.sleep(1)
            confirm_password_field.send_keys(password)
            print(f"✅ 确认密码已填写: {password}")
        except Exception as e:
            print(f"❌ 填写确认密码失败: {e}")
            return False
        
        # 等待 Turnstile 加载
        print("⏳ 等待 Turnstile 加载...")
        time.sleep(5)
        
        # 检查 Turnstile 相关元素
        print("🔍 检查 Turnstile 元素...")
        turnstile_info = driver.execute_script("""
            var info = {
                turnstile_scripts: Array.from(document.querySelectorAll('script')).filter(s => s.src && s.src.includes('turnstile')).map(s => s.src),
                turnstile_iframes: Array.from(document.querySelectorAll('iframe')).filter(f => f.src && f.src.includes('turnstile')).length,
                cf_response_field: document.querySelector('[name="cf-turnstile-response"]') ? 'found' : 'not found',
                all_hidden_inputs: Array.from(document.querySelectorAll('input[type="hidden"]')).map(el => ({name: el.name, id: el.id, value: el.value ? el.value.substring(0, 50) + '...' : ''}))
            };
            return info;
        """)
        
        print(f"  Turnstile 脚本: {turnstile_info['turnstile_scripts']}")
        print(f"  Turnstile iframe 数量: {turnstile_info['turnstile_iframes']}")
        print(f"  cf-turnstile-response 字段: {turnstile_info['cf_response_field']}")
        print(f"  隐藏字段: {turnstile_info['all_hidden_inputs']}")
        
        print("🎯 注入 Turnstile Token...")
        
        # 注入 Token
        inject_result = driver.execute_script("""
            var tokenField = document.querySelector('[name="cf-turnstile-response"]');
            if (tokenField) {
                var oldValue = tokenField.value;
                tokenField.value = arguments[0];
                return {
                    success: true,
                    oldValue: oldValue,
                    newValue: tokenField.value.substring(0, 50) + '...',
                    fieldFound: true
                };
            } else {
                return {
                    success: false,
                    fieldFound: false,
                    allHiddenFields: Array.from(document.querySelectorAll('input[type="hidden"]')).map(el => el.name)
                };
            }
        """, token)
        
        print(f"🔍 注入结果: {inject_result}")
        
        if inject_result['success']:
            print("✅ Token 注入成功!")
        else:
            print("❌ Token 注入失败!")
            return False
        
        # 等待一下
        time.sleep(3)
        
        print("🚀 提交注册...")
        
        # 查找提交按钮
        submit_buttons = driver.execute_script("""
            var buttons = Array.from(document.querySelectorAll('button, input[type="submit"]'));
            return buttons.map(btn => ({
                text: btn.textContent || btn.value,
                type: btn.type,
                className: btn.className,
                id: btn.id
            }));
        """)
        
        print(f"🔍 找到的按钮: {submit_buttons}")
        
        # 点击提交按钮
        try:
            submit_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"], input[type="submit"], button:contains("Register")')
            driver.execute_script("arguments[0].click();", submit_button)
            print("✅ 注册按钮已点击")
        except Exception as e:
            print(f"❌ 点击注册按钮失败: {e}")
            # 尝试其他方式
            try:
                driver.execute_script("document.querySelector('form').submit();")
                print("✅ 通过表单提交")
            except:
                print("❌ 表单提交也失败")
                return False
        
        # 等待结果 - 增加等待时间
        print("⏳ 等待注册结果（30秒）...")
        
        start_time = time.time()
        while time.time() - start_time < 30:
            current_url = driver.current_url
            page_title = driver.title
            
            print(f"📍 当前 URL: {current_url}")
            print(f"📄 页面标题: {page_title}")
            
            # 检查页面内容变化
            page_text = driver.execute_script("return document.body.innerText;")
            
            # 检查成功指标
            success_indicators = [
                'dashboard' in current_url.lower(),
                'success' in current_url.lower(),
                'welcome' in page_text.lower(),
                'congratulations' in page_text.lower(),
                current_url != 'https://app.ddai.space/register'
            ]
            
            # 检查错误指标
            error_indicators = [
                'error' in page_text.lower(),
                'invalid' in page_text.lower(),
                'failed' in page_text.lower(),
                'expired' in page_text.lower()
            ]
            
            if any(success_indicators):
                print("🎉 检测到成功指标!")
                break
            elif any(error_indicators):
                print("❌ 检测到错误指标!")
                print(f"页面内容片段: {page_text[:500]}...")
                break
            
            time.sleep(2)
        
        # 最终状态检查
        final_url = driver.current_url
        final_title = driver.title
        
        print(f"📍 最终 URL: {final_url}")
        print(f"📄 最终标题: {final_title}")
        
        # 保存页面源码用于调试
        with open(f'debug_page_source_{int(time.time())}.html', 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        
        print("📁 页面源码已保存用于调试")
        
        if final_url != 'https://app.ddai.space/register':
            print("🎉 URL 已改变，可能注册成功!")
            return True
        else:
            print("⚠️  仍在注册页面，状态未知")
            return False
    
    except Exception as e:
        print(f"❌ 注册过程出错: {e}")
        return False
    
    finally:
        print("⏳ 等待 15 秒后关闭浏览器...")
        time.sleep(15)
        driver.quit()

def main():
    """主函数"""
    success = debug_register()
    
    if success:
        print()
        print("=" * 60)
        print("🎉 DDAI 注册可能成功!")
        print("=" * 60)
    else:
        print()
        print("=" * 60)
        print("❌ 注册失败或状态未知")
        print("=" * 60)

if __name__ == "__main__":
    main()
