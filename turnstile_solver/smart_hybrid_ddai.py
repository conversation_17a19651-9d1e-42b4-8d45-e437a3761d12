#!/usr/bin/env python3
"""
智能混合 DDAI 注册脚本
最大化自动化，最小化手动操作
"""

import json
import time
import random
import string
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains

def generate_credentials():
    """生成注册凭据"""
    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
    email = f"{username}@mlanm.online"
    password = "Aa88888888."
    return email, username, password

def setup_smart_driver():
    """设置智能反检测 Chrome 驱动"""
    chrome_options = Options()
    
    # 基本设置
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 设置真实用户代理
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    chrome_options.add_argument('--window-size=1920,1080')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    # 反检测脚本
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def smart_turnstile_handler(driver):
    """智能 Turnstile 处理器 - 尝试自动化，失败则提示手动"""
    print("🧠 启动智能 Turnstile 处理器...")
    
    # 第一阶段：尝试自动化处理
    print("🔄 第一阶段：尝试自动化处理...")
    
    auto_success = False
    max_auto_attempts = 30  # 30秒自动尝试
    
    for attempt in range(max_auto_attempts):
        try:
            # 检查 Token 是否已生成
            token_field = driver.find_element(By.CSS_SELECTOR, '[name="cf-turnstile-response"]')
            token_value = token_field.get_attribute('value')
            
            if token_value and len(token_value) > 100:
                print(f"✅ 自动化成功！Token 已生成: {token_value[:50]}...")
                auto_success = True
                return token_value
            
            # 尝试各种自动点击策略
            try:
                # 策略1: 查找并点击复选框
                checkboxes = driver.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                for checkbox in checkboxes:
                    if checkbox.is_displayed() and checkbox.is_enabled():
                        # 滚动到元素
                        driver.execute_script("arguments[0].scrollIntoView(true);", checkbox)
                        time.sleep(0.5)
                        
                        # 模拟人类点击
                        actions = ActionChains(driver)
                        actions.move_to_element(checkbox)
                        actions.pause(random.uniform(0.3, 0.8))
                        actions.click()
                        actions.perform()
                        
                        print(f"🔄 自动点击复选框 (尝试 {attempt + 1})")
                        time.sleep(2)
                        break
                
                # 策略2: iframe 处理
                iframes = driver.find_elements(By.CSS_SELECTOR, 'iframe[src*="turnstile"]')
                for iframe in iframes:
                    try:
                        driver.switch_to.frame(iframe)
                        checkbox = driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
                        if checkbox.is_displayed():
                            actions = ActionChains(driver)
                            actions.move_to_element(checkbox)
                            actions.pause(random.uniform(0.5, 1.0))
                            actions.click()
                            actions.perform()
                            print(f"🔄 iframe 内自动点击 (尝试 {attempt + 1})")
                        driver.switch_to.default_content()
                        time.sleep(2)
                        break
                    except:
                        driver.switch_to.default_content()
                        continue
                
                # 策略3: JavaScript 注入
                js_click = """
                var elements = document.querySelectorAll('input[type="checkbox"], .cf-turnstile, [id*="turnstile"]');
                for (var i = 0; i < elements.length; i++) {
                    var element = elements[i];
                    if (element.offsetParent !== null) {
                        element.click();
                        console.log('JS clicked element:', element);
                        return true;
                    }
                }
                return false;
                """
                
                js_result = driver.execute_script(js_click)
                if js_result:
                    print(f"🔄 JavaScript 自动点击 (尝试 {attempt + 1})")
                    time.sleep(2)
                
            except Exception as e:
                pass
            
            time.sleep(1)
            
        except Exception as e:
            time.sleep(1)
    
    # 第二阶段：如果自动化失败，提示手动操作
    if not auto_success:
        print("\n" + "=" * 50)
        print("🔔 自动化处理未成功，需要手动协助")
        print("请在浏览器中:")
        print("1. 找到 Turnstile 验证框（通常是一个复选框）")
        print("2. 点击复选框完成验证")
        print("3. 看到绿色勾号后，回到终端按回车继续")
        print("=" * 50)
        
        input("✋ 完成验证后按回车键继续...")
        
        # 验证手动操作结果
        try:
            token_field = driver.find_element(By.CSS_SELECTOR, '[name="cf-turnstile-response"]')
            token_value = token_field.get_attribute('value')
            
            if token_value and len(token_value) > 50:
                print(f"✅ 手动验证成功！Token: {token_value[:50]}...")
                return token_value
            else:
                print("⚠️  未检测到有效 Token，但继续尝试...")
                return None
        except:
            print("⚠️  无法检查 Token，但继续尝试...")
            return None

def smart_hybrid_register():
    """智能混合注册"""
    print("🎯 智能混合 DDAI 注册脚本")
    print("🧠 最大化自动化，最小化手动操作")
    print("=" * 60)
    
    # 生成凭据
    email, username, password = generate_credentials()
    
    print(f"📧 邮箱: {email}")
    print(f"👤 用户名: {username}")
    print(f"🔐 密码: {password}")
    print()
    
    # 启动智能浏览器
    print("🌐 启动智能反检测浏览器...")
    driver = setup_smart_driver()
    
    try:
        # 1. 打开注册页面
        print("📍 打开注册页面...")
        driver.get("https://app.ddai.space/register?ref=Bp48g624")
        
        # 等待页面加载
        print("⏳ 等待页面加载...")
        time.sleep(5)
        
        # 2. 填写表单
        print("📝 自动填写注册表单...")
        
        # 等待表单元素
        WebDriverWait(driver, 30).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, '[name="email"]'))
        )
        
        # 填写邮箱
        email_field = driver.find_element(By.CSS_SELECTOR, '[name="email"]')
        email_field.clear()
        time.sleep(0.5)
        email_field.send_keys(email)
        print(f"✅ 邮箱已填写: {email}")
        
        # 填写用户名
        username_field = driver.find_element(By.CSS_SELECTOR, '[name="username"]')
        username_field.clear()
        time.sleep(0.5)
        username_field.send_keys(username)
        print(f"✅ 用户名已填写: {username}")
        
        # 填写密码
        password_field = driver.find_element(By.CSS_SELECTOR, '[name="password"]')
        password_field.clear()
        time.sleep(0.5)
        password_field.send_keys(password)
        print(f"✅ 密码已填写: {password}")
        
        # 填写确认密码
        confirm_password_field = driver.find_element(By.CSS_SELECTOR, '[name="confirmPassword"]')
        confirm_password_field.clear()
        time.sleep(0.5)
        confirm_password_field.send_keys(password)
        print(f"✅ 确认密码已填写: {password}")
        
        # 3. 智能处理 Turnstile
        token = smart_turnstile_handler(driver)
        
        # 4. 提交表单
        print("\n🚀 自动提交注册表单...")
        
        submit_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"]'))
        )
        
        # 滚动到按钮位置
        driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
        time.sleep(1)
        
        # 模拟人类点击
        actions = ActionChains(driver)
        actions.move_to_element(submit_button)
        actions.pause(random.uniform(0.5, 1.0))
        actions.click()
        actions.perform()
        
        print("✅ 注册按钮已点击")
        
        # 5. 智能等待结果
        print("⏳ 智能监控注册结果...")
        
        initial_url = driver.current_url
        start_time = time.time()
        
        while time.time() - start_time < 30:
            current_url = driver.current_url
            print(f"📍 当前 URL: {current_url}")
            
            # 检查是否跳转
            if current_url != initial_url:
                if 'login' in current_url:
                    print("🎉 已跳转到登录页面，注册可能成功!")
                    
                    # 保存注册信息
                    save_registration_info(email, username, password, current_url, token)
                    
                    # 自动登录验证
                    return auto_verify_login(driver, username, password)
                
                elif 'dashboard' in current_url:
                    print("🎉 直接跳转到仪表板，注册成功!")
                    save_registration_info(email, username, password, current_url, token)
                    return True
                
                else:
                    print(f"🔍 跳转到其他页面: {current_url}")
            
            # 智能检查页面内容
            try:
                page_text = driver.execute_script("return document.body.innerText;").lower()
                
                # 检查失败关键词
                failure_keywords = ['captcha verification failed', 'turnstile failed', 'verification failed', 'error', 'failed']
                if any(word in page_text for word in failure_keywords):
                    print("❌ 检测到验证失败关键词!")
                    print(f"页面内容片段: {page_text[:300]}...")
                    return False
                
                # 检查成功关键词
                success_keywords = ['success', 'welcome', 'dashboard', 'congratulations']
                if any(word in page_text for word in success_keywords) and not any(word in page_text for word in failure_keywords):
                    print("🎉 检测到成功关键词!")
                    save_registration_info(email, username, password, current_url, token)
                    return True
                
            except:
                pass
            
            time.sleep(3)
        
        print("⏰ 等待超时")
        return False
    
    except Exception as e:
        print(f"❌ 注册过程出错: {e}")
        return False
    
    finally:
        print("\n⏳ 保持浏览器打开 30 秒以便查看结果...")
        time.sleep(30)
        driver.quit()

def save_registration_info(email, username, password, final_url, token):
    """保存注册信息"""
    registration_data = {
        'email': email,
        'username': username,
        'password': password,
        'token': token,
        'registration_url': 'https://app.ddai.space/register?ref=Bp48g624',
        'final_url': final_url,
        'registration_time': time.strftime('%Y-%m-%d %H:%M:%S'),
        'method': 'smart_hybrid_automated',
        'success': True
    }
    
    filename = f'smart_hybrid_registration_{int(time.time())}.json'
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(registration_data, f, indent=2, ensure_ascii=False)
    
    print(f"📁 注册信息已保存到: {filename}")

def auto_verify_login(driver, username, password):
    """自动登录验证"""
    try:
        print("\n🔐 自动登录验证...")
        
        # 如果不在登录页面，导航到登录页面
        if 'login' not in driver.current_url:
            driver.get("https://app.ddai.space/login")
            time.sleep(3)
        
        # 填写用户名
        username_field = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, '[name="usernameOrEmail"]'))
        )
        username_field.clear()
        username_field.send_keys(username)
        print(f"✅ 登录用户名已填写: {username}")
        
        # 填写密码
        password_field = driver.find_element(By.CSS_SELECTOR, '[name="password"]')
        password_field.clear()
        password_field.send_keys(password)
        print(f"✅ 登录密码已填写")
        
        # 点击登录按钮
        login_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
        
        # 模拟人类点击
        actions = ActionChains(driver)
        actions.move_to_element(login_button)
        actions.pause(random.uniform(0.5, 1.0))
        actions.click()
        actions.perform()
        
        print("✅ 登录按钮已点击")
        
        # 等待登录结果
        time.sleep(8)
        
        current_url = driver.current_url
        print(f"📍 登录后 URL: {current_url}")
        
        if 'dashboard' in current_url or ('login' not in current_url and 'register' not in current_url):
            print("🎉 登录成功！注册验证通过！")
            return True
        else:
            print("❌ 登录失败，可能注册未成功")
            return False
    
    except Exception as e:
        print(f"❌ 登录验证失败: {e}")
        return False

def main():
    """主函数"""
    success = smart_hybrid_register()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 智能混合 DDAI 注册成功!")
        print("✅ 账户已创建并验证")
        print("🧠 最大化自动化，最小化手动操作!")
    else:
        print("❌ 注册失败")
        print("💡 请确保正确完成了 Turnstile 验证")
    print("=" * 60)

if __name__ == "__main__":
    main()
