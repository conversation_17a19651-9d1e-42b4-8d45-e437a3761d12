#!/usr/bin/env python3
"""
DDAI 网站 Turnstile 最终解决方案
请先手动查找 site_key，然后使用此脚本
"""

import requests
import json
import time

def solve_ddai_turnstile(site_key):
    """
    解决 DDAI 网站的 Turnstile 验证
    
    Args:
        site_key (str): 从网站页面中找到的 data-sitekey 值
    
    Returns:
        dict: 包含 token 的结果
    """
    print("🎯 DDAI Turnstile 解决器")
    print("=" * 50)
    print(f"🌐 网站: https://app.ddai.space/register")
    print(f"🔑 Site Key: {site_key}")
    print("⏳ 正在解决验证...")
    
    try:
        start_time = time.time()
        
        response = requests.get(
            url="http://127.0.0.1:8088/solve",
            headers={
                'ngrok-skip-browser-warning': '_',
                'secret': 'jWRN7DH6',
                'Content-Type': 'application/json'
            },
            json={
                "site_url": "https://app.ddai.space/register",
                "site_key": site_key
            },
            timeout=120
        )
        
        total_time = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('status') == 'OK':
                print(f"✅ 解决成功!")
                print(f"⏱️  总耗时: {total_time:.2f} 秒")
                print(f"🔧 服务器耗时: {data.get('elapsed', 'N/A')} 秒")
                print(f"🎫 Token: {data.get('token', '')}")
                print(f"📝 状态: {data.get('status')}")
                
                # 保存结果
                timestamp = int(time.time())
                result_data = {
                    'site_url': 'https://app.ddai.space/register',
                    'site_key': site_key,
                    'token': data.get('token'),
                    'elapsed_total': total_time,
                    'elapsed_server': data.get('elapsed'),
                    'status': data.get('status'),
                    'message': data.get('message'),
                    'timestamp': timestamp,
                    'success': True
                }
                
                filename = f"ddai_turnstile_success_{timestamp}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(result_data, f, indent=2, ensure_ascii=False)
                
                print(f"📁 结果已保存到: {filename}")
                
                # 显示使用说明
                print("\n" + "=" * 50)
                print("🎉 验证解决成功! 使用方法:")
                print("=" * 50)
                print("1. 📋 复制上面的 Token")
                print("2. 🌐 在 DDAI 注册页面中:")
                print("   - 填写注册信息")
                print("   - 在验证框中使用这个 Token")
                print("   - 提交注册表单")
                print()
                print("3. 🔧 在代码中使用:")
                print(f'   token = "{data.get("token", "")}"')
                print('   # 将 token 注入到页面的隐藏字段中')
                print('   # 通常字段名为: cf-turnstile-response')
                
                return result_data
            else:
                print(f"❌ 解决失败!")
                print(f"📝 状态: {data.get('status', 'UNKNOWN')}")
                print(f"💬 消息: {data.get('message', '无错误信息')}")
                return None
        else:
            print(f"❌ HTTP 错误: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到 Turnstile Solver 服务器")
        print("请确保服务器正在运行:")
        print("./start_solver.sh")
        return None
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        print("网站响应可能较慢，请稍后重试")
        return None
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return None

def check_server():
    """检查服务器状态"""
    try:
        response = requests.get("http://127.0.0.1:8088/", timeout=5)
        return response.status_code in [200, 403, 404]
    except:
        return False

def main():
    """主函数"""
    print("🎯 DDAI Turnstile 解决器")
    print("=" * 50)
    
    # 检查服务器状态
    if not check_server():
        print("❌ Turnstile Solver 服务器未运行")
        print("请先启动服务器:")
        print("cd /Users/<USER>/Desktop/自动化验证/turnstile_solver")
        print("./start_solver.sh")
        return
    
    print("✅ 服务器正在运行")
    print()
    
    # 显示查找 site_key 的说明
    print("📋 如何查找 site_key:")
    print("1. 打开 https://app.ddai.space/register")
    print("2. 按 F12 打开开发者工具")
    print("3. 在 Elements 中搜索 'data-sitekey'")
    print("4. 复制找到的 site_key 值")
    print()
    
    # 获取用户输入的 site_key
    site_key = input("请输入找到的 site_key: ").strip()
    
    if not site_key:
        print("❌ 未输入 site_key")
        return
    
    if not site_key.startswith('0x4'):
        print("⚠️  site_key 格式可能不正确")
        print("正确格式通常为: 0x4AAAAAAAxxxxxxxxxxxxx")
        confirm = input("是否继续? (y/n): ").lower()
        if confirm != 'y':
            return
    
    print()
    
    # 解决 Turnstile
    result = solve_ddai_turnstile(site_key)
    
    if result:
        print("\n🎉 任务完成!")
        print("您现在可以使用获得的 Token 完成 DDAI 注册了!")
    else:
        print("\n❌ 解决失败")
        print("💡 建议:")
        print("1. 确认 site_key 是否正确")
        print("2. 检查网站是否可正常访问")
        print("3. 稍后重试")

if __name__ == "__main__":
    main()
