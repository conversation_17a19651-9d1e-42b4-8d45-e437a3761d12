// DDAI Turnstile Token 注入脚本
// 使用方法：
// 1. 在 https://app.ddai.space/register 页面按 F12 打开控制台
// 2. 粘贴此脚本并回车
// 3. 填写注册信息后点击注册按钮

console.log('🎯 DDAI Turnstile Token 注入脚本');
console.log('=' * 50);

// 你的 Token
const DDAI_TOKEN = "0.wAX838PyXmQAjMn-P0c5SEIaHK0xGsZ9U8sAZwS2lXcgfaoGKwNZdxiv7Up84bZHEAvQNGDdrpAzH74YMm70GKqW8ULSp2R0AGHGx06FuRqGVRvHmulziJiyxR_A4aDWPXuhod2aBvEFh62f9U1mdx5sTI0PzTDzPvpTRCLXuOvJWoEVyeSVO_FTQ5JKi9AblrdF6kNXDTqY_Ro9AraF60XWaesehTVVSe11NHAICtEr_v8U8RarfnB1vofQLvpys8NQK4sLOoiVRR0yyJrxbHaaA0ssDaqEMFsn3w9vC3KpOgc-JqW-4ITlDpG__7-Dm5Z_q9o947LdqLqndgUz6gPIutGRdLXW708z-teeJyeywvqOGsin-poiCO98rg4n4Nc1e74UlJlOq-YwgEYfEVSiJlpNWt7iOWB5uwLLJQwf1Wo6x1baAwN3VjtnYyTOGV2tu4SRE0AdWpqoNEb-GB17gLgremDCsS5QgaEEQw2iRkiOk_It1ntFP-NfBdaioJdkZQ9oVoqDKw7Z0tHCYvfKFbFGJWFfExogOSJTvxE4mPLOta6P2Pglvbiy2Nogz2kShzAPI5W4BdnK3txGYxPWWe3EVszvIj_uMithEGEHEQTgVLKHtJERsSTfx5g9cJYJb80RRf6SrNUtVirYNPI7fYdUiFy7y6DhC4KIbHcmsgUFextvN-pfeW1ibUgcbS0B2Xd_Y-xSgaVTDu6v6HDDGH4uTPW623tbvsrAfZ5MXjuraG3LJRDu3zHH7ONs38Ua1BYEnX-ZZGi5LKhDHUzft2xa0zbLIckN4sBjTHFDzzn7w-UW68HneBu25KzhVHfb6eHCdeqDM0jmJhJd0v4S3szLUWuv67DmIHJFk2HHuJgEOCBzejtHVRFovCwRchcTyNyj0Xuo_r_50p_XUYNzDbYl3K4ifeG-YUHC1HA.awVdEAOv_GNw1M6mwRG_tA.ca87bce70267cc5ae922e73cd30f2f715b4c0032c3428e4c01be6474281ffdff";

// 注入 Token 函数
function injectToken() {
    const tokenField = document.querySelector('[name="cf-turnstile-response"]');
    
    if (tokenField) {
        tokenField.value = DDAI_TOKEN;
        console.log('✅ Token 注入成功!');
        console.log('Token 长度:', DDAI_TOKEN.length);
        console.log('Token 预览:', DDAI_TOKEN.substring(0, 50) + '...');
        return true;
    } else {
        console.error('❌ 找不到 cf-turnstile-response 字段');
        console.log('🔍 尝试查找其他可能的字段...');
        
        // 查找其他可能的字段
        const possibleFields = document.querySelectorAll('[name*="turnstile"], [id*="turnstile"], [class*="turnstile"]');
        console.log('找到的相关字段:', possibleFields);
        
        return false;
    }
}

// 验证 Token 是否注入成功
function verifyToken() {
    const tokenField = document.querySelector('[name="cf-turnstile-response"]');
    if (tokenField && tokenField.value) {
        console.log('✅ Token 验证成功');
        console.log('当前 Token 长度:', tokenField.value.length);
        return true;
    } else {
        console.log('❌ Token 验证失败');
        return false;
    }
}

// 自动填写表单（可选）
function autoFillForm(email, username, password) {
    console.log('📝 自动填写表单...');
    
    try {
        // 填写邮箱
        const emailField = document.querySelector('[placeholder*="email"], [type="email"]');
        if (emailField && email) {
            emailField.value = email;
            emailField.dispatchEvent(new Event('input', { bubbles: true }));
        }
        
        // 填写用户名
        const usernameField = document.querySelector('[placeholder*="username"], [placeholder*="User name"]');
        if (usernameField && username) {
            usernameField.value = username;
            usernameField.dispatchEvent(new Event('input', { bubbles: true }));
        }
        
        // 填写密码
        const passwordField = document.querySelector('[placeholder*="password"], [type="password"]');
        if (passwordField && password) {
            passwordField.value = password;
            passwordField.dispatchEvent(new Event('input', { bubbles: true }));
        }
        
        console.log('✅ 表单填写完成');
    } catch (error) {
        console.error('❌ 表单填写失败:', error);
    }
}

// 主执行函数
function main() {
    console.log('🚀 开始执行...');
    
    // 等待页面加载完成
    if (document.readyState !== 'complete') {
        console.log('⏳ 等待页面加载完成...');
        window.addEventListener('load', () => {
            setTimeout(main, 1000);
        });
        return;
    }
    
    // 注入 Token
    const success = injectToken();
    
    if (success) {
        // 验证注入
        setTimeout(() => {
            verifyToken();
        }, 500);
        
        console.log('');
        console.log('=' * 50);
        console.log('🎉 Token 注入完成!');
        console.log('📋 接下来的步骤:');
        console.log('1. 填写邮箱、用户名、密码');
        console.log('2. 点击注册按钮');
        console.log('3. 等待注册结果');
        console.log('=' * 50);
        
        // 可选：自动填写表单
        // autoFillForm('<EMAIL>', 'your_username', 'your_password');
        
    } else {
        console.log('');
        console.log('=' * 50);
        console.log('❌ Token 注入失败');
        console.log('请检查页面是否正确加载');
        console.log('=' * 50);
    }
}

// 执行主函数
main();
