#!/usr/bin/env python3
"""
测试登录脚本
验证注册的账户是否可以成功登录
"""

import json
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def setup_chrome_driver():
    """设置 Chrome 驱动"""
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--window-size=1920,1080')
    
    driver = webdriver.Chrome(options=chrome_options)
    return driver

def test_login():
    """测试登录"""
    print("🔐 DDAI 登录测试")
    print("=" * 60)
    
    # 读取注册信息
    try:
        with open('cloudscraper_registration_1753003782.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        email = data['email']
        username = data['username']
        password = data['password']
        
        print(f"📧 邮箱: {email}")
        print(f"👤 用户名: {username}")
        print(f"🔐 密码: {password}")
        print()
        
    except Exception as e:
        print(f"❌ 读取注册信息失败: {e}")
        return False
    
    # 启动浏览器
    print("🌐 启动浏览器...")
    driver = setup_chrome_driver()
    
    try:
        # 打开登录页面
        print("📍 打开登录页面...")
        driver.get("https://app.ddai.space/login")
        
        # 等待页面加载
        time.sleep(5)
        
        # 填写用户名
        print("📝 填写登录信息...")
        try:
            username_field = WebDriverWait(driver, 15).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '[name="usernameOrEmail"]'))
            )
            username_field.clear()
            username_field.send_keys(username)
            print(f"✅ 用户名已填写: {username}")
        except Exception as e:
            print(f"❌ 填写用户名失败: {e}")
            return False
        
        # 填写密码
        try:
            password_field = driver.find_element(By.CSS_SELECTOR, '[name="password"]')
            password_field.clear()
            password_field.send_keys(password)
            print(f"✅ 密码已填写")
        except Exception as e:
            print(f"❌ 填写密码失败: {e}")
            return False
        
        # 点击登录按钮
        print("🚀 点击登录按钮...")
        try:
            login_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
            driver.execute_script("arguments[0].click();", login_button)
            print("✅ 登录按钮已点击")
        except Exception as e:
            print(f"❌ 点击登录按钮失败: {e}")
            return False
        
        # 等待登录结果
        print("⏳ 等待登录结果...")
        time.sleep(10)
        
        current_url = driver.current_url
        page_title = driver.title
        
        print(f"📍 当前 URL: {current_url}")
        print(f"📄 页面标题: {page_title}")
        
        # 检查登录结果
        if 'dashboard' in current_url:
            print("🎉 登录成功！已跳转到仪表板")
            return True
        elif 'login' not in current_url and 'register' not in current_url:
            print("🎉 登录成功！已跳转到其他页面")
            return True
        else:
            # 检查页面内容
            try:
                page_text = driver.execute_script("return document.body.innerText;").lower()
                
                if 'user not found' in page_text:
                    print("❌ 登录失败：用户未找到")
                    return False
                elif 'invalid' in page_text or 'incorrect' in page_text:
                    print("❌ 登录失败：用户名或密码错误")
                    return False
                else:
                    print("⚠️  登录状态未知")
                    print(f"页面内容片段: {page_text[:200]}...")
                    return False
            except:
                print("⚠️  无法检查页面内容")
                return False
    
    except Exception as e:
        print(f"❌ 登录测试出错: {e}")
        return False
    
    finally:
        print("⏳ 保持浏览器打开 20 秒以便查看结果...")
        time.sleep(20)
        driver.quit()

def main():
    """主函数"""
    success = test_login()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 登录测试成功!")
        print("✅ 注册的账户可以正常登录")
        print("🎯 Turnstile 绕过方案验证成功!")
    else:
        print("❌ 登录测试失败")
        print("💡 可能注册未真正成功")
    print("=" * 60)

if __name__ == "__main__":
    main()
