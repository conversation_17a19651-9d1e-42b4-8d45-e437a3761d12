#!/usr/bin/env python3
"""
最终版 DDAI 注册脚本
基于原始 JSON 脚本，简化并优化
"""

import json
import time
import random
import string
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def setup_chrome_driver(headless=False):
    """设置 Chrome 驱动"""
    chrome_options = Options()
    
    if headless:
        chrome_options.add_argument('--headless')
    
    # 基本设置
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 设置窗口大小
    chrome_options.add_argument('--window-size=1920,1080')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    # 反检测
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def generate_credentials():
    """生成注册凭据"""
    # 生成随机用户名
    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
    
    # 使用临时邮箱域名
    email = f"{username}@mlanm.online"
    
    # 使用原脚本中的密码格式
    password = "Aa88888888."
    
    return email, username, password

def wait_for_element_and_fill(driver, selector, value, description="字段"):
    """等待元素并填写值"""
    try:
        element = WebDriverWait(driver, 15).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
        )
        element.clear()
        time.sleep(0.5)
        element.send_keys(value)
        print(f"✅ {description}已填写: {value}")
        return True
    except Exception as e:
        print(f"❌ 填写{description}失败: {e}")
        return False

def final_register():
    """最终注册流程"""
    print("🎯 DDAI 最终版注册脚本")
    print("=" * 60)
    
    # 生成凭据
    email, username, password = generate_credentials()
    
    print(f"📧 邮箱: {email}")
    print(f"👤 用户名: {username}")
    print(f"🔐 密码: {password}")
    print()
    
    # 启动浏览器（非无头模式，方便手动处理 Turnstile）
    print("🌐 启动浏览器...")
    driver = setup_chrome_driver(headless=False)
    
    try:
        # 1. 打开注册页面
        print("📍 打开注册页面...")
        driver.get("https://app.ddai.space/register?ref=Bp48g624")
        
        # 等待页面加载
        print("⏳ 等待页面加载...")
        time.sleep(5)
        
        # 2. 检查并处理 Turnstile
        print("🔍 检查 Turnstile 状态...")
        
        # 等待 Turnstile 加载
        time.sleep(3)
        
        # 检查是否有 Turnstile
        turnstile_found = False
        try:
            turnstile_elements = driver.find_elements(By.CSS_SELECTOR, 
                '[id*="turnstile"], [class*="turnstile"], iframe[src*="turnstile"], [id*="cf-chl"]')
            if turnstile_elements:
                turnstile_found = True
                print("🔍 检测到 Turnstile 验证")
        except:
            pass
        
        if turnstile_found:
            print()
            print("=" * 60)
            print("🔔 需要手动完成 Turnstile 验证")
            print("1. 请在浏览器中找到 Turnstile 验证框")
            print("2. 点击复选框完成验证")
            print("3. 看到绿色勾号后，回到终端按回车继续")
            print("=" * 60)
            input("✋ 完成验证后按回车键继续...")
            
            # 验证 Token 是否生成
            try:
                token_field = driver.find_element(By.CSS_SELECTOR, '[name="cf-turnstile-response"]')
                token_value = token_field.get_attribute('value')
                
                if token_value and len(token_value) > 50:
                    print(f"✅ Turnstile Token 已生成: {token_value[:50]}...")
                else:
                    print("⚠️  未检测到 Token，但继续尝试...")
            except:
                print("⚠️  无法检查 Token 状态，但继续尝试...")
        else:
            print("✅ 未检测到 Turnstile，直接继续")
        
        # 3. 填写表单
        print("\n📝 开始填写注册表单...")
        
        # 填写邮箱
        if not wait_for_element_and_fill(driver, '[name="email"]', email, "邮箱"):
            return False
        
        # 填写用户名
        if not wait_for_element_and_fill(driver, '[name="username"]', username, "用户名"):
            return False
        
        # 填写密码
        if not wait_for_element_and_fill(driver, '[name="password"]', password, "密码"):
            return False
        
        # 填写确认密码
        if not wait_for_element_and_fill(driver, '[name="confirmPassword"]', password, "确认密码"):
            return False
        
        # 4. 提交表单
        print("\n🚀 提交注册表单...")
        try:
            submit_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"]'))
            )
            
            # 使用 JavaScript 点击以避免拦截
            driver.execute_script("arguments[0].click();", submit_button)
            print("✅ 注册按钮已点击")
            
        except Exception as e:
            print(f"❌ 点击注册按钮失败: {e}")
            return False
        
        # 5. 等待结果
        print("⏳ 等待注册结果...")
        
        # 监控页面变化
        initial_url = driver.current_url
        start_time = time.time()
        
        while time.time() - start_time < 30:
            current_url = driver.current_url
            
            print(f"📍 当前 URL: {current_url}")
            
            # 检查是否跳转
            if current_url != initial_url:
                if 'login' in current_url:
                    print("🎉 已跳转到登录页面，注册可能成功!")
                    
                    # 保存注册信息
                    save_registration_info(email, username, password, current_url)
                    
                    # 尝试登录验证
                    return verify_login(driver, username, password)
                
                elif 'dashboard' in current_url:
                    print("🎉 直接跳转到仪表板，注册成功!")
                    save_registration_info(email, username, password, current_url)
                    return True
                
                else:
                    print(f"🔍 跳转到其他页面: {current_url}")
            
            # 检查页面内容
            try:
                page_text = driver.execute_script("return document.body.innerText;").lower()
                
                if any(word in page_text for word in ['success', 'welcome', 'dashboard']):
                    print("🎉 检测到成功关键词!")
                    save_registration_info(email, username, password, current_url)
                    return True
                
                elif any(word in page_text for word in ['error', 'failed', 'invalid', 'expired']):
                    print("❌ 检测到错误关键词!")
                    print(f"页面内容片段: {page_text[:200]}...")
                    return False
            except:
                pass
            
            time.sleep(3)
        
        print("⏰ 等待超时")
        return False
    
    except Exception as e:
        print(f"❌ 注册过程出错: {e}")
        return False
    
    finally:
        print("\n⏳ 保持浏览器打开 30 秒以便查看结果...")
        time.sleep(30)
        driver.quit()

def save_registration_info(email, username, password, final_url):
    """保存注册信息"""
    registration_data = {
        'email': email,
        'username': username,
        'password': password,
        'registration_url': 'https://app.ddai.space/register?ref=Bp48g624',
        'final_url': final_url,
        'registration_time': time.strftime('%Y-%m-%d %H:%M:%S'),
        'method': 'final_optimized',
        'success': True
    }
    
    filename = f'final_registration_{int(time.time())}.json'
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(registration_data, f, indent=2, ensure_ascii=False)
    
    print(f"📁 注册信息已保存到: {filename}")

def verify_login(driver, username, password):
    """验证登录"""
    try:
        print("\n🔐 开始登录验证...")
        
        # 如果不在登录页面，导航到登录页面
        if 'login' not in driver.current_url:
            driver.get("https://app.ddai.space/login")
            time.sleep(3)
        
        # 填写用户名
        if not wait_for_element_and_fill(driver, '[name="usernameOrEmail"]', username, "登录用户名"):
            return False
        
        # 填写密码
        if not wait_for_element_and_fill(driver, '[name="password"]', password, "登录密码"):
            return False
        
        # 点击登录按钮
        try:
            login_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
            driver.execute_script("arguments[0].click();", login_button)
            print("✅ 登录按钮已点击")
        except Exception as e:
            print(f"❌ 点击登录按钮失败: {e}")
            return False
        
        # 等待登录结果
        time.sleep(8)
        
        current_url = driver.current_url
        print(f"📍 登录后 URL: {current_url}")
        
        if 'dashboard' in current_url or ('login' not in current_url and 'register' not in current_url):
            print("🎉 登录成功！注册验证通过！")
            return True
        else:
            print("❌ 登录失败，可能注册未成功")
            return False
    
    except Exception as e:
        print(f"❌ 登录验证失败: {e}")
        return False

def main():
    """主函数"""
    success = final_register()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 DDAI 注册和验证成功!")
        print("✅ 账户已创建并验证")
    else:
        print("❌ 注册失败")
        print("💡 请检查 Turnstile 验证是否正确完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
