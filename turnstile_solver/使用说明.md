# Turnstile Solver 使用说明

这是一个用于自动解决 Cloudflare Turnstile CAPTCHA 的工具，平均解决时间约为2秒。

## 📋 项目信息

- **项目地址**: https://github.com/odell0111/turnstile_solver
- **版本**: v3.16
- **Python要求**: >= 3.10
- **安装位置**: `/Users/<USER>/Desktop/自动化验证/turnstile_solver`

## ✅ 安装状态

- [x] Python 3.11.13 环境
- [x] 项目依赖包
- [x] Patchright 浏览器引擎
- [x] Solver 命令行工具

## 🚀 快速开始

### 方法1: 使用启动脚本（推荐）

```bash
# 进入项目目录
cd "/Users/<USER>/Desktop/自动化验证/turnstile_solver"

# 启动服务器
./start_solver.sh
```

### 方法2: 手动启动

```bash
# 启动基础服务器
solver

# 或者使用自定义参数
solver --port 8088 --secret jWRN7DH6 --browser-position 100 100
```

## 📡 服务器配置

- **默认端口**: 8088
- **默认密钥**: jWRN7DH6
- **服务器地址**: http://127.0.0.1:8088
- **浏览器位置**: (100, 100) - 可见模式

## 🔧 使用方法

### 1. 启动服务器

首先启动 Turnstile Solver 服务器：

```bash
solver --port 8088 --secret jWRN7DH6
```

服务器启动后会显示：
```
[时间] INFO     Server up and running                                     
       INFO     Running on http://0.0.0.0:8088 (CTRL + C to quit)
```

### 2. 发送解决请求

#### 使用 cURL

```bash
curl --location --request GET 'http://127.0.0.1:8088/solve' \
--header 'ngrok-skip-browser-warning: _' \
--header 'secret: jWRN7DH6' \
--header 'Content-Type: application/json' \
--data '{
    "site_url": "https://spotifydown.com",
    "site_key": "0x4AAAAAAAByvC31sFG0MSlp"
}'
```

#### 使用 Python

```python
import requests

SERVER_URL = "http://127.0.0.1:8088"

url = f"{SERVER_URL}/solve"

headers = {
    'ngrok-skip-browser-warning': '_',
    'secret': 'jWRN7DH6',
    'Content-Type': 'application/json'
}

json_data = {
    "site_url": "https://spotifydown.com",
    "site_key": "0x4AAAAAAAByvC31sFG0MSlp"
}

response = requests.get(url=url, headers=headers, json=json_data)
data = response.json()

print("Token:", data['token'])
```

#### 使用示例脚本

```bash
# 运行提供的示例脚本
python3 example_usage.py
```

### 3. 响应格式

成功响应示例：
```json
{
  "elapsed": "2.641519",
  "message": null,
  "status": "OK",
  "token": "0.MwOLQ3dg..."
}
```

## 📁 项目文件

- `example_usage.py` - Python使用示例脚本
- `start_solver.sh` - 服务器启动脚本
- `使用说明.md` - 本说明文件
- `src/` - 项目源代码
- `requirements.txt` - Python依赖列表

## ⚙️ 高级配置

### 常用参数

```bash
solver \
  --port 8088 \                    # 服务器端口
  --secret jWRN7DH6 \             # API密钥
  --browser-position 100 100 \     # 浏览器窗口位置
  --max-attempts 3 \               # 最大尝试次数
  --captcha-timeout 30 \           # CAPTCHA超时时间(秒)
  --page-load-timeout 30 \         # 页面加载超时时间(秒)
  --reload-on-overrun              # 超时时重新加载页面
```

### 代理设置

```bash
# 全局代理
solver --proxy-server http://proxy.com:3128 --proxy-username user --proxy-password pass

# 从文件加载代理列表
solver --proxies proxies.txt
```

### 性能调优

```bash
solver \
  --max-contexts 40 \              # 最大浏览器上下文数
  --max-pages 2 \                  # 每个浏览器的最大页面数
  --multiple-browser-instances     # 为每个上下文使用新的浏览器实例
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查找占用端口的进程
   lsof -i :8088
   
   # 终止进程
   kill -9 <PID>
   ```

2. **浏览器启动失败**
   ```bash
   # 重新安装 Patchright Chrome
   patchright install --force chrome
   ```

3. **Python版本问题**
   ```bash
   # 检查Python版本
   python3 --version
   
   # 应该 >= 3.10
   ```

### 日志查看

- 服务器日志会显示在终端
- 详细日志可以通过 `--log-level 10` 参数启用

## ⚠️ 注意事项

1. **合法使用**: 请确保在合法和授权的情况下使用此工具
2. **资源消耗**: 每个浏览器上下文会消耗一定内存
3. **网络要求**: 需要稳定的网络连接以获得最佳性能
4. **浏览器检测**: 使用 Patchright 修补版浏览器以避免检测

## 📞 支持

- **项目地址**: https://github.com/odell0111/turnstile_solver
- **问题反馈**: 在 GitHub 项目页面提交 Issue
- **文档**: 查看项目 README.md

---

**安装完成时间**: 2025年7月19日  
**安装者**: Augment Agent  
**状态**: ✅ 可直接使用
