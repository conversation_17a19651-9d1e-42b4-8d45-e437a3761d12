#!/usr/bin/env python3
"""
完全自动化 DDAI 注册脚本
使用 undetected-chromedriver 自动绕过 Cloudflare Turnstile
"""

import json
import time
import random
import string
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def generate_credentials():
    """生成注册凭据"""
    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
    email = f"{username}@mlanm.online"
    password = "Aa88888888."
    return email, username, password

def setup_undetected_driver():
    """设置 undetected Chrome 驱动"""
    options = uc.ChromeOptions()
    
    # 基本设置
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--window-size=1920,1080')
    
    # 禁用图片加载以提高速度
    prefs = {
        "profile.managed_default_content_settings.images": 2,
        "profile.default_content_setting_values.notifications": 2
    }
    options.add_experimental_option("prefs", prefs)
    
    # 创建 undetected Chrome 实例
    driver = uc.Chrome(options=options, version_main=None)
    
    return driver

def wait_for_element_and_fill(driver, selector, value, description="字段", timeout=15):
    """等待元素并填写值"""
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
        )
        element.clear()
        time.sleep(0.5)
        element.send_keys(value)
        print(f"✅ {description}已填写: {value}")
        return True
    except Exception as e:
        print(f"❌ 填写{description}失败: {e}")
        return False

def wait_for_turnstile_auto_solve(driver, max_wait=120):
    """等待 Turnstile 自动解决"""
    print("⏳ 等待 Turnstile 自动解决...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        try:
            # 检查 Turnstile Token
            token_field = driver.find_element(By.CSS_SELECTOR, '[name="cf-turnstile-response"]')
            token_value = token_field.get_attribute('value')
            
            if token_value and len(token_value) > 100:
                print(f"✅ Turnstile 自动解决成功!")
                print(f"   Token: {token_value[:50]}...")
                return token_value
            
            # 检查是否有 Turnstile iframe 并尝试自动点击
            try:
                # 查找 Turnstile 复选框
                turnstile_checkbox = driver.find_elements(By.CSS_SELECTOR, 
                    'input[type="checkbox"][id*="turnstile"], '
                    'input[type="checkbox"][class*="turnstile"], '
                    'iframe[src*="turnstile"] + input, '
                    '.cf-turnstile input[type="checkbox"]'
                )
                
                if turnstile_checkbox:
                    print("🔍 发现 Turnstile 复选框，尝试自动点击...")
                    for checkbox in turnstile_checkbox:
                        if checkbox.is_displayed() and checkbox.is_enabled():
                            driver.execute_script("arguments[0].click();", checkbox)
                            print("✅ 已点击 Turnstile 复选框")
                            time.sleep(3)
                            break
                
                # 尝试在 iframe 中查找并点击
                iframes = driver.find_elements(By.CSS_SELECTOR, 'iframe[src*="turnstile"]')
                for iframe in iframes:
                    try:
                        driver.switch_to.frame(iframe)
                        checkbox = driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
                        if checkbox.is_displayed():
                            driver.execute_script("arguments[0].click();", checkbox)
                            print("✅ 在 iframe 中点击了 Turnstile 复选框")
                        driver.switch_to.default_content()
                        time.sleep(3)
                        break
                    except:
                        driver.switch_to.default_content()
                        continue
                        
            except Exception as e:
                pass
            
            time.sleep(2)
            
        except Exception as e:
            time.sleep(2)
    
    print("⏰ Turnstile 等待超时")
    return None

def fully_automated_register():
    """完全自动化注册"""
    print("🎯 DDAI 完全自动化注册脚本")
    print("🚀 使用 undetected-chromedriver 绕过检测")
    print("=" * 60)
    
    # 生成凭据
    email, username, password = generate_credentials()
    
    print(f"📧 邮箱: {email}")
    print(f"👤 用户名: {username}")
    print(f"🔐 密码: {password}")
    print()
    
    # 启动 undetected Chrome
    print("🌐 启动 undetected Chrome...")
    driver = setup_undetected_driver()
    
    try:
        # 1. 打开注册页面
        print("📍 打开注册页面...")
        driver.get("https://app.ddai.space/register?ref=Bp48g624")
        
        # 等待页面加载
        print("⏳ 等待页面加载...")
        time.sleep(8)
        
        # 2. 填写表单
        print("📝 开始填写注册表单...")
        
        # 填写邮箱
        if not wait_for_element_and_fill(driver, '[name="email"]', email, "邮箱"):
            return False
        
        # 填写用户名
        if not wait_for_element_and_fill(driver, '[name="username"]', username, "用户名"):
            return False
        
        # 填写密码
        if not wait_for_element_and_fill(driver, '[name="password"]', password, "密码"):
            return False
        
        # 填写确认密码
        if not wait_for_element_and_fill(driver, '[name="confirmPassword"]', password, "确认密码"):
            return False
        
        # 3. 等待 Turnstile 自动解决
        token = wait_for_turnstile_auto_solve(driver, max_wait=180)
        
        if not token:
            print("❌ Turnstile 自动解决失败")
            print("💡 尝试手动等待...")
            
            # 给用户一个手动完成的机会
            print("\n" + "=" * 40)
            print("🔔 如果看到 Turnstile 验证框，请手动点击")
            print("然后按回车继续，或等待 30 秒自动继续")
            print("=" * 40)
            
            # 等待 30 秒或用户按回车
            import select
            import sys
            
            print("⏳ 等待 30 秒或按回车继续...")
            ready, _, _ = select.select([sys.stdin], [], [], 30)
            if ready:
                sys.stdin.readline()
            
            # 再次检查 Token
            try:
                token_field = driver.find_element(By.CSS_SELECTOR, '[name="cf-turnstile-response"]')
                token = token_field.get_attribute('value')
                
                if token and len(token) > 50:
                    print(f"✅ 获取到 Token: {token[:50]}...")
                else:
                    print("⚠️  未获取到 Token，但继续尝试提交...")
            except:
                print("⚠️  无法检查 Token，但继续尝试提交...")
        
        # 4. 提交表单
        print("\n🚀 提交注册表单...")
        try:
            submit_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"]'))
            )
            
            # 使用 JavaScript 点击
            driver.execute_script("arguments[0].click();", submit_button)
            print("✅ 注册按钮已点击")
            
        except Exception as e:
            print(f"❌ 点击注册按钮失败: {e}")
            return False
        
        # 5. 等待结果
        print("⏳ 等待注册结果...")
        
        # 监控页面变化
        initial_url = driver.current_url
        start_time = time.time()
        
        while time.time() - start_time < 30:
            current_url = driver.current_url
            
            print(f"📍 当前 URL: {current_url}")
            
            # 检查是否跳转
            if current_url != initial_url:
                if 'login' in current_url:
                    print("🎉 已跳转到登录页面，注册可能成功!")
                    
                    # 保存注册信息
                    save_registration_info(email, username, password, current_url, token)
                    
                    # 尝试登录验证
                    return verify_login(driver, username, password)
                
                elif 'dashboard' in current_url:
                    print("🎉 直接跳转到仪表板，注册成功!")
                    save_registration_info(email, username, password, current_url, token)
                    return True
                
                else:
                    print(f"🔍 跳转到其他页面: {current_url}")
            
            # 检查页面内容
            try:
                page_text = driver.execute_script("return document.body.innerText;").lower()
                
                if any(word in page_text for word in ['success', 'welcome', 'dashboard']):
                    print("🎉 检测到成功关键词!")
                    save_registration_info(email, username, password, current_url, token)
                    return True
                
                elif any(word in page_text for word in ['error', 'failed', 'invalid', 'expired']):
                    print("❌ 检测到错误关键词!")
                    print(f"页面内容片段: {page_text[:200]}...")
                    return False
            except:
                pass
            
            time.sleep(3)
        
        print("⏰ 等待超时")
        return False
    
    except Exception as e:
        print(f"❌ 注册过程出错: {e}")
        return False
    
    finally:
        print("\n⏳ 保持浏览器打开 20 秒以便查看结果...")
        time.sleep(20)
        driver.quit()

def save_registration_info(email, username, password, final_url, token):
    """保存注册信息"""
    registration_data = {
        'email': email,
        'username': username,
        'password': password,
        'token': token,
        'registration_url': 'https://app.ddai.space/register?ref=Bp48g624',
        'final_url': final_url,
        'registration_time': time.strftime('%Y-%m-%d %H:%M:%S'),
        'method': 'fully_automated_undetected_chrome',
        'success': True
    }
    
    filename = f'fully_automated_registration_{int(time.time())}.json'
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(registration_data, f, indent=2, ensure_ascii=False)
    
    print(f"📁 注册信息已保存到: {filename}")

def verify_login(driver, username, password):
    """验证登录"""
    try:
        print("\n🔐 开始登录验证...")
        
        # 如果不在登录页面，导航到登录页面
        if 'login' not in driver.current_url:
            driver.get("https://app.ddai.space/login")
            time.sleep(3)
        
        # 填写用户名
        if not wait_for_element_and_fill(driver, '[name="usernameOrEmail"]', username, "登录用户名"):
            return False
        
        # 填写密码
        if not wait_for_element_and_fill(driver, '[name="password"]', password, "登录密码"):
            return False
        
        # 点击登录按钮
        try:
            login_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
            driver.execute_script("arguments[0].click();", login_button)
            print("✅ 登录按钮已点击")
        except Exception as e:
            print(f"❌ 点击登录按钮失败: {e}")
            return False
        
        # 等待登录结果
        time.sleep(8)
        
        current_url = driver.current_url
        print(f"📍 登录后 URL: {current_url}")
        
        if 'dashboard' in current_url or ('login' not in current_url and 'register' not in current_url):
            print("🎉 登录成功！注册验证通过！")
            return True
        else:
            print("❌ 登录失败，可能注册未成功")
            return False
    
    except Exception as e:
        print(f"❌ 登录验证失败: {e}")
        return False

def main():
    """主函数"""
    success = fully_automated_register()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 DDAI 完全自动化注册成功!")
        print("✅ 账户已创建并验证")
        print("🚀 使用 undetected-chromedriver 成功绕过检测!")
    else:
        print("❌ 注册失败")
        print("💡 可能需要进一步优化绕过策略")
    print("=" * 60)

if __name__ == "__main__":
    main()
