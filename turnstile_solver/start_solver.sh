#!/bin/bash

# Turnstile Solver 启动脚本
# 这个脚本用于启动 turnstile_solver 服务器

echo "🚀 启动 Turnstile Solver 服务器..."
echo "=================================="

# 检查 solver 命令是否可用
if ! command -v solver &> /dev/null; then
    echo "❌ 错误: solver 命令未找到"
    echo "请确保已正确安装 turnstile_solver:"
    echo "pip install git+https://github.com/odell0111/turnstile_solver@main"
    exit 1
fi

# 检查端口是否被占用
if lsof -Pi :8088 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  警告: 端口 8088 已被占用"
    echo "正在尝试终止占用端口的进程..."
    lsof -ti:8088 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

echo "📡 启动服务器在端口 8088..."
echo "🔑 使用密钥: jWRN7DH6"
echo "🌐 服务器地址: http://127.0.0.1:8088"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "=================================="

# 启动服务器
solver --port 8088 --secret jWRN7DH6 --browser-position 100 100 --max-attempts 3 --captcha-timeout 30 --page-load-timeout 30 --reload-on-overrun
