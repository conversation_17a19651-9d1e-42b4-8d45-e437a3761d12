#!/usr/bin/env python3
"""
直接测试 DDAI 网站的 Turnstile
尝试一些可能的 site_key 值
"""

import requests
import json
import time

# 可能的 site_key 列表（基于常见模式）
POSSIBLE_SITEKEYS = [
    # 常见的测试 site_key
    "0x4AAAAAAAByvC31sFG0MSlp",
    "0x4AAAAAAAC3HKBVrXvGMKBF", 
    "0x4AAAAAAADnPIDROzI0qf",
    "0x4AAAAAAABkTM-IxRTMPH",
    "0x4AAAAAAAGQrABaoAr1zK",
    
    # DDAI 可能使用的模式
    "0x4AAAAAAADDAI12345678",
    "0x4AAAAAAADDAISpace123",
    "0x4AAAAAAADDAI-Register",
    
    # 其他常见模式
    "0x4AAAAAAABkTM-IxRTMPH",
    "0x4AAAAAAAGQrABaoAr1zK",
    "0x4AAAAAAABkTM-IxRTMPH",
]

def test_sitekey(site_key, site_url="https://app.ddai.space/register"):
    """测试指定的 site_key"""
    print(f"🧪 测试 site_key: {site_key}")
    
    try:
        start_time = time.time()
        
        response = requests.get(
            url="http://127.0.0.1:8088/solve",
            headers={
                'ngrok-skip-browser-warning': '_',
                'secret': 'jWRN7DH6',
                'Content-Type': 'application/json'
            },
            json={
                "site_url": site_url,
                "site_key": site_key
            },
            timeout=90
        )
        
        elapsed = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('status') == 'OK':
                print(f"✅ 成功! 耗时: {elapsed:.2f}s")
                print(f"🎫 Token: {data.get('token', '')[:50]}...")
                
                # 保存成功结果
                timestamp = int(time.time())
                filename = f"ddai_success_{timestamp}.json"
                with open(filename, 'w') as f:
                    json.dump({
                        'site_url': site_url,
                        'site_key': site_key,
                        'token': data.get('token'),
                        'elapsed': elapsed,
                        'server_elapsed': data.get('elapsed'),
                        'timestamp': timestamp
                    }, f, indent=2)
                
                print(f"📁 结果保存到: {filename}")
                return True, data
            else:
                print(f"❌ 失败: {data.get('message', '未知错误')}")
                return False, data
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False, None
            
    except requests.exceptions.Timeout:
        print("⏰ 超时")
        return False, None
    except requests.exceptions.ConnectionError:
        print("🔌 连接错误 - 请确保服务器运行")
        return False, None
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False, None

def check_server():
    """检查服务器状态"""
    try:
        response = requests.get("http://127.0.0.1:8088/", timeout=5)
        return response.status_code in [200, 403, 404]
    except:
        return False

def main():
    print("🎯 DDAI Turnstile 直接测试工具")
    print("=" * 50)
    
    # 检查服务器
    if not check_server():
        print("❌ 服务器未运行，请先启动: ./start_solver.sh")
        return
    
    print("✅ 服务器正在运行")
    print(f"🧪 将测试 {len(POSSIBLE_SITEKEYS)} 个可能的 site_key")
    print()
    
    success_count = 0
    
    for i, site_key in enumerate(POSSIBLE_SITEKEYS, 1):
        print(f"--- 测试 {i}/{len(POSSIBLE_SITEKEYS)} ---")
        
        success, result = test_sitekey(site_key)
        
        if success:
            success_count += 1
            print(f"🎉 找到有效的 site_key!")
            print(f"📋 site_key: {site_key}")
            print(f"🎫 token: {result.get('token', '')[:50]}...")
            
            # 询问是否继续测试其他的
            if i < len(POSSIBLE_SITEKEYS):
                continue_test = input("\n继续测试其他 site_key? (y/n): ").lower()
                if continue_test != 'y':
                    break
        
        # 避免请求过于频繁
        if i < len(POSSIBLE_SITEKEYS):
            print("⏳ 等待 2 秒...")
            time.sleep(2)
        
        print()
    
    print("=" * 50)
    print(f"🏁 测试完成!")
    print(f"✅ 成功: {success_count}/{len(POSSIBLE_SITEKEYS)}")
    
    if success_count == 0:
        print("\n💡 建议:")
        print("1. 手动在浏览器中查找真实的 site_key")
        print("2. 检查网站是否可正常访问")
        print("3. 确认网站确实使用了 Cloudflare Turnstile")

if __name__ == "__main__":
    main()
