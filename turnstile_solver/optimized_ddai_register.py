#!/usr/bin/env python3
"""
基于 ddai_副本.json 优化的 DDAI 注册脚本
使用临时邮箱服务和完整的注册流程
"""

import json
import time
import random
import string
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains

class TempEmailService:
    """临时邮箱服务"""
    
    def __init__(self):
        self.base_url = "https://www.1secmail.com/api/v1/"
        self.email = None
        self.domain = None
        self.username = None
    
    def generate_email(self):
        """生成临时邮箱"""
        try:
            # 获取可用域名
            response = requests.get(f"{self.base_url}?action=getDomainList")
            domains = response.json()
            
            if domains:
                self.domain = random.choice(domains)
                self.username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
                self.email = f"{self.username}@{self.domain}"
                print(f"✅ 生成临时邮箱: {self.email}")
                return self.email
        except Exception as e:
            print(f"⚠️  临时邮箱生成失败，使用备用方案: {e}")
            # 备用方案：使用 mlanm.online 域名
            self.username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
            self.email = f"{self.username}@mlanm.online"
            return self.email
    
    def check_emails(self):
        """检查邮箱中的邮件"""
        if not self.username or not self.domain:
            return []
        
        try:
            response = requests.get(f"{self.base_url}?action=getMessages&login={self.username}&domain={self.domain}")
            return response.json()
        except:
            return []
    
    def get_email_content(self, email_id):
        """获取邮件内容"""
        try:
            response = requests.get(f"{self.base_url}?action=readMessage&login={self.username}&domain={self.domain}&id={email_id}")
            return response.json()
        except:
            return None

def setup_chrome_driver():
    """设置 Chrome 驱动"""
    chrome_options = Options()
    
    # 基本设置
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 设置用户代理
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    # 禁用图片加载以提高速度
    prefs = {
        "profile.managed_default_content_settings.images": 2,
        "profile.default_content_setting_values.notifications": 2
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    # 反检测脚本
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def handle_turnstile_manually(driver):
    """手动处理 Turnstile 验证"""
    print("🔔 Turnstile 验证处理")
    print("=" * 40)

    try:
        # 检查是否存在 Turnstile
        turnstile_elements = driver.find_elements(By.CSS_SELECTOR, '[id*="turnstile"], [class*="turnstile"], iframe[src*="turnstile"]')

        if turnstile_elements:
            print("🔍 检测到 Turnstile 验证")
            print("请在浏览器中手动完成 Turnstile 验证")
            print("完成后回到终端按回车继续...")
            input("✋ 按回车键继续...")

            # 检查 Token 是否生成
            try:
                token_field = driver.find_element(By.CSS_SELECTOR, '[name="cf-turnstile-response"]')
                token_value = token_field.get_attribute('value')

                if token_value and len(token_value) > 50:
                    print(f"✅ 检测到 Turnstile Token: {token_value[:50]}...")
                    return True
                else:
                    print("❌ 未检测到有效 Token")
                    return False
            except:
                print("❌ 无法找到 Token 字段")
                return False
        else:
            print("✅ 未检测到 Turnstile，直接继续")
            return True

    except Exception as e:
        print(f"❌ Turnstile 处理出错: {e}")
        return False

def optimized_register():
    """优化的注册流程"""
    print("🎯 DDAI 优化注册脚本")
    print("=" * 60)
    
    # 生成临时邮箱
    temp_email = TempEmailService()
    email = temp_email.generate_email()
    
    # 生成用户名和密码（基于原脚本的格式）
    username = email.split('@')[0]  # 使用邮箱前缀作为用户名
    password = "Aa88888888."  # 使用原脚本中的密码
    
    print(f"📧 邮箱: {email}")
    print(f"👤 用户名: {username}")
    print(f"🔐 密码: {password}")
    print()
    
    # 设置浏览器
    print("🌐 启动浏览器...")
    driver = setup_chrome_driver()
    
    try:
        # 1. 打开注册页面（使用原脚本中的推荐链接）
        print("📍 打开注册页面...")
        driver.get("https://app.ddai.space/register?ref=Bp48g624")
        
        # 等待页面加载
        print("⏳ 等待页面加载...")
        time.sleep(8)
        
        # 2. 处理 Turnstile 验证
        if not handle_turnstile_manually(driver):
            print("❌ Turnstile 验证失败")
            return False
        
        # 3. 填写邮箱
        print("📝 填写邮箱...")
        try:
            email_field = WebDriverWait(driver, 15).until(
                EC.element_to_be_clickable((By.NAME, "email"))
            )
            email_field.clear()
            time.sleep(0.5)
            email_field.send_keys(email)
            print(f"✅ 邮箱已填写: {email}")
        except Exception as e:
            print(f"❌ 填写邮箱失败: {e}")
            return False
        
        # 4. 填写用户名
        print("📝 填写用户名...")
        try:
            username_field = driver.find_element(By.NAME, "username")
            username_field.clear()
            time.sleep(0.5)
            username_field.send_keys(username)
            print(f"✅ 用户名已填写: {username}")
        except Exception as e:
            print(f"❌ 填写用户名失败: {e}")
            return False
        
        # 5. 填写密码
        print("📝 填写密码...")
        try:
            password_field = driver.find_element(By.NAME, "password")
            password_field.clear()
            time.sleep(0.5)
            password_field.send_keys(password)
            print(f"✅ 密码已填写: {password}")
        except Exception as e:
            print(f"❌ 填写密码失败: {e}")
            return False
        
        # 6. 填写确认密码
        print("📝 填写确认密码...")
        try:
            confirm_password_field = driver.find_element(By.NAME, "confirmPassword")
            confirm_password_field.clear()
            time.sleep(0.5)
            confirm_password_field.send_keys(password)
            print(f"✅ 确认密码已填写: {password}")
        except Exception as e:
            print(f"❌ 填写确认密码失败: {e}")
            return False
        
        # 7. 提交注册表单
        print("🚀 提交注册...")
        try:
            submit_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
            driver.execute_script("arguments[0].click();", submit_button)
            print("✅ 注册按钮已点击")
        except Exception as e:
            print(f"❌ 点击注册按钮失败: {e}")
            return False
        
        # 8. 等待注册完成
        print("⏳ 等待注册结果...")
        time.sleep(10)
        
        current_url = driver.current_url
        print(f"📍 当前 URL: {current_url}")
        
        # 检查是否跳转到登录页面
        if 'login' in current_url or current_url != 'https://app.ddai.space/register?ref=Bp48g624':
            print("🎉 注册可能成功，已跳转!")
            
            # 保存注册信息
            registration_data = {
                'email': email,
                'username': username,
                'password': password,
                'registration_url': 'https://app.ddai.space/register?ref=Bp48g624',
                'final_url': current_url,
                'registration_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'method': 'optimized_with_temp_email',
                'success': True
            }
            
            with open(f'optimized_registration_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                json.dump(registration_data, f, indent=2, ensure_ascii=False)
            
            print("📁 注册信息已保存")
            
            # 9. 尝试登录验证
            print("🔐 尝试登录验证...")
            return verify_login(driver, username, password)
        else:
            print("❌ 注册可能失败，未发生页面跳转")
            return False
    
    except Exception as e:
        print(f"❌ 注册过程出错: {e}")
        return False
    
    finally:
        print("⏳ 等待 10 秒后关闭浏览器...")
        time.sleep(10)
        driver.quit()

def verify_login(driver, username, password):
    """验证登录"""
    try:
        print("🔐 开始登录验证...")
        
        # 如果不在登录页面，先导航到登录页面
        if 'login' not in driver.current_url:
            driver.get("https://app.ddai.space/login")
            time.sleep(3)
        
        # 填写用户名
        username_field = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.NAME, "usernameOrEmail"))
        )
        username_field.clear()
        username_field.send_keys(username)
        print(f"✅ 登录用户名已填写: {username}")
        
        # 填写密码
        password_field = driver.find_element(By.NAME, "password")
        password_field.clear()
        password_field.send_keys(password)
        print(f"✅ 登录密码已填写")
        
        # 点击登录按钮
        login_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
        driver.execute_script("arguments[0].click();", login_button)
        print("✅ 登录按钮已点击")
        
        # 等待登录结果
        time.sleep(5)
        
        current_url = driver.current_url
        print(f"📍 登录后 URL: {current_url}")
        
        if 'dashboard' in current_url or 'login' not in current_url:
            print("🎉 登录成功！注册验证通过！")
            return True
        else:
            print("❌ 登录失败")
            return False
    
    except Exception as e:
        print(f"❌ 登录验证失败: {e}")
        return False

def main():
    """主函数"""
    success = optimized_register()
    
    if success:
        print()
        print("=" * 60)
        print("🎉 DDAI 注册和验证成功!")
        print("=" * 60)
    else:
        print()
        print("=" * 60)
        print("❌ 注册失败")
        print("=" * 60)

if __name__ == "__main__":
    main()
