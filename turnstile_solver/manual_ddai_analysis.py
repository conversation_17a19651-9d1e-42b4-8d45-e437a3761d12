#!/usr/bin/env python3
"""
手动分析 DDAI 网站的 Turnstile
通过浏览器开发者工具手动查找 site_key
"""

import asyncio
import json
import time
import requests
from patchright.async_api import async_playwright

# 常见的 Turnstile site_key 模式（用于测试）
COMMON_SITEKEYS = [
    "0x4AAAAAAAByvC31sFG0MSlp",  # 示例 site_key
    "0x4AAAAAAAC3HKBVrXvGMKBF",  # 另一个常见的
    "0x4AAAAAAADnPIDROzI0qf",   # 测试用
]

async def interactive_analysis():
    """交互式分析 - 打开浏览器让用户手动查找"""
    print("🔍 交互式分析模式")
    print("=" * 60)
    print("我将打开浏览器，请您按以下步骤操作：")
    print()
    print("1. 📱 打开开发者工具 (F12)")
    print("2. 🔍 在 Elements 标签中搜索 'turnstile' 或 'sitekey'")
    print("3. 📋 复制找到的 data-sitekey 值")
    print("4. ⌨️  在终端中输入找到的 site_key")
    print()
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            print("🌐 正在打开 DDAI 注册页面...")
            await page.goto("https://app.ddai.space/register", wait_until="networkidle")
            
            print("✅ 页面已打开，请在浏览器中查找 site_key")
            print("💡 提示：在开发者工具中搜索 'data-sitekey' 或 'cf-turnstile'")
            print()
            
            # 等待用户操作
            input("按 Enter 键继续（找到 site_key 后）...")
            
        except Exception as e:
            print(f"❌ 打开页面失败: {e}")
        finally:
            await browser.close()

def test_with_sitekey(site_key):
    """使用指定的 site_key 测试 Turnstile 解决"""
    print(f"\n🚀 测试 site_key: {site_key}")
    print("=" * 60)
    
    try:
        print("📡 发送解决请求...")
        start_time = time.time()
        
        response = requests.get(
            url="http://127.0.0.1:8088/solve",
            headers={
                'ngrok-skip-browser-warning': '_',
                'secret': 'jWRN7DH6',
                'Content-Type': 'application/json'
            },
            json={
                "site_url": "https://app.ddai.space/register",
                "site_key": site_key
            },
            timeout=120
        )
        
        elapsed = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('status') == 'OK':
                print(f"✅ 解决成功!")
                print(f"⏱️  总耗时: {elapsed:.2f} 秒")
                print(f"🔧 服务器耗时: {data.get('elapsed', 'N/A')} 秒")
                print(f"🎫 Token: {data.get('token', 'N/A')[:50]}...")
                print(f"📝 状态: {data.get('status', 'N/A')}")
                
                # 保存结果
                timestamp = int(time.time())
                filename = f"ddai_success_{timestamp}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump({
                        'site_url': 'https://app.ddai.space/register',
                        'site_key': site_key,
                        'result': data,
                        'total_time': elapsed
                    }, f, indent=2, ensure_ascii=False)
                
                print(f"📁 结果已保存到: {filename}")
                return data
            else:
                print(f"❌ 解决失败: {data.get('message', '未知错误')}")
                return None
        else:
            print(f"❌ 服务器错误: {response.status_code}")
            print(f"响应: {response.text[:200]}...")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到 Turnstile Solver 服务器")
        print("请确保服务器正在运行: ./start_solver.sh")
        return None
    except requests.exceptions.Timeout:
        print("❌ 请求超时，可能网站响应较慢")
        return None
    except Exception as e:
        print(f"❌ 请求错误: {e}")
        return None

def check_server():
    """检查服务器状态"""
    try:
        response = requests.get("http://127.0.0.1:8088/", timeout=5)
        if response.status_code in [200, 403, 404]:
            print("✅ Turnstile Solver 服务器正在运行")
            return True
        else:
            print(f"⚠️  服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("请先启动服务器: ./start_solver.sh")
        return False
    except Exception as e:
        print(f"❌ 检查服务器时出错: {e}")
        return False

async def main():
    """主函数"""
    print("🎯 DDAI 网站 Turnstile 手动分析工具")
    print("=" * 60)
    
    # 检查服务器
    if not check_server():
        return
    
    print("\n选择操作模式:")
    print("1. 🔍 交互式分析（打开浏览器手动查找 site_key）")
    print("2. 🧪 测试常见 site_key")
    print("3. ⌨️  手动输入 site_key")
    
    choice = input("\n请选择 (1/2/3): ").strip()
    
    if choice == "1":
        await interactive_analysis()
        
        # 询问用户找到的 site_key
        site_key = input("\n请输入找到的 site_key: ").strip()
        if site_key:
            test_with_sitekey(site_key)
        else:
            print("❌ 未输入 site_key")
    
    elif choice == "2":
        print("\n🧪 测试常见的 site_key...")
        
        for i, site_key in enumerate(COMMON_SITEKEYS, 1):
            print(f"\n--- 测试 {i}/{len(COMMON_SITEKEYS)} ---")
            result = test_with_sitekey(site_key)
            
            if result and result.get('status') == 'OK':
                print(f"🎉 找到有效的 site_key: {site_key}")
                break
            
            if i < len(COMMON_SITEKEYS):
                print("⏳ 等待 3 秒后继续...")
                time.sleep(3)
        else:
            print("\n❌ 所有常见 site_key 都无效")
            print("💡 建议使用交互式分析模式手动查找")
    
    elif choice == "3":
        site_key = input("\n请输入 site_key: ").strip()
        if site_key:
            test_with_sitekey(site_key)
        else:
            print("❌ 未输入 site_key")
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    asyncio.run(main())
