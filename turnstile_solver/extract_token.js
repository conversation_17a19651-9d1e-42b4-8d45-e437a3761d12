// DDAI Turnstile Token 提取脚本
// 在浏览器 Console 中运行此脚本

console.log('🔍 开始搜索 Turnstile Token...');

// 方法1: 标准选择器
console.log('\n--- 方法1: 标准选择器 ---');
try {
    const standardField = document.querySelector('[name="cf-turnstile-response"]');
    if (standardField && standardField.value) {
        console.log('✅ 找到标准字段:', standardField.value);
    } else {
        console.log('❌ 标准字段未找到或为空');
    }
} catch (e) {
    console.log('❌ 标准字段查找出错:', e.message);
}

// 方法2: ID选择器
console.log('\n--- 方法2: ID选择器 ---');
try {
    const idField = document.querySelector('#cf-chl-widget-msovg_response');
    if (idField && idField.value) {
        console.log('✅ 找到ID字段:', idField.value);
    } else {
        console.log('❌ ID字段未找到或为空');
    }
} catch (e) {
    console.log('❌ ID字段查找出错:', e.message);
}

// 方法3: 查找所有隐藏字段
console.log('\n--- 方法3: 所有隐藏字段 ---');
const hiddenInputs = document.querySelectorAll('input[type="hidden"]');
console.log(`找到 ${hiddenInputs.length} 个隐藏字段:`);

hiddenInputs.forEach((input, index) => {
    const name = input.name || '(无名称)';
    const id = input.id || '(无ID)';
    const value = input.value || '(空值)';
    
    console.log(`${index + 1}. 名称: ${name}, ID: ${id}`);
    
    if (value.length > 20) {
        console.log(`   ✅ 可能的Token: ${value.substring(0, 50)}...`);
    } else {
        console.log(`   值: ${value}`);
    }
});

// 方法4: 查找包含 turnstile 关键词的元素
console.log('\n--- 方法4: Turnstile 相关元素 ---');
const turnstileSelectors = [
    '[name*="turnstile"]',
    '[id*="turnstile"]',
    '[class*="turnstile"]',
    '[name*="cf-"]',
    '[id*="cf-"]',
    '[class*="cf-"]'
];

turnstileSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    if (elements.length > 0) {
        console.log(`选择器 ${selector} 找到 ${elements.length} 个元素:`);
        elements.forEach((el, index) => {
            console.log(`  ${index + 1}. 标签: ${el.tagName}, 值: ${el.value || '(无值)'}`);
        });
    }
});

// 方法5: 查找 Turnstile 容器
console.log('\n--- 方法5: Turnstile 容器分析 ---');
const turnstileContainer = document.querySelector('#cf-turnstile');
if (turnstileContainer) {
    console.log('✅ 找到 Turnstile 容器');
    
    // 查找容器内的所有 input 元素
    const containerInputs = turnstileContainer.querySelectorAll('input');
    console.log(`容器内有 ${containerInputs.length} 个 input 元素:`);
    
    containerInputs.forEach((input, index) => {
        console.log(`  ${index + 1}. 类型: ${input.type}, 名称: ${input.name}, 值长度: ${input.value ? input.value.length : 0}`);
        if (input.value && input.value.length > 20) {
            console.log(`     ✅ 可能的Token: ${input.value.substring(0, 50)}...`);
        }
    });
} else {
    console.log('❌ 未找到 Turnstile 容器');
}

// 方法6: 全局搜索长字符串
console.log('\n--- 方法6: 全局搜索长字符串 ---');
const allInputs = document.querySelectorAll('input');
const possibleTokens = [];

allInputs.forEach(input => {
    if (input.value && input.value.length > 50) {
        possibleTokens.push({
            element: input,
            name: input.name || '(无名称)',
            id: input.id || '(无ID)',
            value: input.value
        });
    }
});

if (possibleTokens.length > 0) {
    console.log(`✅ 找到 ${possibleTokens.length} 个可能的Token:`);
    possibleTokens.forEach((token, index) => {
        console.log(`${index + 1}. 名称: ${token.name}, ID: ${token.id}`);
        console.log(`   Token: ${token.value}`);
        console.log('   ---');
    });
} else {
    console.log('❌ 未找到长字符串Token');
}

// 总结
console.log('\n🎯 搜索完成!');
console.log('如果找到了Token，请复制上面显示的完整Token字符串');
console.log('然后粘贴到终端的保存工具中');

// 如果找到了可能的token，自动复制到剪贴板（如果浏览器支持）
if (possibleTokens.length > 0) {
    const firstToken = possibleTokens[0].value;
    try {
        navigator.clipboard.writeText(firstToken).then(() => {
            console.log('✅ 第一个Token已复制到剪贴板!');
        }).catch(() => {
            console.log('⚠️  无法自动复制，请手动复制Token');
        });
    } catch (e) {
        console.log('⚠️  浏览器不支持自动复制，请手动复制Token');
    }
}
