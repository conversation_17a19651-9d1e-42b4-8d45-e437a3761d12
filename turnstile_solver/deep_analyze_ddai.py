#!/usr/bin/env python3
"""
深度分析 DDAI 网站的 Turnstile
使用多种方法查找 site_key
"""

import asyncio
import json
import re
from patchright.async_api import async_playwright

async def deep_analyze_ddai():
    """深度分析 DDAI 网站"""
    print("🔍 DDAI 网站深度分析")
    print("=" * 60)
    
    async with async_playwright() as p:
        # 启动浏览器（非无头模式，方便观察）
        browser = await p.chromium.launch(
            headless=False,
            slow_mo=1000,
            args=['--disable-blink-features=AutomationControlled']
        )
        
        context = await browser.new_context(
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        
        page = await context.new_page()
        
        # 收集网络请求
        requests_log = []
        
        def log_request(request):
            if any(keyword in request.url.lower() for keyword in ['turnstile', 'cloudflare', 'captcha']):
                requests_log.append({
                    'url': request.url,
                    'method': request.method,
                    'headers': dict(request.headers)
                })
                print(f"📡 相关请求: {request.url}")
        
        page.on("request", log_request)
        
        try:
            print("🌐 正在访问 DDAI 注册页面...")
            
            # 尝试多种加载策略
            await page.goto("https://app.ddai.space/register", wait_until="load", timeout=30000)
            print("✅ 页面基础加载完成")
            
            # 等待动态内容加载
            print("⏳ 等待动态内容加载...")
            await page.wait_for_timeout(5000)
            
            # 尝试等待 Turnstile 相关元素
            try:
                await page.wait_for_selector('[data-sitekey], .cf-turnstile, [class*="turnstile"]', timeout=10000)
                print("✅ 找到 Turnstile 相关元素")
            except:
                print("⚠️  未找到明显的 Turnstile 元素，继续分析...")
            
            print("\n🔍 分析方法1: 查找页面元素")
            print("-" * 40)
            
            # 方法1: 查找所有可能的元素
            elements = await page.query_selector_all('*')
            turnstile_elements = []
            
            for element in elements[:100]:  # 限制检查前100个元素避免太慢
                try:
                    tag_name = await element.evaluate('el => el.tagName')
                    class_name = await element.get_attribute('class') or ''
                    data_sitekey = await element.get_attribute('data-sitekey')
                    
                    if any(keyword in class_name.lower() for keyword in ['turnstile', 'cloudflare', 'captcha']) or data_sitekey:
                        element_info = {
                            'tag': tag_name,
                            'class': class_name,
                            'data-sitekey': data_sitekey,
                        }
                        turnstile_elements.append(element_info)
                        print(f"🎯 找到相关元素: {element_info}")
                except:
                    continue
            
            print(f"\n📊 找到 {len(turnstile_elements)} 个相关元素")
            
            print("\n🔍 分析方法2: 页面源码分析")
            print("-" * 40)
            
            # 方法2: 分析页面源码
            content = await page.content()
            
            # 搜索各种可能的 site_key 模式
            patterns = [
                r'data-sitekey["\s]*=["\s]*["\']([^"\']+)["\']',
                r'"sitekey"["\s]*:["\s]*["\']([^"\']+)["\']',
                r'"siteKey"["\s]*:["\s]*["\']([^"\']+)["\']',
                r'sitekey["\s]*=["\s]*["\']([^"\']+)["\']',
                r'(0x4[A-Za-z0-9_-]{15,})',
                r'turnstile.*?["\']([0-9a-fA-F-]{20,})["\']',
            ]
            
            found_keys = set()
            for pattern in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    if len(match) > 10:  # 过滤太短的匹配
                        found_keys.add(match)
                        print(f"🔑 通过正则找到可能的 key: {match}")
            
            print(f"\n📊 通过正则表达式找到 {len(found_keys)} 个可能的 key")
            
            print("\n🔍 分析方法3: JavaScript 执行")
            print("-" * 40)
            
            # 方法3: 执行 JavaScript 查找
            js_result = await page.evaluate("""
                () => {
                    const results = [];
                    
                    // 查找所有可能的选择器
                    const selectors = [
                        '[data-sitekey]',
                        '.cf-turnstile',
                        '[class*="turnstile"]',
                        '[class*="cloudflare"]',
                        '[id*="turnstile"]',
                        '[id*="cloudflare"]'
                    ];
                    
                    selectors.forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => {
                            const info = {
                                selector: selector,
                                tagName: el.tagName,
                                className: el.className,
                                id: el.id,
                                dataSitekey: el.getAttribute('data-sitekey'),
                                innerHTML: el.innerHTML.substring(0, 200)
                            };
                            results.push(info);
                        });
                    });
                    
                    // 检查全局变量
                    const globalVars = [];
                    if (typeof window.turnstile !== 'undefined') {
                        globalVars.push('window.turnstile exists');
                    }
                    if (typeof window.cloudflare !== 'undefined') {
                        globalVars.push('window.cloudflare exists');
                    }
                    
                    return {
                        elements: results,
                        globalVars: globalVars,
                        scripts: Array.from(document.scripts).map(s => s.src).filter(src => 
                            src && (src.includes('turnstile') || src.includes('cloudflare'))
                        )
                    };
                }
            """)
            
            print(f"🔧 JavaScript 分析结果:")
            print(f"   - 找到 {len(js_result['elements'])} 个相关元素")
            print(f"   - 全局变量: {js_result['globalVars']}")
            print(f"   - 相关脚本: {len(js_result['scripts'])} 个")
            
            for element in js_result['elements']:
                if element['dataSitekey']:
                    print(f"🎯 JavaScript 找到 site_key: {element['dataSitekey']}")
            
            print("\n🔍 分析方法4: 网络请求分析")
            print("-" * 40)
            
            print(f"📡 捕获到 {len(requests_log)} 个相关网络请求")
            
            # 分析网络请求中的 site_key
            for req in requests_log:
                url = req['url']
                if 'sitekey' in url or 'site-key' in url:
                    # 尝试从URL中提取 site_key
                    key_match = re.search(r'(?:sitekey|site-key)[=:]([^&\s]+)', url)
                    if key_match:
                        print(f"🔑 从网络请求找到 site_key: {key_match.group(1)}")
            
            # 汇总所有找到的可能的 site_key
            all_possible_keys = list(found_keys)
            for element in js_result['elements']:
                if element['dataSitekey']:
                    all_possible_keys.append(element['dataSitekey'])
            
            # 去重
            unique_keys = list(set(all_possible_keys))
            
            print("\n" + "=" * 60)
            print("📋 分析结果汇总")
            print("=" * 60)
            
            if unique_keys:
                print(f"🎉 找到 {len(unique_keys)} 个可能的 site_key:")
                for i, key in enumerate(unique_keys, 1):
                    print(f"   {i}. {key}")
                
                # 保存结果
                result = {
                    'site_keys': unique_keys,
                    'elements': js_result['elements'],
                    'network_requests': requests_log,
                    'analysis_time': '2025-07-19'
                }
                
                with open('ddai_analysis_result.json', 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                
                print(f"📁 详细结果已保存到: ddai_analysis_result.json")
                
                return unique_keys
            else:
                print("❌ 未找到明确的 site_key")
                print("💡 可能的原因:")
                print("   1. 网站使用了高级的反检测技术")
                print("   2. Turnstile 是通过复杂的 JavaScript 动态加载的")
                print("   3. 需要特定的用户交互才会加载")
                
                return []
                
        except Exception as e:
            print(f"❌ 分析过程中出错: {e}")
            return []
        finally:
            print("\n⏳ 保持浏览器打开30秒，您可以手动检查...")
            await page.wait_for_timeout(30000)
            await browser.close()

async def main():
    """主函数"""
    print("🎯 DDAI 网站深度分析工具")
    print("这个工具将使用多种方法查找 site_key")
    print("=" * 60)
    
    possible_keys = await deep_analyze_ddai()
    
    if possible_keys:
        print(f"\n🎉 分析完成! 找到 {len(possible_keys)} 个可能的 site_key")
        print("接下来您可以使用这些 key 测试:")
        print()
        for i, key in enumerate(possible_keys, 1):
            print(f"python3 ddai_final_solver.py")
            print(f"# 然后输入: {key}")
            if i < len(possible_keys):
                print()
    else:
        print("\n❌ 自动分析未找到 site_key")
        print("💡 建议手动方法:")
        print("1. 在刚才打开的浏览器中，按 F12")
        print("2. 切换到 Network 标签")
        print("3. 刷新页面，查找包含 'turnstile' 的请求")
        print("4. 在请求URL或响应中查找 site_key")

if __name__ == "__main__":
    asyncio.run(main())
