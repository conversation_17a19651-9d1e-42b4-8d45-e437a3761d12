#!/usr/bin/env python3
"""
获取全新 Turnstile Token 并注册
每次都获取新的 Token，而不是重复使用旧的
"""

import json
import time
import random
import string
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def generate_random_credentials():
    """生成随机注册信息"""
    username = 'user_' + ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    email = username + '@gmail.com'
    password = ''.join(random.choices(string.ascii_letters + string.digits, k=12)) + '!A1'
    return email, username, password

def setup_chrome_driver():
    """设置 Chrome 驱动"""
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    return driver

def wait_for_fresh_token(driver, max_wait=60):
    """等待获取全新的 Turnstile Token"""
    print("⏳ 等待 Turnstile 生成全新 Token...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        try:
            # 检查 Token 字段
            token_field = driver.find_element(By.CSS_SELECTOR, '[name="cf-turnstile-response"]')
            current_token = token_field.get_attribute('value')
            
            if current_token and len(current_token) > 100:
                print(f"✅ 获取到全新 Token: {current_token[:50]}...")
                print(f"📏 Token 长度: {len(current_token)}")
                return current_token
            
            time.sleep(2)
            
        except Exception as e:
            print(f"⚠️  检查 Token 时出错: {e}")
            time.sleep(2)
    
    print("❌ 等待 Token 超时")
    return None

def fresh_token_register():
    """使用全新 Token 注册"""
    print("🎯 DDAI 全新 Token 注册")
    print("=" * 60)
    
    # 生成随机注册信息
    email, username, password = generate_random_credentials()
    print(f"📧 邮箱: {email}")
    print(f"👤 用户名: {username}")
    print(f"🔐 密码: {password}")
    print()
    
    # 设置浏览器
    print("🌐 启动浏览器...")
    driver = setup_chrome_driver()
    
    try:
        print("📍 打开注册页面...")
        driver.get("https://app.ddai.space/register")
        
        print("⏳ 等待页面完全加载...")
        time.sleep(10)  # 给更多时间让 Turnstile 加载
        
        # 等待获取全新 Token
        fresh_token = wait_for_fresh_token(driver, max_wait=90)
        
        if not fresh_token:
            print("❌ 无法获取全新 Token")
            return False
        
        print("📝 开始填写注册信息...")
        
        # 填写邮箱
        try:
            email_field = WebDriverWait(driver, 15).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '[name="email"]'))
            )
            email_field.clear()
            time.sleep(1)
            email_field.send_keys(email)
            print(f"✅ 邮箱已填写: {email}")
        except Exception as e:
            print(f"❌ 填写邮箱失败: {e}")
            return False
        
        # 填写用户名
        try:
            username_field = driver.find_element(By.CSS_SELECTOR, '[name="username"]')
            username_field.clear()
            time.sleep(1)
            username_field.send_keys(username)
            print(f"✅ 用户名已填写: {username}")
        except Exception as e:
            print(f"❌ 填写用户名失败: {e}")
            return False
        
        # 填写密码
        try:
            password_field = driver.find_element(By.CSS_SELECTOR, '[name="password"]')
            password_field.clear()
            time.sleep(1)
            password_field.send_keys(password)
            print(f"✅ 密码已填写: {password}")
        except Exception as e:
            print(f"❌ 填写密码失败: {e}")
            return False
        
        # 填写确认密码
        try:
            confirm_password_field = driver.find_element(By.CSS_SELECTOR, '[name="confirmPassword"]')
            confirm_password_field.clear()
            time.sleep(1)
            confirm_password_field.send_keys(password)
            print(f"✅ 确认密码已填写: {password}")
        except Exception as e:
            print(f"❌ 填写确认密码失败: {e}")
            return False
        
        # 再次确认 Token 仍然有效
        print("🔍 最终确认 Token 状态...")
        final_token = driver.find_element(By.CSS_SELECTOR, '[name="cf-turnstile-response"]').get_attribute('value')
        
        if final_token != fresh_token:
            print("⚠️  Token 发生了变化，使用最新的 Token")
            fresh_token = final_token
        
        print(f"🎯 使用 Token: {fresh_token[:50]}...")
        
        # 等待一下确保所有字段都已填写
        time.sleep(3)
        
        print("🚀 提交注册...")
        
        # 提交表单
        try:
            driver.execute_script("document.querySelector('form').submit();")
            print("✅ 表单已提交")
        except Exception as e:
            print(f"❌ 表单提交失败: {e}")
            return False
        
        # 等待结果
        print("⏳ 等待注册结果（45秒）...")
        
        start_time = time.time()
        while time.time() - start_time < 45:
            current_url = driver.current_url
            page_title = driver.title
            
            print(f"📍 当前 URL: {current_url}")
            
            # 检查是否跳转到其他页面
            if current_url != 'https://app.ddai.space/register' and 'register' not in current_url:
                print("🎉 URL 已跳转，可能注册成功!")
                
                # 保存成功的注册信息
                success_data = {
                    'email': email,
                    'username': username,
                    'password': password,
                    'token': fresh_token,
                    'final_url': current_url,
                    'registration_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'success': True
                }
                
                with open(f'fresh_token_success_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                    json.dump(success_data, f, indent=2, ensure_ascii=False)
                
                print("📁 注册信息已保存")
                return True
            
            # 检查页面内容
            try:
                page_text = driver.execute_script("return document.body.innerText;")
                
                if any(word in page_text.lower() for word in ['success', 'welcome', 'dashboard', 'congratulations']):
                    print("🎉 检测到成功关键词!")
                    return True
                elif any(word in page_text.lower() for word in ['error', 'failed', 'invalid', 'expired']):
                    print("❌ 检测到错误关键词!")
                    print(f"页面内容片段: {page_text[:300]}...")
                    return False
            except:
                pass
            
            time.sleep(3)
        
        print("⏰ 等待超时")
        return False
    
    except Exception as e:
        print(f"❌ 注册过程出错: {e}")
        return False
    
    finally:
        print("⏳ 等待 20 秒后关闭浏览器...")
        time.sleep(20)
        driver.quit()

def main():
    """主函数"""
    success = fresh_token_register()
    
    if success:
        print()
        print("=" * 60)
        print("🎉 使用全新 Token 注册可能成功!")
        print("=" * 60)
    else:
        print()
        print("=" * 60)
        print("❌ 注册失败")
        print("=" * 60)

if __name__ == "__main__":
    main()
