#!/usr/bin/env python3
"""
专门针对 DDAI 网站的 Turnstile 验证码绕过工具
优化了等待时间和错误处理
"""

from botasaurus.browser import browser, Driver
import time
import json
from datetime import datetime

@browser(
    # 针对 DDAI 网站的优化配置
    cache=False,
    max_retry=3,
    reuse_driver=False,
    headless=False,  # 显示浏览器窗口以便观察
    wait_for_complete_page_load=False,  # 不等待所有资源加载完成
    block_images_and_css=False,  # 允许加载图片和CSS
)
def solve_ddai_turnstile(driver: Driver, data):
    """
    专门绕过 DDAI 网站的 Turnstile 验证码
    """
    url = "https://app.ddai.space/register"
    
    print(f"🚀 开始处理 DDAI 注册页面: {url}")
    print("🔄 正在启动浏览器...")
    
    try:
        # 第一步：使用 google_get 访问，启用 Cloudflare 绕过
        print("🛡️ 使用 Cloudflare 绕过模式访问网站...")
        driver.google_get(url, bypass_cloudflare=True)
        
        # 第二步：等待页面初始加载
        print("⏳ 等待页面初始加载...")
        time.sleep(3)
        
        # 第三步：检查当前状态
        current_url = driver.current_url
        print(f"📍 当前URL: {current_url}")
        
        try:
            title = driver.title
            print(f"📄 页面标题: {title}")
        except:
            title = "无法获取标题"
        
        # 第四步：等待 Turnstile 验证码处理
        print("🔐 等待 Turnstile 验证码处理...")
        max_wait_time = 30  # 最多等待30秒
        wait_interval = 2   # 每2秒检查一次
        
        for i in range(0, max_wait_time, wait_interval):
            try:
                # 检查是否有 Turnstile 验证码框
                turnstile_elements = driver.select_all('[data-sitekey]')
                if turnstile_elements:
                    print(f"🔍 发现 Turnstile 验证码，等待自动处理... ({i}s/{max_wait_time}s)")
                else:
                    print(f"✅ 未发现 Turnstile 验证码或已处理完成 ({i}s)")
                    break
                
                # 检查是否有注册表单
                register_form = driver.select_all('input[type="email"], input[placeholder*="email"]')
                if register_form:
                    print("📝 发现注册表单，验证码可能已通过")
                    break
                    
                time.sleep(wait_interval)
                
            except Exception as e:
                print(f"⚠️ 检查过程中出现异常: {e}")
                time.sleep(wait_interval)
        
        # 第五步：最终状态检查
        print("🔍 进行最终状态检查...")
        final_url = driver.current_url
        final_title = driver.title if hasattr(driver, 'title') else "无法获取标题"
        
        # 检查页面内容
        try:
            # 尝试获取页面的关键元素
            email_input = driver.select_all('input[type="email"], input[placeholder*="email"]')
            register_button = driver.select_all('button[type="submit"], button:contains("Register"), button:contains("注册")')
            turnstile_frame = driver.select_all('iframe[src*="turnstile"], [data-sitekey]')
            
            has_email_input = len(email_input) > 0
            has_register_button = len(register_button) > 0
            has_turnstile = len(turnstile_frame) > 0
            
            print(f"📧 邮箱输入框: {'✅ 找到' if has_email_input else '❌ 未找到'}")
            print(f"🔘 注册按钮: {'✅ 找到' if has_register_button else '❌ 未找到'}")
            print(f"🔐 Turnstile 验证码: {'⚠️ 仍存在' if has_turnstile else '✅ 已处理'}")
            
        except Exception as e:
            print(f"⚠️ 页面元素检查失败: {e}")
            has_email_input = False
            has_register_button = False
            has_turnstile = True
        
        # 判断成功标准
        success = (
            "register" in final_url.lower() and
            has_email_input and
            not has_turnstile
        )
        
        # 尝试获取页面预览
        try:
            page_preview = driver.get_text('body')[:500] if driver.get_text('body') else "无法获取页面内容"
        except:
            page_preview = "无法获取页面内容"
        
        result = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "original_url": url,
            "final_url": final_url,
            "title": final_title,
            "success": success,
            "has_email_input": has_email_input,
            "has_register_button": has_register_button,
            "has_turnstile": has_turnstile,
            "page_preview": page_preview,
            "status": "成功绕过 Turnstile 验证" if success else "验证码处理中或页面加载异常"
        }
        
        if success:
            print("🎉 成功绕过 DDAI 网站的 Cloudflare Turnstile 验证！")
            print("📝 注册表单已可用，可以进行注册操作")
        else:
            print("⚠️ 验证码可能仍在处理中，或页面存在其他问题")
            print("💡 建议：检查浏览器窗口，可能需要手动完成某些步骤")
        
        # 保持浏览器打开一段时间供用户查看
        print("🔍 保持浏览器打开10秒供您查看结果...")
        time.sleep(10)
        
        return result
        
    except Exception as e:
        error_result = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "original_url": url,
            "error": str(e),
            "success": False,
            "status": f"访问失败: {str(e)}"
        }
        print(f"❌ 访问失败: {e}")
        return error_result

def main():
    """主函数"""
    print("=" * 60)
    print("🤖 DDAI 网站 Turnstile 验证码绕过工具")
    print("=" * 60)
    print("🎯 目标网站: https://app.ddai.space/register")
    print("🔐 功能: 自动绕过 Cloudflare Turnstile 验证码")
    print()
    
    print("🚀 开始处理...")
    result = solve_ddai_turnstile()
    
    print("\n" + "=" * 60)
    print("📊 处理结果:")
    print("=" * 60)
    print(f"🕐 处理时间: {result.get('timestamp', 'N/A')}")
    print(f"🌐 最终网址: {result.get('final_url', 'N/A')}")
    print(f"📄 页面标题: {result.get('title', 'N/A')}")
    print(f"📧 邮箱输入框: {'✅ 可用' if result.get('has_email_input') else '❌ 不可用'}")
    print(f"🔘 注册按钮: {'✅ 可用' if result.get('has_register_button') else '❌ 不可用'}")
    print(f"🔐 Turnstile 状态: {'✅ 已绕过' if not result.get('has_turnstile') else '⚠️ 仍存在'}")
    print(f"✅ 整体状态: {'🎉 成功' if result.get('success') else '⚠️ 需要检查'}")
    print(f"📝 状态说明: {result.get('status', 'N/A')}")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"output/ddai_turnstile_result_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"💾 详细结果已保存到: {filename}")
    except Exception as e:
        print(f"⚠️ 保存结果失败: {e}")
    
    print("\n🎯 使用建议:")
    if result.get('success'):
        print("✅ 验证码已成功绕过，您现在可以:")
        print("   1. 在浏览器中填写注册信息")
        print("   2. 完成注册流程")
    else:
        print("⚠️ 如果验证码仍然存在，您可以:")
        print("   1. 等待几秒钟让验证码自动完成")
        print("   2. 刷新页面重试")
        print("   3. 检查网络连接")
    
    print("\n👋 处理完成！")

if __name__ == "__main__":
    main()
