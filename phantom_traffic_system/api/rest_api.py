"""
幻影流量系统 - REST API
提供HTTP REST接口
"""

import json
from typing import Dict, Any, Optional
from datetime import datetime
from aiohttp import web, ClientSession
import aiohttp_cors

from ..core.logger import phantom_logger
from ..core.exceptions import PhantomTrafficError

logger = phantom_logger.get_logger("rest_api")


class RestAPI:
    """REST API处理器"""
    
    def __init__(self, phantom_system):
        """
        初始化REST API
        
        Args:
            phantom_system: 幻影流量系统实例
        """
        self.phantom_system = phantom_system
        self.app = web.Application()
        
        # 设置CORS
        self.cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })
        
        # 注册路由
        self._setup_routes()
        
        logger.debug("REST API初始化完成")
    
    def _setup_routes(self):
        """设置API路由"""
        # 系统管理
        self.app.router.add_get('/api/status', self.get_system_status)
        self.app.router.add_get('/api/health', self.health_check)
        self.app.router.add_post('/api/start', self.start_system)
        self.app.router.add_post('/api/stop', self.stop_system)
        
        # 流量生成
        self.app.router.add_post('/api/visit', self.execute_visit)
        self.app.router.add_post('/api/session', self.create_session)
        self.app.router.add_get('/api/sessions', self.get_sessions)
        
        # 配置管理
        self.app.router.add_get('/api/config', self.get_config)
        self.app.router.add_post('/api/config', self.update_config)
        self.app.router.add_post('/api/targets', self.add_target_url)
        self.app.router.add_delete('/api/targets', self.remove_target_url)
        
        # 统计信息
        self.app.router.add_get('/api/stats', self.get_statistics)
        self.app.router.add_get('/api/stats/proxy', self.get_proxy_stats)
        self.app.router.add_get('/api/stats/browser', self.get_browser_stats)
        self.app.router.add_get('/api/stats/scheduler', self.get_scheduler_stats)
        
        # 为所有路由添加CORS
        for route in list(self.app.router.routes()):
            self.cors.add(route)
    
    async def get_system_status(self, request):
        """获取系统状态"""
        try:
            status = self.phantom_system.get_system_status()
            return web.json_response({
                "success": True,
                "data": status
            })
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def health_check(self, request):
        """健康检查"""
        try:
            health = await self.phantom_system.health_check()
            status_code = 200 if health["overall_health"] == "healthy" else 503
            
            return web.json_response({
                "success": True,
                "data": health
            }, status=status_code)
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def start_system(self, request):
        """启动系统"""
        try:
            await self.phantom_system.start()
            return web.json_response({
                "success": True,
                "message": "系统启动成功"
            })
        except Exception as e:
            logger.error(f"启动系统失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def stop_system(self, request):
        """停止系统"""
        try:
            await self.phantom_system.stop()
            return web.json_response({
                "success": True,
                "message": "系统停止成功"
            })
        except Exception as e:
            logger.error(f"停止系统失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def execute_visit(self, request):
        """执行单次访问"""
        try:
            data = await request.json()
            target_url = data.get("target_url")
            
            if not target_url:
                return web.json_response({
                    "success": False,
                    "error": "缺少target_url参数"
                }, status=400)
            
            visit_config = data.get("config", {})
            result = await self.phantom_system.execute_single_visit(target_url, visit_config)
            
            return web.json_response({
                "success": True,
                "data": result
            })
            
        except Exception as e:
            logger.error(f"执行访问失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def create_session(self, request):
        """创建流量会话"""
        try:
            data = await request.json()
            target_url = data.get("target_url")
            
            if not target_url:
                return web.json_response({
                    "success": False,
                    "error": "缺少target_url参数"
                }, status=400)
            
            session_config = data.get("config", {})
            session_id = await self.phantom_system.create_traffic_session(target_url, session_config)
            
            return web.json_response({
                "success": True,
                "data": {
                    "session_id": session_id,
                    "target_url": target_url
                }
            })
            
        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_sessions(self, request):
        """获取活跃会话"""
        try:
            sessions = self.phantom_system.get_active_sessions()
            return web.json_response({
                "success": True,
                "data": {
                    "sessions": sessions,
                    "count": len(sessions)
                }
            })
        except Exception as e:
            logger.error(f"获取会话失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_config(self, request):
        """获取配置"""
        try:
            config = self.phantom_system.config_manager.get_config()
            return web.json_response({
                "success": True,
                "data": config
            })
        except Exception as e:
            logger.error(f"获取配置失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def update_config(self, request):
        """更新配置"""
        try:
            data = await request.json()
            # 这里应该实现配置更新逻辑
            return web.json_response({
                "success": True,
                "message": "配置更新成功"
            })
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def add_target_url(self, request):
        """添加目标URL"""
        try:
            data = await request.json()
            url = data.get("url")
            
            if not url:
                return web.json_response({
                    "success": False,
                    "error": "缺少url参数"
                }, status=400)
            
            self.phantom_system.add_target_url(url)
            
            return web.json_response({
                "success": True,
                "message": f"目标URL已添加: {url}"
            })
            
        except Exception as e:
            logger.error(f"添加目标URL失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def remove_target_url(self, request):
        """移除目标URL"""
        try:
            data = await request.json()
            url = data.get("url")
            
            if not url:
                return web.json_response({
                    "success": False,
                    "error": "缺少url参数"
                }, status=400)
            
            self.phantom_system.remove_target_url(url)
            
            return web.json_response({
                "success": True,
                "message": f"目标URL已移除: {url}"
            })
            
        except Exception as e:
            logger.error(f"移除目标URL失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_statistics(self, request):
        """获取统计信息"""
        try:
            stats = self.phantom_system.get_system_status()
            return web.json_response({
                "success": True,
                "data": stats
            })
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_proxy_stats(self, request):
        """获取代理统计"""
        try:
            stats = self.phantom_system.proxy_manager.get_statistics()
            return web.json_response({
                "success": True,
                "data": stats
            })
        except Exception as e:
            logger.error(f"获取代理统计失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_browser_stats(self, request):
        """获取浏览器统计"""
        try:
            if not self.phantom_system.is_running:
                return web.json_response({
                    "success": False,
                    "error": "系统未运行"
                }, status=503)
            
            stats = self.phantom_system.browser_controller.get_controller_statistics()
            return web.json_response({
                "success": True,
                "data": stats
            })
        except Exception as e:
            logger.error(f"获取浏览器统计失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_scheduler_stats(self, request):
        """获取调度器统计"""
        try:
            if not self.phantom_system.is_running:
                return web.json_response({
                    "success": False,
                    "error": "系统未运行"
                }, status=503)
            
            stats = self.phantom_system.session_scheduler.get_comprehensive_statistics()
            return web.json_response({
                "success": True,
                "data": stats
            })
        except Exception as e:
            logger.error(f"获取调度器统计失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
