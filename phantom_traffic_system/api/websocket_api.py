"""
幻影流量系统 - WebSocket API
提供实时通信接口
"""

import json
import asyncio
from typing import Dict, Set, Any
from datetime import datetime
from aiohttp import web, WSMsgType
import weakref

from ..core.logger import phantom_logger

logger = phantom_logger.get_logger("websocket_api")


class WebSocketAPI:
    """WebSocket API处理器"""
    
    def __init__(self, phantom_system):
        """
        初始化WebSocket API
        
        Args:
            phantom_system: 幻影流量系统实例
        """
        self.phantom_system = phantom_system
        self.connections: Set[web.WebSocketResponse] = set()
        self.subscriptions: Dict[str, Set[web.WebSocketResponse]] = {}
        
        # 启动状态广播任务
        self.broadcast_task = None
        
        logger.debug("WebSocket API初始化完成")
    
    async def websocket_handler(self, request):
        """WebSocket连接处理器"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        # 添加到连接集合
        self.connections.add(ws)
        client_ip = request.remote
        
        logger.info(f"WebSocket客户端连接 | IP: {client_ip}")
        
        # 发送欢迎消息
        await self._send_message(ws, {
            "type": "welcome",
            "message": "连接到幻影流量系统",
            "timestamp": datetime.now().isoformat()
        })
        
        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    try:
                        data = json.loads(msg.data)
                        await self._handle_message(ws, data)
                    except json.JSONDecodeError:
                        await self._send_error(ws, "无效的JSON格式")
                    except Exception as e:
                        logger.error(f"处理WebSocket消息失败: {e}")
                        await self._send_error(ws, str(e))
                        
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f"WebSocket错误: {ws.exception()}")
                    break
                    
        except Exception as e:
            logger.error(f"WebSocket连接异常: {e}")
        
        finally:
            # 清理连接
            self.connections.discard(ws)
            self._remove_from_subscriptions(ws)
            logger.info(f"WebSocket客户端断开 | IP: {client_ip}")
        
        return ws
    
    async def _handle_message(self, ws: web.WebSocketResponse, data: Dict[str, Any]):
        """处理WebSocket消息"""
        message_type = data.get("type")
        
        if message_type == "subscribe":
            await self._handle_subscribe(ws, data)
        elif message_type == "unsubscribe":
            await self._handle_unsubscribe(ws, data)
        elif message_type == "get_status":
            await self._handle_get_status(ws)
        elif message_type == "execute_visit":
            await self._handle_execute_visit(ws, data)
        elif message_type == "ping":
            await self._send_message(ws, {"type": "pong", "timestamp": datetime.now().isoformat()})
        else:
            await self._send_error(ws, f"未知的消息类型: {message_type}")
    
    async def _handle_subscribe(self, ws: web.WebSocketResponse, data: Dict[str, Any]):
        """处理订阅请求"""
        topics = data.get("topics", [])
        
        for topic in topics:
            if topic not in self.subscriptions:
                self.subscriptions[topic] = set()
            self.subscriptions[topic].add(ws)
        
        await self._send_message(ws, {
            "type": "subscribed",
            "topics": topics,
            "message": f"已订阅 {len(topics)} 个主题"
        })
        
        logger.debug(f"客户端订阅主题: {topics}")
    
    async def _handle_unsubscribe(self, ws: web.WebSocketResponse, data: Dict[str, Any]):
        """处理取消订阅请求"""
        topics = data.get("topics", [])
        
        for topic in topics:
            if topic in self.subscriptions:
                self.subscriptions[topic].discard(ws)
                if not self.subscriptions[topic]:
                    del self.subscriptions[topic]
        
        await self._send_message(ws, {
            "type": "unsubscribed",
            "topics": topics,
            "message": f"已取消订阅 {len(topics)} 个主题"
        })
        
        logger.debug(f"客户端取消订阅主题: {topics}")
    
    async def _handle_get_status(self, ws: web.WebSocketResponse):
        """处理获取状态请求"""
        try:
            status = self.phantom_system.get_system_status()
            await self._send_message(ws, {
                "type": "status",
                "data": status
            })
        except Exception as e:
            await self._send_error(ws, f"获取状态失败: {e}")
    
    async def _handle_execute_visit(self, ws: web.WebSocketResponse, data: Dict[str, Any]):
        """处理执行访问请求"""
        try:
            target_url = data.get("target_url")
            if not target_url:
                await self._send_error(ws, "缺少target_url参数")
                return
            
            # 发送开始消息
            await self._send_message(ws, {
                "type": "visit_started",
                "target_url": target_url,
                "timestamp": datetime.now().isoformat()
            })
            
            # 执行访问
            visit_config = data.get("config", {})
            result = await self.phantom_system.execute_single_visit(target_url, visit_config)
            
            # 发送结果
            await self._send_message(ws, {
                "type": "visit_completed",
                "data": result
            })
            
        except Exception as e:
            await self._send_error(ws, f"执行访问失败: {e}")
    
    async def _send_message(self, ws: web.WebSocketResponse, message: Dict[str, Any]):
        """发送消息到WebSocket客户端"""
        try:
            if ws.closed:
                return
            
            await ws.send_str(json.dumps(message, ensure_ascii=False))
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {e}")
    
    async def _send_error(self, ws: web.WebSocketResponse, error_message: str):
        """发送错误消息"""
        await self._send_message(ws, {
            "type": "error",
            "error": error_message,
            "timestamp": datetime.now().isoformat()
        })
    
    def _remove_from_subscriptions(self, ws: web.WebSocketResponse):
        """从所有订阅中移除WebSocket连接"""
        for topic, subscribers in list(self.subscriptions.items()):
            subscribers.discard(ws)
            if not subscribers:
                del self.subscriptions[topic]
    
    async def broadcast_to_topic(self, topic: str, message: Dict[str, Any]):
        """向特定主题的订阅者广播消息"""
        if topic not in self.subscriptions:
            return
        
        message["topic"] = topic
        message["timestamp"] = datetime.now().isoformat()
        
        # 获取订阅者副本，避免在迭代时修改
        subscribers = list(self.subscriptions[topic])
        
        for ws in subscribers:
            try:
                if not ws.closed:
                    await self._send_message(ws, message)
                else:
                    # 移除已关闭的连接
                    self.subscriptions[topic].discard(ws)
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                self.subscriptions[topic].discard(ws)
    
    async def broadcast_to_all(self, message: Dict[str, Any]):
        """向所有连接的客户端广播消息"""
        message["timestamp"] = datetime.now().isoformat()
        
        # 获取连接副本，避免在迭代时修改
        connections = list(self.connections)
        
        for ws in connections:
            try:
                if not ws.closed:
                    await self._send_message(ws, message)
                else:
                    # 移除已关闭的连接
                    self.connections.discard(ws)
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                self.connections.discard(ws)
    
    async def start_status_broadcast(self):
        """启动状态广播任务"""
        if self.broadcast_task and not self.broadcast_task.done():
            return
        
        self.broadcast_task = asyncio.create_task(self._status_broadcast_loop())
        logger.info("状态广播任务启动")
    
    async def stop_status_broadcast(self):
        """停止状态广播任务"""
        if self.broadcast_task and not self.broadcast_task.done():
            self.broadcast_task.cancel()
            try:
                await self.broadcast_task
            except asyncio.CancelledError:
                pass
        
        logger.info("状态广播任务停止")
    
    async def _status_broadcast_loop(self):
        """状态广播循环"""
        while True:
            try:
                # 每30秒广播一次系统状态
                await asyncio.sleep(30)
                
                if self.subscriptions.get("system_status"):
                    status = self.phantom_system.get_system_status()
                    await self.broadcast_to_topic("system_status", {
                        "type": "system_status_update",
                        "data": status
                    })
                
                # 广播统计信息
                if self.subscriptions.get("statistics"):
                    if self.phantom_system.is_running:
                        stats = {
                            "proxy": self.phantom_system.proxy_manager.get_statistics(),
                            "browser": self.phantom_system.browser_controller.get_controller_statistics(),
                            "scheduler": self.phantom_system.session_scheduler.get_comprehensive_statistics()
                        }
                        
                        await self.broadcast_to_topic("statistics", {
                            "type": "statistics_update",
                            "data": stats
                        })
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"状态广播异常: {e}")
                await asyncio.sleep(5)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        return {
            "total_connections": len(self.connections),
            "active_subscriptions": {
                topic: len(subscribers) 
                for topic, subscribers in self.subscriptions.items()
            },
            "total_subscriptions": sum(len(subscribers) for subscribers in self.subscriptions.values())
        }
