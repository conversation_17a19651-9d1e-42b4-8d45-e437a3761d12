"""
幻影流量系统 - API服务器
统一管理REST API和WebSocket API
"""

import asyncio
from typing import Dict, Any, Optional
from aiohttp import web
import ssl

from .rest_api import RestAPI
from .websocket_api import WebSocketAPI
from ..core.logger import phantom_logger

logger = phantom_logger.get_logger("api_server")


class APIServer:
    """API服务器"""
    
    def __init__(self, phantom_system, config: Dict[str, Any]):
        """
        初始化API服务器
        
        Args:
            phantom_system: 幻影流量系统实例
            config: API服务器配置
        """
        self.phantom_system = phantom_system
        self.config = config
        
        # 服务器配置
        self.host = config.get("host", "0.0.0.0")
        self.port = config.get("port", 8080)
        self.ssl_enabled = config.get("ssl_enabled", False)
        self.ssl_cert_path = config.get("ssl_cert_path")
        self.ssl_key_path = config.get("ssl_key_path")
        
        # 初始化API处理器
        self.rest_api = RestAPI(phantom_system)
        self.websocket_api = WebSocketAPI(phantom_system)
        
        # 创建应用
        self.app = self._create_app()
        
        # 服务器状态
        self.runner: Optional[web.AppRunner] = None
        self.site: Optional[web.TCPSite] = None
        self.is_running = False
        
        logger.info(f"API服务器初始化完成 | 地址: {self.host}:{self.port}")
    
    def _create_app(self) -> web.Application:
        """创建Web应用"""
        # 使用REST API的应用作为基础
        app = self.rest_api.app
        
        # 添加WebSocket路由
        app.router.add_get('/ws', self.websocket_api.websocket_handler)
        
        # 添加静态文件服务（如果需要）
        static_dir = self.config.get("static_dir")
        if static_dir:
            app.router.add_static('/', static_dir, name='static')
        
        # 添加中间件
        app.middlewares.append(self._logging_middleware)
        app.middlewares.append(self._error_middleware)
        
        return app
    
    @web.middleware
    async def _logging_middleware(self, request, handler):
        """日志中间件"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            response = await handler(request)
            process_time = asyncio.get_event_loop().time() - start_time
            
            logger.debug(
                f"{request.method} {request.path} - "
                f"{response.status} - {process_time:.3f}s"
            )
            
            return response
            
        except Exception as e:
            process_time = asyncio.get_event_loop().time() - start_time
            logger.error(
                f"{request.method} {request.path} - "
                f"ERROR: {e} - {process_time:.3f}s"
            )
            raise
    
    @web.middleware
    async def _error_middleware(self, request, handler):
        """错误处理中间件"""
        try:
            return await handler(request)
        except web.HTTPException:
            # HTTP异常直接抛出
            raise
        except Exception as e:
            logger.error(f"API请求处理异常: {e}")
            return web.json_response({
                "success": False,
                "error": "内部服务器错误",
                "details": str(e) if self.config.get("debug", False) else None
            }, status=500)
    
    async def start(self):
        """启动API服务器"""
        if self.is_running:
            logger.warning("API服务器已在运行")
            return
        
        try:
            logger.info("正在启动API服务器...")
            
            # 创建应用运行器
            self.runner = web.AppRunner(self.app)
            await self.runner.setup()
            
            # 配置SSL（如果启用）
            ssl_context = None
            if self.ssl_enabled:
                ssl_context = self._create_ssl_context()
            
            # 创建TCP站点
            self.site = web.TCPSite(
                self.runner, 
                self.host, 
                self.port,
                ssl_context=ssl_context
            )
            
            await self.site.start()
            
            # 启动WebSocket状态广播
            await self.websocket_api.start_status_broadcast()
            
            self.is_running = True
            
            protocol = "https" if self.ssl_enabled else "http"
            logger.info(f"API服务器启动成功 | {protocol}://{self.host}:{self.port}")
            
        except Exception as e:
            logger.error(f"API服务器启动失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止API服务器"""
        if not self.is_running:
            logger.warning("API服务器未在运行")
            return
        
        try:
            logger.info("正在停止API服务器...")
            
            # 停止WebSocket状态广播
            await self.websocket_api.stop_status_broadcast()
            
            # 关闭所有WebSocket连接
            await self._close_websocket_connections()
            
            # 停止站点
            if self.site:
                await self.site.stop()
                self.site = None
            
            # 清理运行器
            if self.runner:
                await self.runner.cleanup()
                self.runner = None
            
            self.is_running = False
            
            logger.info("API服务器停止完成")
            
        except Exception as e:
            logger.error(f"API服务器停止失败: {e}")
            raise
    
    def _create_ssl_context(self) -> ssl.SSLContext:
        """创建SSL上下文"""
        if not self.ssl_cert_path or not self.ssl_key_path:
            raise ValueError("SSL启用但未提供证书路径")
        
        ssl_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
        ssl_context.load_cert_chain(self.ssl_cert_path, self.ssl_key_path)
        
        return ssl_context
    
    async def _close_websocket_connections(self):
        """关闭所有WebSocket连接"""
        connections = list(self.websocket_api.connections)
        
        for ws in connections:
            try:
                if not ws.closed:
                    await ws.close()
            except Exception as e:
                logger.warning(f"关闭WebSocket连接失败: {e}")
        
        logger.debug(f"已关闭 {len(connections)} 个WebSocket连接")
    
    def get_server_info(self) -> Dict[str, Any]:
        """获取服务器信息"""
        return {
            "is_running": self.is_running,
            "host": self.host,
            "port": self.port,
            "ssl_enabled": self.ssl_enabled,
            "websocket_connections": self.websocket_api.get_connection_stats(),
            "endpoints": {
                "rest_api": f"{'https' if self.ssl_enabled else 'http'}://{self.host}:{self.port}/api",
                "websocket": f"{'wss' if self.ssl_enabled else 'ws'}://{self.host}:{self.port}/ws",
                "health_check": f"{'https' if self.ssl_enabled else 'http'}://{self.host}:{self.port}/api/health"
            }
        }
    
    async def broadcast_event(self, event_type: str, data: Any, topic: Optional[str] = None):
        """广播事件到WebSocket客户端"""
        message = {
            "type": event_type,
            "data": data
        }
        
        if topic:
            await self.websocket_api.broadcast_to_topic(topic, message)
        else:
            await self.websocket_api.broadcast_to_all(message)
    
    async def notify_visit_started(self, target_url: str, session_id: str):
        """通知访问开始"""
        await self.broadcast_event("visit_started", {
            "target_url": target_url,
            "session_id": session_id
        }, "visits")
    
    async def notify_visit_completed(self, result: Dict[str, Any]):
        """通知访问完成"""
        await self.broadcast_event("visit_completed", result, "visits")
    
    async def notify_system_status_change(self, status: str):
        """通知系统状态变化"""
        await self.broadcast_event("system_status_change", {
            "status": status
        }, "system_status")
    
    async def notify_error(self, error_type: str, error_message: str, details: Optional[Dict] = None):
        """通知错误"""
        await self.broadcast_event("error", {
            "error_type": error_type,
            "error_message": error_message,
            "details": details
        }, "errors")
    
    def add_custom_route(self, method: str, path: str, handler):
        """添加自定义路由"""
        if method.upper() == "GET":
            self.app.router.add_get(path, handler)
        elif method.upper() == "POST":
            self.app.router.add_post(path, handler)
        elif method.upper() == "PUT":
            self.app.router.add_put(path, handler)
        elif method.upper() == "DELETE":
            self.app.router.add_delete(path, handler)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")
        
        # 为新路由添加CORS
        route = list(self.app.router.routes())[-1]
        self.rest_api.cors.add(route)
        
        logger.debug(f"添加自定义路由: {method.upper()} {path}")
    
    async def health_check(self) -> Dict[str, Any]:
        """API服务器健康检查"""
        return {
            "api_server": {
                "status": "healthy" if self.is_running else "stopped",
                "connections": len(self.websocket_api.connections),
                "subscriptions": len(self.websocket_api.subscriptions)
            }
        }
