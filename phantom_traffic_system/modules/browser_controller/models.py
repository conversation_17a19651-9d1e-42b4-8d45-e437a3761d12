"""
浏览器控制模块 - 数据模型
定义浏览器实例、配置和交互结果等数据结构
"""

from enum import Enum
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import uuid


class BrowserType(Enum):
    """浏览器类型枚举"""
    CHROMIUM = "chromium"
    FIREFOX = "firefox"
    WEBKIT = "webkit"


class BrowserStatus(Enum):
    """浏览器状态枚举"""
    IDLE = "idle"
    BUSY = "busy"
    CRASHED = "crashed"
    CLOSED = "closed"


class InteractionType(Enum):
    """交互类型枚举"""
    NAVIGATION = "navigation"
    CLICK = "click"
    TYPE = "type"
    SCROLL = "scroll"
    HOVER = "hover"
    WAIT = "wait"
    SCREENSHOT = "screenshot"
    EVALUATE = "evaluate"


@dataclass
class BrowserConfig:
    """浏览器配置"""
    browser_type: BrowserType = BrowserType.CHROMIUM
    headless: bool = True
    
    # 窗口配置
    viewport_width: int = 1920
    viewport_height: int = 1080
    
    # 启动参数
    args: List[str] = field(default_factory=list)
    
    # 代理配置
    proxy_server: Optional[str] = None
    proxy_username: Optional[str] = None
    proxy_password: Optional[str] = None
    
    # 用户数据目录
    user_data_dir: Optional[str] = None
    
    # 扩展配置
    extensions: List[str] = field(default_factory=list)
    
    # 反检测配置
    stealth_mode: bool = True
    anti_detection_patches: List[str] = field(default_factory=lambda: [
        "webdriver", "chrome_runtime", "permissions", "plugins", "languages"
    ])
    
    # 超时配置
    page_load_timeout: float = 30.0
    navigation_timeout: float = 30.0
    
    # 资源过滤
    block_images: bool = False
    block_css: bool = False
    block_fonts: bool = False
    block_media: bool = False
    
    # 其他配置
    ignore_default_args: Optional[List[str]] = None
    disable_web_security: bool = False
    
    def to_launch_options(self) -> Dict[str, Any]:
        """转换为启动选项"""
        options = {
            "headless": self.headless,
            "args": self.args.copy()
        }

        # 添加忽略默认参数（如果指定）
        if self.ignore_default_args:
            options["ignore_default_args"] = self.ignore_default_args
        
        # 添加视口大小
        if not self.headless:
            options["args"].extend([
                f"--window-size={self.viewport_width},{self.viewport_height}"
            ])
        
        # 添加代理配置
        if self.proxy_server:
            options["args"].append(f"--proxy-server={self.proxy_server}")
        
        # 添加用户数据目录
        if self.user_data_dir:
            options["args"].append(f"--user-data-dir={self.user_data_dir}")
        
        # 添加反检测参数
        if self.stealth_mode:
            options["args"].extend([
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-blink-features=AutomationControlled",
                "--disable-features=VizDisplayCompositor"
            ])
        
        # 添加安全配置
        if self.disable_web_security:
            options["args"].extend([
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor"
            ])
        
        return options


@dataclass
class BrowserInstance:
    """浏览器实例"""
    instance_id: str
    browser_type: BrowserType
    config: BrowserConfig
    status: BrowserStatus = BrowserStatus.IDLE
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    last_used_at: Optional[datetime] = None
    
    # 运行时信息
    process_id: Optional[int] = None
    browser_context: Any = None
    page: Any = None
    
    # 统计信息
    total_navigations: int = 0
    total_interactions: int = 0
    total_errors: int = 0
    
    # 当前状态
    current_url: Optional[str] = None
    current_title: Optional[str] = None
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.instance_id:
            self.instance_id = str(uuid.uuid4())
    
    @property
    def is_available(self) -> bool:
        """检查实例是否可用"""
        return self.status == BrowserStatus.IDLE and self.browser_context is not None
    
    @property
    def uptime(self) -> float:
        """计算运行时间（秒）"""
        return (datetime.now() - self.created_at).total_seconds()
    
    def update_usage(self):
        """更新使用时间"""
        self.last_used_at = datetime.now()
    
    def record_navigation(self, url: str):
        """记录导航"""
        self.total_navigations += 1
        self.current_url = url
        self.update_usage()
    
    def record_interaction(self):
        """记录交互"""
        self.total_interactions += 1
        self.update_usage()
    
    def record_error(self):
        """记录错误"""
        self.total_errors += 1


@dataclass
class InteractionResult:
    """交互结果"""
    interaction_type: InteractionType
    success: bool
    duration: float
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 结果数据
    result_data: Any = None
    error_message: Optional[str] = None
    
    # 页面信息
    page_url: Optional[str] = None
    page_title: Optional[str] = None
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PageLoadResult:
    """页面加载结果"""
    url: str
    success: bool
    load_time: float
    final_url: str
    title: str
    status_code: Optional[int] = None
    error_message: Optional[str] = None
    
    # 页面信息
    content_length: int = 0
    resource_count: int = 0
    
    # 性能指标
    dom_content_loaded: Optional[float] = None
    load_event: Optional[float] = None
    first_paint: Optional[float] = None
    first_contentful_paint: Optional[float] = None
    
    # 元数据
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ElementInfo:
    """元素信息"""
    selector: str
    tag_name: str
    text_content: Optional[str] = None
    inner_html: Optional[str] = None
    
    # 位置信息
    x: Optional[float] = None
    y: Optional[float] = None
    width: Optional[float] = None
    height: Optional[float] = None
    
    # 属性
    attributes: Dict[str, str] = field(default_factory=dict)
    
    # 状态
    is_visible: bool = True
    is_enabled: bool = True
    is_clickable: bool = True


@dataclass
class ScreenshotResult:
    """截图结果"""
    success: bool
    file_path: Optional[str] = None
    image_data: Optional[bytes] = None
    error_message: Optional[str] = None
    
    # 截图信息
    width: int = 0
    height: int = 0
    format: str = "png"
    
    # 元数据
    timestamp: datetime = field(default_factory=datetime.now)
    page_url: Optional[str] = None


@dataclass
class BrowserPoolStats:
    """浏览器池统计信息"""
    total_instances: int = 0
    idle_instances: int = 0
    busy_instances: int = 0
    crashed_instances: int = 0
    
    # 使用统计
    total_navigations: int = 0
    total_interactions: int = 0
    total_errors: int = 0
    
    # 性能统计
    average_load_time: float = 0.0
    success_rate: float = 0.0
    
    # 时间信息
    pool_start_time: datetime = field(default_factory=datetime.now)
    last_update_time: datetime = field(default_factory=datetime.now)
    
    @property
    def utilization_rate(self) -> float:
        """计算利用率"""
        if self.total_instances == 0:
            return 0.0
        return (self.busy_instances / self.total_instances) * 100
    
    @property
    def uptime(self) -> float:
        """计算运行时间（秒）"""
        return (datetime.now() - self.pool_start_time).total_seconds()
    
    def update(self):
        """更新统计时间"""
        self.last_update_time = datetime.now()


@dataclass
class AntiDetectionConfig:
    """反检测配置"""
    # 基础反检测
    hide_webdriver: bool = True
    spoof_permissions: bool = True
    randomize_viewport: bool = True
    
    # 指纹伪装
    custom_user_agent: Optional[str] = None
    custom_viewport: Optional[Tuple[int, int]] = None
    custom_timezone: Optional[str] = None
    custom_language: Optional[str] = None
    
    # 行为模拟
    human_delays: bool = True
    mouse_movements: bool = True
    random_scrolling: bool = True
    
    # 高级反检测
    patch_chrome_runtime: bool = True
    patch_permissions_api: bool = True
    patch_plugins: bool = True
    patch_webgl: bool = True
    patch_canvas: bool = True
    
    # 网络层面
    rotate_user_agents: bool = True
    randomize_headers: bool = True
    
    # 其他
    disable_automation_indicators: bool = True
    hide_chrome_extensions: bool = True
