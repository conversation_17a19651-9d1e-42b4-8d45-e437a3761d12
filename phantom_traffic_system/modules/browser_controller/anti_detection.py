"""
浏览器控制模块 - 反检测管理器
实现各种反自动化检测技术
"""

import random
import json
from typing import Dict, List, Any, Optional

from .models import AntiDetectionConfig
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("anti_detection")


class AntiDetectionManager:
    """反检测管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化反检测管理器
        
        Args:
            config: 反检测配置
        """
        self.config = config
        self.anti_detection_config = AntiDetectionConfig(**config.get("anti_detection", {}))
        
        # 预定义的User-Agent列表
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.2 Safari/605.1.15"
        ]
        
        # 预定义的视口大小
        self.viewport_sizes = [
            (1920, 1080), (1366, 768), (1440, 900), (1536, 864),
            (1280, 720), (1600, 900), (1024, 768), (1280, 1024)
        ]
        
        logger.debug("反检测管理器初始化完成")
    
    def get_stealth_scripts(self) -> List[str]:
        """
        获取隐身脚本列表
        
        Returns:
            JavaScript隐身脚本列表
        """
        scripts = []
        
        if self.anti_detection_config.hide_webdriver:
            scripts.append(self._get_webdriver_hiding_script())
        
        if self.anti_detection_config.patch_chrome_runtime:
            scripts.append(self._get_chrome_runtime_patch())
        
        if self.anti_detection_config.patch_permissions_api:
            scripts.append(self._get_permissions_patch())
        
        if self.anti_detection_config.patch_plugins:
            scripts.append(self._get_plugins_patch())
        
        if self.anti_detection_config.patch_webgl:
            scripts.append(self._get_webgl_patch())
        
        if self.anti_detection_config.patch_canvas:
            scripts.append(self._get_canvas_patch())
        
        if self.anti_detection_config.disable_automation_indicators:
            scripts.append(self._get_automation_indicators_patch())
        
        return scripts
    
    def _get_webdriver_hiding_script(self) -> str:
        """获取隐藏webdriver的脚本"""
        return """
        // 隐藏webdriver属性
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // 删除webdriver属性
        delete navigator.__proto__.webdriver;
        delete navigator.webdriver;
        
        // 重写toString方法
        navigator.__defineGetter__('webdriver', () => undefined);
        """
    
    def _get_chrome_runtime_patch(self) -> str:
        """获取Chrome运行时补丁"""
        return """
        // 修复chrome.runtime泄露
        if (window.chrome && window.chrome.runtime) {
            Object.defineProperty(window.chrome.runtime, 'onConnect', {
                value: undefined,
                writable: false,
                configurable: false
            });
            
            Object.defineProperty(window.chrome.runtime, 'onMessage', {
                value: undefined,
                writable: false,
                configurable: false
            });
        }
        
        // 伪造chrome对象
        if (!window.chrome) {
            window.chrome = {
                runtime: {},
                loadTimes: function() {
                    return {
                        commitLoadTime: Date.now() / 1000 - Math.random(),
                        connectionInfo: 'http/1.1',
                        finishDocumentLoadTime: Date.now() / 1000 - Math.random(),
                        finishLoadTime: Date.now() / 1000 - Math.random(),
                        firstPaintAfterLoadTime: 0,
                        firstPaintTime: Date.now() / 1000 - Math.random(),
                        navigationType: 'Other',
                        npnNegotiatedProtocol: 'unknown',
                        requestTime: Date.now() / 1000 - Math.random(),
                        startLoadTime: Date.now() / 1000 - Math.random(),
                        wasAlternateProtocolAvailable: false,
                        wasFetchedViaSpdy: false,
                        wasNpnNegotiated: false
                    };
                },
                csi: function() {
                    return {
                        pageT: Date.now(),
                        tran: 15
                    };
                }
            };
        }
        """
    
    def _get_permissions_patch(self) -> str:
        """获取权限API补丁"""
        return """
        // 修复permissions API
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
        
        // 伪造权限状态
        const getParameter = WebGLRenderingContext.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            if (parameter === 37445) {
                return 'Intel Inc.';
            }
            if (parameter === 37446) {
                return 'Intel Iris OpenGL Engine';
            }
            return getParameter(parameter);
        };
        """
    
    def _get_plugins_patch(self) -> str:
        """获取插件补丁"""
        return """
        // 伪造插件信息
        Object.defineProperty(navigator, 'plugins', {
            get: () => [
                {
                    0: {
                        type: "application/x-google-chrome-pdf",
                        suffixes: "pdf",
                        description: "Portable Document Format",
                        enabledPlugin: Plugin
                    },
                    description: "Portable Document Format",
                    filename: "internal-pdf-viewer",
                    length: 1,
                    name: "Chrome PDF Plugin"
                },
                {
                    0: {
                        type: "application/pdf",
                        suffixes: "pdf", 
                        description: "Portable Document Format",
                        enabledPlugin: Plugin
                    },
                    description: "Portable Document Format",
                    filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                    length: 1,
                    name: "Chrome PDF Viewer"
                }
            ],
        });
        
        Object.defineProperty(navigator, 'mimeTypes', {
            get: () => [
                {
                    type: "application/pdf",
                    suffixes: "pdf",
                    description: "Portable Document Format",
                    enabledPlugin: {
                        name: "Chrome PDF Viewer",
                        filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai"
                    }
                }
            ],
        });
        """
    
    def _get_webgl_patch(self) -> str:
        """获取WebGL补丁"""
        return """
        // WebGL指纹伪装
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            // 伪造GPU信息
            if (parameter === 37445) {
                return 'Intel Inc.';
            }
            if (parameter === 37446) {
                return 'Intel Iris Pro OpenGL Engine';
            }
            return getParameter.call(this, parameter);
        };
        
        // 同样处理WebGL2
        if (typeof WebGL2RenderingContext !== 'undefined') {
            WebGL2RenderingContext.prototype.getParameter = WebGLRenderingContext.prototype.getParameter;
        }
        """
    
    def _get_canvas_patch(self) -> str:
        """获取Canvas补丁"""
        return """
        // Canvas指纹伪装
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
        
        HTMLCanvasElement.prototype.toDataURL = function(type, quality) {
            // 对小尺寸canvas添加噪音
            if (this.width < 300 && this.height < 300) {
                const context = this.getContext('2d');
                const imageData = context.getImageData(0, 0, this.width, this.height);
                const data = imageData.data;
                
                // 添加微小的随机噪音
                for (let i = 0; i < data.length; i += 4) {
                    if (Math.random() < 0.1) {
                        data[i] = Math.min(255, data[i] + Math.floor(Math.random() * 5) - 2);
                        data[i + 1] = Math.min(255, data[i + 1] + Math.floor(Math.random() * 5) - 2);
                        data[i + 2] = Math.min(255, data[i + 2] + Math.floor(Math.random() * 5) - 2);
                    }
                }
                
                context.putImageData(imageData, 0, 0);
            }
            
            return originalToDataURL.call(this, type, quality);
        };
        
        CanvasRenderingContext2D.prototype.getImageData = function(sx, sy, sw, sh) {
            const imageData = originalGetImageData.call(this, sx, sy, sw, sh);
            
            // 对小尺寸区域添加噪音
            if (sw < 300 && sh < 300) {
                const data = imageData.data;
                for (let i = 0; i < data.length; i += 4) {
                    if (Math.random() < 0.1) {
                        data[i] = Math.min(255, data[i] + Math.floor(Math.random() * 3) - 1);
                        data[i + 1] = Math.min(255, data[i + 1] + Math.floor(Math.random() * 3) - 1);
                        data[i + 2] = Math.min(255, data[i + 2] + Math.floor(Math.random() * 3) - 1);
                    }
                }
            }
            
            return imageData;
        };
        """
    
    def _get_automation_indicators_patch(self) -> str:
        """获取自动化指示器补丁"""
        return """
        // 移除自动化指示器
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // 修复window.chrome
        if (!window.chrome) {
            window.chrome = {};
        }
        
        // 隐藏Selenium相关属性
        delete window.document.$cdc_asdjflasutopfhvcZLmcfl_;
        delete window.document.$chrome_asyncScriptInfo;
        delete window.document.__$webdriverAsyncExecutor;
        delete window.document.__webdriver_evaluate;
        delete window.document.__selenium_evaluate;
        delete window.document.__webdriver_script_function;
        delete window.document.__webdriver_script_func;
        delete window.document.__webdriver_script_fn;
        delete window.document.__fxdriver_evaluate;
        delete window.document.__driver_unwrapped;
        delete window.document.__webdriver_unwrapped;
        delete window.document.__driver_evaluate;
        delete window.document.__selenium_unwrapped;
        delete window.document.__fxdriver_unwrapped;
        
        // 重写document.querySelector以隐藏自动化脚本
        const originalQuerySelector = document.querySelector;
        document.querySelector = function(selector) {
            if (selector && (
                selector.includes('webdriver') ||
                selector.includes('selenium') ||
                selector.includes('automation')
            )) {
                return null;
            }
            return originalQuerySelector.call(this, selector);
        };
        """
    
    def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        if self.anti_detection_config.custom_user_agent:
            return self.anti_detection_config.custom_user_agent
        
        if self.anti_detection_config.rotate_user_agents:
            return random.choice(self.user_agents)
        
        return self.user_agents[0]  # 默认使用第一个
    
    def get_random_viewport(self) -> tuple:
        """获取随机视口大小"""
        if self.anti_detection_config.custom_viewport:
            return self.anti_detection_config.custom_viewport
        
        if self.anti_detection_config.randomize_viewport:
            return random.choice(self.viewport_sizes)
        
        return (1920, 1080)  # 默认大小
    
    def get_browser_args(self) -> List[str]:
        """获取浏览器启动参数"""
        args = [
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-blink-features=AutomationControlled",
            "--disable-features=VizDisplayCompositor",
            "--disable-ipc-flooding-protection",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",
            "--disable-field-trial-config",
            "--disable-back-forward-cache",
            "--disable-background-timer-throttling",
            "--disable-background-networking",
            "--disable-client-side-phishing-detection",
            "--disable-default-apps",
            "--disable-dev-shm-usage",
            "--disable-extensions-file-access-check",
            "--disable-extensions-http-throttling",
            "--disable-extensions-except",
            "--disable-hang-monitor",
            "--disable-popup-blocking",
            "--disable-prompt-on-repost",
            "--disable-sync",
            "--disable-translate",
            "--disable-web-security",
            "--metrics-recording-only",
            "--no-sandbox",
            "--safebrowsing-disable-auto-update",
            "--enable-automation",
            "--password-store=basic",
            "--use-mock-keychain"
        ]
        
        # 添加随机端口
        args.append(f"--remote-debugging-port={random.randint(9222, 9999)}")
        
        return args
    
    def get_extra_headers(self) -> Dict[str, str]:
        """获取额外的HTTP头"""
        headers = {}
        
        if self.anti_detection_config.randomize_headers:
            # 添加一些常见的头部
            headers.update({
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Accept-Encoding": "gzip, deflate",
                "DNT": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Cache-Control": "max-age=0"
            })
        
        return headers
    
    async def apply_stealth_patches(self, page: Any):
        """
        应用隐身补丁到页面
        
        Args:
            page: 页面对象
        """
        try:
            # 获取所有隐身脚本
            scripts = self.get_stealth_scripts()
            
            # 逐个执行脚本
            for script in scripts:
                await page.add_init_script(script)
            
            logger.debug("隐身补丁应用完成")
            
        except Exception as e:
            logger.error(f"应用隐身补丁失败: {e}")
    
    def should_use_stealth(self) -> bool:
        """判断是否应该使用隐身模式"""
        return (
            self.anti_detection_config.hide_webdriver or
            self.anti_detection_config.patch_chrome_runtime or
            self.anti_detection_config.disable_automation_indicators
        )
