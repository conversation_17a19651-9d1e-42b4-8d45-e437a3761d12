"""
浏览器控制模块 - 浏览器池管理器
管理浏览器实例的创建、分配、回收和监控
"""

import asyncio
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from .models import BrowserInstance, BrowserConfig, BrowserType, BrowserStatus, BrowserPoolStats
from .anti_detection import AntiDetectionManager
from .stealth_patches import StealthPatches
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("browser_pool")


class BrowserPool:
    """浏览器池管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化浏览器池
        
        Args:
            config: 浏览器池配置
        """
        self.config = config
        self.max_instances = config.get("max_instances", 10)
        self.min_instances = config.get("min_instances", 2)
        self.instance_timeout = config.get("instance_timeout", 3600)  # 1小时
        self.health_check_interval = config.get("health_check_interval", 60)  # 1分钟
        
        # 浏览器实例管理
        self.instances: Dict[str, BrowserInstance] = {}
        self.available_instances: List[str] = []
        self.busy_instances: List[str] = []
        
        # 组件
        self.anti_detection = AntiDetectionManager(config)
        self.stealth_patches = StealthPatches()
        
        # 统计信息
        self.stats = BrowserPoolStats()
        
        # 状态管理
        self.is_running = False
        self.health_check_task: Optional[asyncio.Task] = None
        
        logger.info(f"浏览器池初始化 | 最大实例: {self.max_instances} | 最小实例: {self.min_instances}")
    
    async def start(self):
        """启动浏览器池"""
        if self.is_running:
            logger.warning("浏览器池已在运行")
            return
        
        self.is_running = True
        self.stats.pool_start_time = datetime.now()
        
        # 创建最小数量的实例
        await self._ensure_min_instances()
        
        # 启动健康检查
        self.health_check_task = asyncio.create_task(self._health_check_loop())
        
        logger.info("浏览器池启动完成")
    
    async def stop(self):
        """停止浏览器池"""
        self.is_running = False
        
        # 停止健康检查
        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
        
        # 关闭所有实例
        await self._close_all_instances()
        
        logger.info("浏览器池停止完成")
    
    async def get_instance(self, config: Optional[BrowserConfig] = None) -> Optional[BrowserInstance]:
        """
        获取可用的浏览器实例
        
        Args:
            config: 浏览器配置（可选）
            
        Returns:
            浏览器实例
        """
        # 尝试获取现有的可用实例
        if self.available_instances:
            instance_id = self.available_instances.pop(0)
            instance = self.instances[instance_id]
            
            # 移动到忙碌列表
            self.busy_instances.append(instance_id)
            instance.status = BrowserStatus.BUSY
            instance.update_usage()
            
            logger.debug(f"分配现有实例 | ID: {instance_id}")
            return instance
        
        # 如果没有可用实例且未达到最大数量，创建新实例
        if len(self.instances) < self.max_instances:
            instance = await self._create_instance(config)
            if instance:
                self.busy_instances.append(instance.instance_id)
                instance.status = BrowserStatus.BUSY
                logger.debug(f"创建新实例 | ID: {instance.instance_id}")
                return instance
        
        logger.warning("无可用浏览器实例")
        return None
    
    async def release_instance(self, instance_id: str):
        """
        释放浏览器实例
        
        Args:
            instance_id: 实例ID
        """
        if instance_id not in self.instances:
            logger.warning(f"实例不存在: {instance_id}")
            return
        
        instance = self.instances[instance_id]
        
        # 从忙碌列表移除
        if instance_id in self.busy_instances:
            self.busy_instances.remove(instance_id)
        
        # 检查实例是否仍然健康
        if await self._check_instance_health(instance):
            # 添加到可用列表
            self.available_instances.append(instance_id)
            instance.status = BrowserStatus.IDLE
            logger.debug(f"实例已释放 | ID: {instance_id}")
        else:
            # 实例不健康，关闭它
            await self._close_instance(instance_id)
            logger.warning(f"实例不健康已关闭 | ID: {instance_id}")
    
    async def _create_instance(self, config: Optional[BrowserConfig] = None) -> Optional[BrowserInstance]:
        """创建新的浏览器实例"""
        try:
            # 使用默认配置或提供的配置
            if not config:
                config = BrowserConfig()
            
            # 应用反检测配置
            if self.anti_detection.should_use_stealth():
                config.args.extend(self.anti_detection.get_browser_args())
            
            # 创建浏览器实例对象
            instance = BrowserInstance(
                instance_id="",  # 将在__post_init__中生成
                browser_type=config.browser_type,
                config=config
            )
            
            # 启动浏览器
            browser_context, page = await self._launch_browser(config)
            
            if not browser_context or not page:
                logger.error("浏览器启动失败")
                return None
            
            # 设置实例信息
            instance.browser_context = browser_context
            instance.page = page
            instance.process_id = browser_context.browser.process.pid if hasattr(browser_context.browser, 'process') else None
            
            # 应用反检测补丁
            if self.anti_detection.should_use_stealth():
                await self.anti_detection.apply_stealth_patches(page)
            
            # 添加到实例字典
            self.instances[instance.instance_id] = instance
            self.stats.total_instances += 1
            
            logger.info(f"浏览器实例创建成功 | ID: {instance.instance_id} | PID: {instance.process_id}")
            
            return instance
            
        except Exception as e:
            logger.error(f"创建浏览器实例失败: {e}")
            return None
    
    async def _launch_browser(self, config: BrowserConfig) -> tuple:
        """启动浏览器"""
        try:
            from playwright.async_api import async_playwright
            
            playwright = await async_playwright().start()
            
            # 根据浏览器类型选择启动方法
            if config.browser_type == BrowserType.CHROMIUM:
                browser = await playwright.chromium.launch(**config.to_launch_options())
            elif config.browser_type == BrowserType.FIREFOX:
                browser = await playwright.firefox.launch(**config.to_launch_options())
            elif config.browser_type == BrowserType.WEBKIT:
                browser = await playwright.webkit.launch(**config.to_launch_options())
            else:
                raise ValueError(f"不支持的浏览器类型: {config.browser_type}")
            
            # 创建浏览器上下文
            context_options = {
                "viewport": {
                    "width": config.viewport_width,
                    "height": config.viewport_height
                },
                "user_agent": self.anti_detection.get_random_user_agent(),
                "extra_http_headers": self.anti_detection.get_extra_headers()
            }
            
            # 添加代理配置
            if config.proxy_server:
                context_options["proxy"] = {
                    "server": config.proxy_server
                }
                if config.proxy_username and config.proxy_password:
                    context_options["proxy"]["username"] = config.proxy_username
                    context_options["proxy"]["password"] = config.proxy_password
            
            context = await browser.new_context(**context_options)
            
            # 创建页面
            page = await context.new_page()
            
            # 设置超时
            page.set_default_timeout(config.page_load_timeout * 1000)
            page.set_default_navigation_timeout(config.navigation_timeout * 1000)
            
            return context, page
            
        except Exception as e:
            logger.error(f"浏览器启动失败: {e}")
            return None, None
    
    async def _close_instance(self, instance_id: str):
        """关闭浏览器实例"""
        if instance_id not in self.instances:
            return
        
        instance = self.instances[instance_id]
        
        try:
            # 关闭浏览器上下文
            if instance.browser_context:
                await instance.browser_context.close()
            
            # 更新状态
            instance.status = BrowserStatus.CLOSED
            
            logger.debug(f"浏览器实例已关闭 | ID: {instance_id}")
            
        except Exception as e:
            logger.error(f"关闭浏览器实例失败 | ID: {instance_id} | 错误: {e}")
        
        finally:
            # 从各个列表中移除
            if instance_id in self.available_instances:
                self.available_instances.remove(instance_id)
            if instance_id in self.busy_instances:
                self.busy_instances.remove(instance_id)
            
            # 从实例字典中移除
            del self.instances[instance_id]
            self.stats.total_instances -= 1
    
    async def _close_all_instances(self):
        """关闭所有浏览器实例"""
        instance_ids = list(self.instances.keys())
        
        for instance_id in instance_ids:
            await self._close_instance(instance_id)
        
        logger.info("所有浏览器实例已关闭")
    
    async def _check_instance_health(self, instance: BrowserInstance) -> bool:
        """检查实例健康状态"""
        try:
            if not instance.page or not instance.browser_context:
                return False
            
            # 检查页面是否响应
            await instance.page.evaluate("() => document.readyState")
            
            # 检查实例是否超时
            if instance.last_used_at:
                idle_time = (datetime.now() - instance.last_used_at).total_seconds()
                if idle_time > self.instance_timeout:
                    logger.debug(f"实例超时 | ID: {instance.instance_id} | 空闲时间: {idle_time}s")
                    return False
            
            return True
            
        except Exception as e:
            logger.warning(f"实例健康检查失败 | ID: {instance.instance_id} | 错误: {e}")
            return False
    
    async def _ensure_min_instances(self):
        """确保最小实例数量"""
        current_count = len(self.instances)
        
        if current_count < self.min_instances:
            needed = self.min_instances - current_count
            
            logger.info(f"创建最小实例数量 | 需要: {needed}")
            
            for _ in range(needed):
                instance = await self._create_instance()
                if instance:
                    self.available_instances.append(instance.instance_id)
                    instance.status = BrowserStatus.IDLE
    
    async def _health_check_loop(self):
        """健康检查循环"""
        logger.debug("健康检查循环启动")
        
        while self.is_running:
            try:
                await self._perform_health_check()
                await asyncio.sleep(self.health_check_interval)
                
            except Exception as e:
                logger.error(f"健康检查异常: {e}")
                await asyncio.sleep(10)
        
        logger.debug("健康检查循环停止")
    
    async def _perform_health_check(self):
        """执行健康检查"""
        unhealthy_instances = []
        
        # 检查所有实例
        for instance_id, instance in self.instances.items():
            if not await self._check_instance_health(instance):
                unhealthy_instances.append(instance_id)
        
        # 关闭不健康的实例
        for instance_id in unhealthy_instances:
            await self._close_instance(instance_id)
        
        # 确保最小实例数量
        await self._ensure_min_instances()
        
        # 更新统计信息
        self._update_stats()
    
    def _update_stats(self):
        """更新统计信息"""
        self.stats.total_instances = len(self.instances)
        self.stats.idle_instances = len(self.available_instances)
        self.stats.busy_instances = len(self.busy_instances)
        self.stats.crashed_instances = len([
            i for i in self.instances.values() 
            if i.status == BrowserStatus.CRASHED
        ])
        
        # 计算总体统计
        self.stats.total_navigations = sum(i.total_navigations for i in self.instances.values())
        self.stats.total_interactions = sum(i.total_interactions for i in self.instances.values())
        self.stats.total_errors = sum(i.total_errors for i in self.instances.values())
        
        # 计算成功率
        total_operations = self.stats.total_navigations + self.stats.total_interactions
        if total_operations > 0:
            self.stats.success_rate = ((total_operations - self.stats.total_errors) / total_operations) * 100
        
        self.stats.update()
    
    def get_instance_info(self, instance_id: str) -> Optional[Dict[str, Any]]:
        """获取实例信息"""
        if instance_id not in self.instances:
            return None
        
        instance = self.instances[instance_id]
        
        return {
            "instance_id": instance.instance_id,
            "browser_type": instance.browser_type.value,
            "status": instance.status.value,
            "created_at": instance.created_at.isoformat(),
            "last_used_at": instance.last_used_at.isoformat() if instance.last_used_at else None,
            "uptime": instance.uptime,
            "total_navigations": instance.total_navigations,
            "total_interactions": instance.total_interactions,
            "total_errors": instance.total_errors,
            "current_url": instance.current_url,
            "current_title": instance.current_title,
            "process_id": instance.process_id
        }
    
    def get_pool_statistics(self) -> Dict[str, Any]:
        """获取浏览器池统计信息"""
        self._update_stats()
        
        return {
            "total_instances": self.stats.total_instances,
            "idle_instances": self.stats.idle_instances,
            "busy_instances": self.stats.busy_instances,
            "crashed_instances": self.stats.crashed_instances,
            "utilization_rate": self.stats.utilization_rate,
            "total_navigations": self.stats.total_navigations,
            "total_interactions": self.stats.total_interactions,
            "total_errors": self.stats.total_errors,
            "success_rate": self.stats.success_rate,
            "uptime": self.stats.uptime,
            "max_instances": self.max_instances,
            "min_instances": self.min_instances
        }
