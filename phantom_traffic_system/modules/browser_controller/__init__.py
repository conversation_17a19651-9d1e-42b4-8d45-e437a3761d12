"""
幻影流量系统 - 浏览器控制模块
负责浏览器实例管理、反检测技术、页面交互控制
"""

from .browser_controller import BrowserController
from .browser_pool import BrowserPool
from .anti_detection import AntiDetectionManager
from .page_interaction import PageInteractionController
from .stealth_patches import StealthPatches
from .models import BrowserInstance, BrowserConfig, InteractionResult

__all__ = [
    "BrowserController",
    "BrowserPool",
    "AntiDetectionManager",
    "PageInteractionController",
    "StealthPatches",
    "BrowserInstance",
    "BrowserConfig", 
    "InteractionResult"
]
