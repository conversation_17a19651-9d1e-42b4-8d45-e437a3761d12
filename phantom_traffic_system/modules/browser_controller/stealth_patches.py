"""
浏览器控制模块 - 隐身补丁
提供高级的反检测JavaScript补丁
"""

from typing import Dict, List, Any
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("stealth_patches")


class StealthPatches:
    """隐身补丁管理器"""
    
    def __init__(self):
        """初始化隐身补丁管理器"""
        self.patches = {}
        self._init_patches()
        
        logger.debug("隐身补丁管理器初始化完成")
    
    def _init_patches(self):
        """初始化所有补丁"""
        self.patches = {
            "webdriver": self._get_webdriver_patch(),
            "chrome_runtime": self._get_chrome_runtime_patch(),
            "permissions": self._get_permissions_patch(),
            "plugins": self._get_plugins_patch(),
            "languages": self._get_languages_patch(),
            "webgl_vendor": self._get_webgl_vendor_patch(),
            "navigator_vendor": self._get_navigator_vendor_patch(),
            "user_agent_data": self._get_user_agent_data_patch(),
            "media_codecs": self._get_media_codecs_patch(),
            "iframe_contentWindow": self._get_iframe_contentwindow_patch(),
            "hairline_fix": self._get_hairline_fix_patch(),
            "console_debug": self._get_console_debug_patch(),
            "window_outerdimensions": self._get_window_outerdimensions_patch(),
            "notification_permissions": self._get_notification_permissions_patch()
        }
    
    def _get_webdriver_patch(self) -> str:
        """WebDriver检测补丁"""
        return """
        (() => {
            // 删除webdriver属性
            delete Object.getPrototypeOf(navigator).webdriver;
            delete navigator.__proto__.webdriver;
            delete navigator.webdriver;
            
            // 重新定义webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
                set: () => {},
                enumerable: false,
                configurable: true
            });
            
            // 修复webdriver的toString
            const originalToString = Function.prototype.toString;
            Function.prototype.toString = function() {
                if (this === navigator.webdriver) {
                    return 'function webdriver() { [native code] }';
                }
                return originalToString.call(this);
            };
        })();
        """
    
    def _get_chrome_runtime_patch(self) -> str:
        """Chrome Runtime补丁"""
        return """
        (() => {
            // 修复chrome.runtime检测
            if (!window.chrome) {
                window.chrome = {};
            }
            
            if (!window.chrome.runtime) {
                window.chrome.runtime = {};
            }
            
            // 移除可能暴露自动化的属性
            delete window.chrome.runtime.onConnect;
            delete window.chrome.runtime.onMessage;
            delete window.chrome.runtime.onConnectExternal;
            delete window.chrome.runtime.onMessageExternal;
            
            // 添加正常的chrome.runtime方法
            window.chrome.runtime.getManifest = () => undefined;
            window.chrome.runtime.connect = () => undefined;
            window.chrome.runtime.sendMessage = () => undefined;
            
            // 添加chrome.loadTimes (已废弃但某些检测仍在使用)
            if (!window.chrome.loadTimes) {
                window.chrome.loadTimes = function() {
                    return {
                        commitLoadTime: performance.timing.responseStart / 1000,
                        connectionInfo: 'http/1.1',
                        finishDocumentLoadTime: performance.timing.domContentLoadedEventEnd / 1000,
                        finishLoadTime: performance.timing.loadEventEnd / 1000,
                        firstPaintAfterLoadTime: 0,
                        firstPaintTime: performance.timing.responseStart / 1000,
                        navigationType: 'Other',
                        npnNegotiatedProtocol: 'unknown',
                        requestTime: performance.timing.requestStart / 1000,
                        startLoadTime: performance.timing.requestStart / 1000,
                        wasAlternateProtocolAvailable: false,
                        wasFetchedViaSpdy: false,
                        wasNpnNegotiated: false
                    };
                };
            }
            
            // 添加chrome.csi
            if (!window.chrome.csi) {
                window.chrome.csi = function() {
                    return {
                        pageT: Date.now(),
                        tran: 15
                    };
                };
            }
        })();
        """
    
    def _get_permissions_patch(self) -> str:
        """权限API补丁"""
        return """
        (() => {
            const originalQuery = window.navigator.permissions.query;
            
            window.navigator.permissions.query = function(parameters) {
                const permission = parameters.name;
                
                // 对于通知权限，返回实际的Notification.permission状态
                if (permission === 'notifications') {
                    return Promise.resolve({
                        state: Notification.permission,
                        onchange: null
                    });
                }
                
                // 对于其他权限，使用原始方法
                return originalQuery.call(this, parameters);
            };
            
            // 确保permissions.query的toString正常
            Object.defineProperty(window.navigator.permissions.query, 'toString', {
                value: () => 'function query() { [native code] }',
                writable: false,
                enumerable: false,
                configurable: true
            });
        })();
        """
    
    def _get_plugins_patch(self) -> str:
        """插件补丁"""
        return """
        (() => {
            // 创建真实的插件对象
            const pdfPlugin = {
                0: {
                    type: "application/x-google-chrome-pdf",
                    suffixes: "pdf",
                    description: "Portable Document Format",
                    enabledPlugin: null
                },
                description: "Portable Document Format",
                filename: "internal-pdf-viewer",
                length: 1,
                name: "Chrome PDF Plugin"
            };
            
            const pdfViewer = {
                0: {
                    type: "application/pdf",
                    suffixes: "pdf",
                    description: "Portable Document Format", 
                    enabledPlugin: null
                },
                description: "Portable Document Format",
                filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                length: 1,
                name: "Chrome PDF Viewer"
            };
            
            // 设置enabledPlugin引用
            pdfPlugin[0].enabledPlugin = pdfPlugin;
            pdfViewer[0].enabledPlugin = pdfViewer;
            
            // 重新定义navigator.plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => {
                    const plugins = [pdfPlugin, pdfViewer];
                    
                    // 添加数组方法
                    plugins.item = function(index) {
                        return this[index] || null;
                    };
                    
                    plugins.namedItem = function(name) {
                        return this.find(plugin => plugin.name === name) || null;
                    };
                    
                    plugins.refresh = function() {};
                    
                    return plugins;
                },
                enumerable: true,
                configurable: true
            });
            
            // 重新定义navigator.mimeTypes
            Object.defineProperty(navigator, 'mimeTypes', {
                get: () => {
                    const mimeTypes = [
                        {
                            type: "application/pdf",
                            suffixes: "pdf",
                            description: "Portable Document Format",
                            enabledPlugin: pdfViewer
                        }
                    ];
                    
                    mimeTypes.item = function(index) {
                        return this[index] || null;
                    };
                    
                    mimeTypes.namedItem = function(type) {
                        return this.find(mime => mime.type === type) || null;
                    };
                    
                    return mimeTypes;
                },
                enumerable: true,
                configurable: true
            });
        })();
        """
    
    def _get_languages_patch(self) -> str:
        """语言补丁"""
        return """
        (() => {
            // 确保languages和language一致
            Object.defineProperty(navigator, 'languages', {
                get: () => [navigator.language, 'en'],
                enumerable: true,
                configurable: true
            });
        })();
        """
    
    def _get_webgl_vendor_patch(self) -> str:
        """WebGL供应商补丁"""
        return """
        (() => {
            const getParameter = WebGLRenderingContext.prototype.getParameter;
            
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                // 37445 = VENDOR, 37446 = RENDERER
                if (parameter === 37445) {
                    return 'Google Inc. (Intel)';
                }
                if (parameter === 37446) {
                    return 'ANGLE (Intel, Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0, D3D11)';
                }
                
                return getParameter.call(this, parameter);
            };
            
            // 同样处理WebGL2
            if (typeof WebGL2RenderingContext !== 'undefined') {
                WebGL2RenderingContext.prototype.getParameter = WebGLRenderingContext.prototype.getParameter;
            }
        })();
        """
    
    def _get_navigator_vendor_patch(self) -> str:
        """Navigator供应商补丁"""
        return """
        (() => {
            Object.defineProperty(navigator, 'vendor', {
                get: () => 'Google Inc.',
                enumerable: true,
                configurable: true
            });
        })();
        """
    
    def _get_user_agent_data_patch(self) -> str:
        """User Agent Data补丁"""
        return """
        (() => {
            if (!navigator.userAgentData) {
                Object.defineProperty(navigator, 'userAgentData', {
                    get: () => ({
                        brands: [
                            { brand: 'Not_A Brand', version: '8' },
                            { brand: 'Chromium', version: '120' },
                            { brand: 'Google Chrome', version: '120' }
                        ],
                        mobile: false,
                        platform: 'Windows',
                        getHighEntropyValues: async (hints) => ({
                            architecture: 'x86',
                            bitness: '64',
                            brands: [
                                { brand: 'Not_A Brand', version: '8' },
                                { brand: 'Chromium', version: '120' },
                                { brand: 'Google Chrome', version: '120' }
                            ],
                            fullVersionList: [
                                { brand: 'Not_A Brand', version: '8.0.0.0' },
                                { brand: 'Chromium', version: '120.0.6099.109' },
                                { brand: 'Google Chrome', version: '120.0.6099.109' }
                            ],
                            mobile: false,
                            model: '',
                            platform: 'Windows',
                            platformVersion: '10.0.0',
                            uaFullVersion: '120.0.6099.109',
                            wow64: false
                        }),
                        toJSON: function() {
                            return {
                                brands: this.brands,
                                mobile: this.mobile,
                                platform: this.platform
                            };
                        }
                    }),
                    enumerable: true,
                    configurable: true
                });
            }
        })();
        """
    
    def _get_media_codecs_patch(self) -> str:
        """媒体编解码器补丁"""
        return """
        (() => {
            const canPlayType = HTMLMediaElement.prototype.canPlayType;
            
            HTMLMediaElement.prototype.canPlayType = function(type) {
                // 确保返回合理的编解码器支持
                if (type.includes('webm')) {
                    return 'probably';
                }
                if (type.includes('mp4')) {
                    return 'probably';
                }
                if (type.includes('ogg')) {
                    return 'maybe';
                }
                
                return canPlayType.call(this, type);
            };
        })();
        """
    
    def _get_iframe_contentwindow_patch(self) -> str:
        """iframe contentWindow补丁"""
        return """
        (() => {
            try {
                // 修复iframe.contentWindow检测
                const originalContentWindow = Object.getOwnPropertyDescriptor(HTMLIFrameElement.prototype, 'contentWindow');
                
                if (originalContentWindow) {
                    Object.defineProperty(HTMLIFrameElement.prototype, 'contentWindow', {
                        get: function() {
                            const win = originalContentWindow.get.call(this);
                            if (win) {
                                // 确保iframe中的window也有正确的属性
                                try {
                                    if (!win.navigator.webdriver) {
                                        Object.defineProperty(win.navigator, 'webdriver', {
                                            get: () => undefined,
                                            enumerable: false,
                                            configurable: true
                                        });
                                    }
                                } catch (e) {
                                    // 跨域iframe可能会抛出异常，忽略
                                }
                            }
                            return win;
                        },
                        enumerable: true,
                        configurable: true
                    });
                }
            } catch (e) {
                // 某些情况下可能失败，忽略错误
            }
        })();
        """
    
    def _get_hairline_fix_patch(self) -> str:
        """Hairline修复补丁"""
        return """
        (() => {
            // 修复一些检测脚本使用的hairline技术
            const originalGetComputedStyle = window.getComputedStyle;
            
            window.getComputedStyle = function(element, pseudoElement) {
                const style = originalGetComputedStyle.call(this, element, pseudoElement);
                
                // 创建代理对象来拦截属性访问
                return new Proxy(style, {
                    get: function(target, property) {
                        // 对于某些可能被用于检测的CSS属性，返回正常值
                        if (property === 'getPropertyValue') {
                            return function(prop) {
                                const value = target.getPropertyValue(prop);
                                // 确保返回的值看起来正常
                                return value;
                            };
                        }
                        
                        return target[property];
                    }
                });
            };
        })();
        """
    
    def _get_console_debug_patch(self) -> str:
        """控制台调试补丁"""
        return """
        (() => {
            // 隐藏控制台中的自动化痕迹
            const originalLog = console.log;
            const originalWarn = console.warn;
            const originalError = console.error;
            
            console.log = function(...args) {
                const message = args.join(' ');
                if (message.includes('webdriver') || 
                    message.includes('automation') || 
                    message.includes('selenium')) {
                    return;
                }
                return originalLog.apply(this, args);
            };
            
            console.warn = function(...args) {
                const message = args.join(' ');
                if (message.includes('webdriver') || 
                    message.includes('automation') || 
                    message.includes('selenium')) {
                    return;
                }
                return originalWarn.apply(this, args);
            };
            
            console.error = function(...args) {
                const message = args.join(' ');
                if (message.includes('webdriver') || 
                    message.includes('automation') || 
                    message.includes('selenium')) {
                    return;
                }
                return originalError.apply(this, args);
            };
        })();
        """
    
    def _get_window_outerdimensions_patch(self) -> str:
        """窗口外部尺寸补丁"""
        return """
        (() => {
            // 确保window.outerWidth和window.outerHeight合理
            try {
                if (window.outerWidth === 0 || window.outerHeight === 0) {
                    Object.defineProperty(window, 'outerWidth', {
                        get: () => window.innerWidth,
                        enumerable: true,
                        configurable: true
                    });
                    
                    Object.defineProperty(window, 'outerHeight', {
                        get: () => window.innerHeight + 85, // 添加浏览器UI高度
                        enumerable: true,
                        configurable: true
                    });
                }
            } catch (e) {
                // 某些情况下可能失败，忽略错误
            }
        })();
        """
    
    def _get_notification_permissions_patch(self) -> str:
        """通知权限补丁"""
        return """
        (() => {
            // 确保Notification.permission返回合理的值
            if (typeof Notification !== 'undefined') {
                try {
                    if (Notification.permission === '') {
                        Object.defineProperty(Notification, 'permission', {
                            get: () => 'default',
                            enumerable: true,
                            configurable: true
                        });
                    }
                } catch (e) {
                    // 某些情况下可能失败，忽略错误
                }
            }
        })();
        """
    
    def get_patch(self, patch_name: str) -> str:
        """
        获取指定的补丁
        
        Args:
            patch_name: 补丁名称
            
        Returns:
            补丁JavaScript代码
        """
        return self.patches.get(patch_name, "")
    
    def get_all_patches(self) -> List[str]:
        """
        获取所有补丁
        
        Returns:
            所有补丁的JavaScript代码列表
        """
        return list(self.patches.values())
    
    def get_patches_by_names(self, patch_names: List[str]) -> List[str]:
        """
        根据名称获取补丁列表
        
        Args:
            patch_names: 补丁名称列表
            
        Returns:
            对应的补丁JavaScript代码列表
        """
        return [self.patches.get(name, "") for name in patch_names if name in self.patches]
    
    def get_available_patches(self) -> List[str]:
        """
        获取可用的补丁名称列表
        
        Returns:
            补丁名称列表
        """
        return list(self.patches.keys())
