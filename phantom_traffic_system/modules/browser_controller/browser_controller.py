"""
浏览器控制模块 - 浏览器控制器
统一管理浏览器实例、页面交互和反检测功能
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime

from .models import BrowserInstance, BrowserConfig, InteractionResult, PageLoadResult
from .browser_pool import BrowserPool
from .page_interaction import PageInteractionController
from .anti_detection import AntiDetectionManager
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("browser_controller")


class BrowserController:
    """浏览器控制器 - 浏览器控制系统的中央控制器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化浏览器控制器
        
        Args:
            config: 浏览器控制配置
        """
        self.config = config
        
        # 初始化各个组件
        self.browser_pool = BrowserPool(config.get("browser_pool", {}))
        self.page_interaction = PageInteractionController(config.get("page_interaction", {}))
        self.anti_detection = AntiDetectionManager(config.get("anti_detection", {}))
        
        # 会话管理
        self.active_sessions: Dict[str, BrowserInstance] = {}
        
        logger.info("浏览器控制器初始化完成")
    
    async def start(self):
        """启动浏览器控制器"""
        await self.browser_pool.start()
        logger.info("浏览器控制器启动")
    
    async def stop(self):
        """停止浏览器控制器"""
        # 结束所有活跃会话
        for session_id in list(self.active_sessions.keys()):
            await self.end_session(session_id)
        
        await self.browser_pool.stop()
        logger.info("浏览器控制器停止")
    
    async def create_session(self, session_id: str, 
                           browser_config: Optional[BrowserConfig] = None) -> bool:
        """
        创建浏览器会话
        
        Args:
            session_id: 会话ID
            browser_config: 浏览器配置
            
        Returns:
            是否创建成功
        """
        if session_id in self.active_sessions:
            logger.warning(f"会话已存在: {session_id}")
            return False
        
        # 从浏览器池获取实例
        instance = await self.browser_pool.get_instance(browser_config)
        if not instance:
            logger.error(f"无法获取浏览器实例 | 会话: {session_id}")
            return False
        
        # 添加到活跃会话
        self.active_sessions[session_id] = instance
        
        logger.info(f"浏览器会话创建成功 | 会话: {session_id} | 实例: {instance.instance_id}")
        
        return True
    
    async def end_session(self, session_id: str):
        """
        结束浏览器会话
        
        Args:
            session_id: 会话ID
        """
        if session_id not in self.active_sessions:
            logger.warning(f"会话不存在: {session_id}")
            return
        
        instance = self.active_sessions[session_id]
        
        # 释放浏览器实例回池中
        await self.browser_pool.release_instance(instance.instance_id)
        
        # 从活跃会话中移除
        del self.active_sessions[session_id]
        
        logger.info(f"浏览器会话结束 | 会话: {session_id} | 实例: {instance.instance_id}")
    
    async def navigate_to_page(self, session_id: str, url: str, 
                             wait_until: str = "networkidle") -> PageLoadResult:
        """
        导航到指定页面
        
        Args:
            session_id: 会话ID
            url: 目标URL
            wait_until: 等待条件
            
        Returns:
            页面加载结果
        """
        instance = self._get_session_instance(session_id)
        if not instance:
            return PageLoadResult(
                url=url,
                success=False,
                load_time=0.0,
                final_url=url,
                title="",
                error_message="会话不存在"
            )
        
        # 执行导航
        result = await self.page_interaction.navigate_to_page(
            instance.page, url, wait_until
        )
        
        # 更新实例信息
        if result.success:
            instance.record_navigation(result.final_url)
            instance.current_title = result.title
        else:
            instance.record_error()
        
        return result
    
    async def click_element(self, session_id: str, selector: str, 
                          human_like: bool = True) -> InteractionResult:
        """
        点击页面元素
        
        Args:
            session_id: 会话ID
            selector: 元素选择器
            human_like: 是否使用人性化点击
            
        Returns:
            交互结果
        """
        instance = self._get_session_instance(session_id)
        if not instance:
            return InteractionResult(
                interaction_type="click",
                success=False,
                duration=0.0,
                error_message="会话不存在"
            )
        
        # 执行点击
        result = await self.page_interaction.click_element(
            instance.page, selector, human_like
        )
        
        # 更新实例统计
        if result.success:
            instance.record_interaction()
        else:
            instance.record_error()
        
        return result
    
    async def type_text(self, session_id: str, selector: str, text: str,
                       human_like: bool = True) -> InteractionResult:
        """
        在元素中输入文本
        
        Args:
            session_id: 会话ID
            selector: 元素选择器
            text: 要输入的文本
            human_like: 是否使用人性化输入
            
        Returns:
            交互结果
        """
        instance = self._get_session_instance(session_id)
        if not instance:
            return InteractionResult(
                interaction_type="type",
                success=False,
                duration=0.0,
                error_message="会话不存在"
            )
        
        # 执行输入
        result = await self.page_interaction.type_text(
            instance.page, selector, text, human_like
        )
        
        # 更新实例统计
        if result.success:
            instance.record_interaction()
        else:
            instance.record_error()
        
        return result
    
    async def scroll_page(self, session_id: str, direction: str = "down", 
                         distance: int = 500, human_like: bool = True) -> InteractionResult:
        """
        滚动页面
        
        Args:
            session_id: 会话ID
            direction: 滚动方向
            distance: 滚动距离
            human_like: 是否使用人性化滚动
            
        Returns:
            交互结果
        """
        instance = self._get_session_instance(session_id)
        if not instance:
            return InteractionResult(
                interaction_type="scroll",
                success=False,
                duration=0.0,
                error_message="会话不存在"
            )
        
        # 执行滚动
        result = await self.page_interaction.scroll_page(
            instance.page, direction, distance, human_like
        )
        
        # 更新实例统计
        if result.success:
            instance.record_interaction()
        else:
            instance.record_error()
        
        return result
    
    async def hover_element(self, session_id: str, selector: str) -> InteractionResult:
        """
        悬停在元素上
        
        Args:
            session_id: 会话ID
            selector: 元素选择器
            
        Returns:
            交互结果
        """
        instance = self._get_session_instance(session_id)
        if not instance:
            return InteractionResult(
                interaction_type="hover",
                success=False,
                duration=0.0,
                error_message="会话不存在"
            )
        
        # 执行悬停
        result = await self.page_interaction.hover_element(instance.page, selector)
        
        # 更新实例统计
        if result.success:
            instance.record_interaction()
        else:
            instance.record_error()
        
        return result
    
    async def wait_for_element(self, session_id: str, selector: str, 
                             timeout: Optional[int] = None) -> InteractionResult:
        """
        等待元素出现
        
        Args:
            session_id: 会话ID
            selector: 元素选择器
            timeout: 超时时间（毫秒）
            
        Returns:
            交互结果
        """
        instance = self._get_session_instance(session_id)
        if not instance:
            return InteractionResult(
                interaction_type="wait",
                success=False,
                duration=0.0,
                error_message="会话不存在"
            )
        
        # 执行等待
        result = await self.page_interaction.wait_for_element(
            instance.page, selector, timeout
        )
        
        return result
    
    async def take_screenshot(self, session_id: str, file_path: Optional[str] = None,
                            full_page: bool = False):
        """
        截取页面截图
        
        Args:
            session_id: 会话ID
            file_path: 保存路径
            full_page: 是否截取整页
            
        Returns:
            截图结果
        """
        instance = self._get_session_instance(session_id)
        if not instance:
            return {
                "success": False,
                "error_message": "会话不存在"
            }
        
        # 执行截图
        result = await self.page_interaction.take_screenshot(
            instance.page, file_path, full_page
        )
        
        return result
    
    async def evaluate_script(self, session_id: str, script: str) -> InteractionResult:
        """
        执行JavaScript脚本
        
        Args:
            session_id: 会话ID
            script: JavaScript代码
            
        Returns:
            交互结果
        """
        instance = self._get_session_instance(session_id)
        if not instance:
            return InteractionResult(
                interaction_type="evaluate",
                success=False,
                duration=0.0,
                error_message="会话不存在"
            )
        
        # 执行脚本
        result = await self.page_interaction.evaluate_script(instance.page, script)
        
        return result
    
    async def inject_fingerprint_scripts(self, session_id: str, 
                                       fingerprint_scripts: Dict[str, str]) -> bool:
        """
        注入指纹伪装脚本
        
        Args:
            session_id: 会话ID
            fingerprint_scripts: 指纹脚本字典
            
        Returns:
            是否注入成功
        """
        instance = self._get_session_instance(session_id)
        if not instance:
            logger.error(f"会话不存在: {session_id}")
            return False
        
        try:
            # 注入各种指纹脚本
            for script_name, script_code in fingerprint_scripts.items():
                await instance.page.add_init_script(script_code)
                logger.debug(f"指纹脚本注入成功 | 会话: {session_id} | 脚本: {script_name}")
            
            logger.info(f"所有指纹脚本注入完成 | 会话: {session_id} | 数量: {len(fingerprint_scripts)}")
            return True
            
        except Exception as e:
            logger.error(f"指纹脚本注入失败 | 会话: {session_id} | 错误: {e}")
            return False
    
    def _get_session_instance(self, session_id: str) -> Optional[BrowserInstance]:
        """获取会话对应的浏览器实例"""
        return self.active_sessions.get(session_id)
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        获取会话信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话信息
        """
        instance = self._get_session_instance(session_id)
        if not instance:
            return None
        
        return {
            "session_id": session_id,
            "instance_id": instance.instance_id,
            "browser_type": instance.browser_type.value,
            "status": instance.status.value,
            "current_url": instance.current_url,
            "current_title": instance.current_title,
            "total_navigations": instance.total_navigations,
            "total_interactions": instance.total_interactions,
            "total_errors": instance.total_errors,
            "created_at": instance.created_at.isoformat(),
            "last_used_at": instance.last_used_at.isoformat() if instance.last_used_at else None,
            "uptime": instance.uptime
        }
    
    def get_active_sessions(self) -> List[str]:
        """获取所有活跃会话ID"""
        return list(self.active_sessions.keys())
    
    def get_controller_statistics(self) -> Dict[str, Any]:
        """获取控制器统计信息"""
        pool_stats = self.browser_pool.get_pool_statistics()
        
        return {
            "active_sessions": len(self.active_sessions),
            "browser_pool": pool_stats,
            "sessions": {
                session_id: {
                    "instance_id": instance.instance_id,
                    "current_url": instance.current_url,
                    "total_interactions": instance.total_interactions,
                    "uptime": instance.uptime
                }
                for session_id, instance in self.active_sessions.items()
            }
        }
    
    async def execute_behavior_sequence(self, session_id: str, 
                                      behavior_sequence: Any) -> Dict[str, Any]:
        """
        执行行为序列
        
        Args:
            session_id: 会话ID
            behavior_sequence: 行为序列对象
            
        Returns:
            执行结果
        """
        instance = self._get_session_instance(session_id)
        if not instance:
            return {
                "success": False,
                "error_message": "会话不存在"
            }
        
        execution_results = {
            "session_id": session_id,
            "sequence_id": behavior_sequence.sequence_id,
            "start_time": datetime.now(),
            "actions_executed": 0,
            "actions_failed": 0,
            "total_duration": 0.0,
            "errors": []
        }
        
        start_time = datetime.now()
        
        try:
            # 执行行为序列中的每个动作
            for i, action in enumerate(behavior_sequence.actions):
                try:
                    await self._execute_behavior_action(session_id, action)
                    execution_results["actions_executed"] += 1
                    
                except Exception as e:
                    execution_results["actions_failed"] += 1
                    execution_results["errors"].append({
                        "action_index": i,
                        "action_type": type(action).__name__,
                        "error": str(e)
                    })
                    
                    logger.error(f"行为动作执行失败 | 会话: {session_id} | 动作: {i} | 错误: {e}")
            
            # 计算总执行时间
            end_time = datetime.now()
            execution_results["end_time"] = end_time
            execution_results["total_duration"] = (end_time - start_time).total_seconds()
            execution_results["success"] = execution_results["actions_failed"] == 0
            
            logger.info(
                f"行为序列执行完成 | 会话: {session_id} | "
                f"成功: {execution_results['actions_executed']} | "
                f"失败: {execution_results['actions_failed']}"
            )
            
            return execution_results
            
        except Exception as e:
            logger.error(f"行为序列执行异常 | 会话: {session_id} | 错误: {e}")
            execution_results["success"] = False
            execution_results["error_message"] = str(e)
            return execution_results
    
    async def _execute_behavior_action(self, session_id: str, action: Any):
        """执行单个行为动作"""
        from ..behavior_simulator.models import MouseAction, ScrollAction, TimingAction
        
        if isinstance(action, TimingAction):
            # 等待动作
            await asyncio.sleep(action.delay)
        
        elif isinstance(action, MouseAction):
            # 鼠标动作
            if action.action_type.value == "click":
                # 这里需要根据action的坐标信息执行点击
                # 暂时使用模拟等待
                await asyncio.sleep(action.duration)
            else:
                await asyncio.sleep(action.duration)
        
        elif isinstance(action, ScrollAction):
            # 滚动动作
            direction = "down" if action.direction.value == "down" else "up"
            await self.scroll_page(session_id, direction, action.distance, True)
            
            # 如果有段间暂停，也要等待
            if action.pause_between_segments > 0:
                await asyncio.sleep(action.pause_between_segments)
        
        else:
            logger.warning(f"未知动作类型: {type(action)}")
            await asyncio.sleep(1)  # 默认等待1秒
