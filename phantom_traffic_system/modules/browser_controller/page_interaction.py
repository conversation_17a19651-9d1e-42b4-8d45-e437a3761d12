"""
浏览器控制模块 - 页面交互控制器
处理页面导航、元素交互、截图等操作
"""

import asyncio
import random
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from .models import InteractionResult, InteractionType, PageLoadResult, ElementInfo, ScreenshotResult
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("page_interaction")


class PageInteractionController:
    """页面交互控制器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化页面交互控制器
        
        Args:
            config: 交互配置
        """
        self.config = config
        self.default_timeout = config.get("default_timeout", 30000)
        self.navigation_timeout = config.get("navigation_timeout", 30000)
        self.human_delays = config.get("human_delays", True)
        self.screenshot_on_error = config.get("screenshot_on_error", True)
        
        logger.debug("页面交互控制器初始化完成")
    
    async def navigate_to_page(self, page: Any, url: str, 
                             wait_until: str = "networkidle") -> PageLoadResult:
        """
        导航到指定页面
        
        Args:
            page: 页面对象
            url: 目标URL
            wait_until: 等待条件
            
        Returns:
            页面加载结果
        """
        start_time = time.time()
        
        try:
            logger.info(f"导航到页面: {url}")
            
            # 执行导航
            response = await page.goto(
                url,
                wait_until=wait_until,
                timeout=self.navigation_timeout
            )
            
            # 等待页面稳定
            if self.human_delays:
                await asyncio.sleep(random.uniform(1, 3))
            
            # 获取页面信息
            final_url = page.url
            title = await page.title()
            
            load_time = time.time() - start_time
            
            # 获取性能指标
            performance_metrics = await self._get_performance_metrics(page)
            
            result = PageLoadResult(
                url=url,
                success=True,
                load_time=load_time,
                final_url=final_url,
                title=title,
                status_code=response.status if response else None,
                **performance_metrics
            )
            
            logger.info(f"页面加载成功 | URL: {final_url} | 耗时: {load_time:.2f}s")
            
            return result
            
        except Exception as e:
            load_time = time.time() - start_time
            error_message = str(e)
            
            logger.error(f"页面导航失败 | URL: {url} | 错误: {error_message}")
            
            # 错误时截图
            if self.screenshot_on_error:
                try:
                    await self.take_screenshot(page, f"error_{int(time.time())}.png")
                except:
                    pass
            
            return PageLoadResult(
                url=url,
                success=False,
                load_time=load_time,
                final_url=url,
                title="",
                error_message=error_message
            )
    
    async def _get_performance_metrics(self, page: Any) -> Dict[str, Any]:
        """获取页面性能指标"""
        try:
            # 获取导航时间
            navigation_timing = await page.evaluate("""
                () => {
                    const timing = performance.timing;
                    const navigation = performance.getEntriesByType('navigation')[0];
                    
                    return {
                        dom_content_loaded: timing.domContentLoadedEventEnd - timing.navigationStart,
                        load_event: timing.loadEventEnd - timing.navigationStart,
                        first_paint: navigation ? navigation.responseStart - navigation.requestStart : null,
                        first_contentful_paint: null // 需要Performance Observer API
                    };
                }
            """)
            
            # 获取资源数量
            resource_count = await page.evaluate("() => performance.getEntriesByType('resource').length")
            
            # 获取页面内容长度
            content_length = await page.evaluate("() => document.documentElement.outerHTML.length")
            
            return {
                "dom_content_loaded": navigation_timing.get("dom_content_loaded"),
                "load_event": navigation_timing.get("load_event"),
                "first_paint": navigation_timing.get("first_paint"),
                "first_contentful_paint": navigation_timing.get("first_contentful_paint"),
                "resource_count": resource_count,
                "content_length": content_length
            }
            
        except Exception as e:
            logger.warning(f"获取性能指标失败: {e}")
            return {}
    
    async def click_element(self, page: Any, selector: str, 
                          human_like: bool = True) -> InteractionResult:
        """
        点击页面元素
        
        Args:
            page: 页面对象
            selector: 元素选择器
            human_like: 是否使用人性化点击
            
        Returns:
            交互结果
        """
        start_time = time.time()
        
        try:
            logger.debug(f"点击元素: {selector}")
            
            # 等待元素可见
            await page.wait_for_selector(selector, timeout=self.default_timeout)
            
            # 人性化延迟
            if human_like and self.human_delays:
                await asyncio.sleep(random.uniform(0.1, 0.5))
            
            # 滚动到元素位置
            await page.locator(selector).scroll_into_view_if_needed()
            
            # 再次等待以确保元素稳定
            if human_like:
                await asyncio.sleep(random.uniform(0.1, 0.3))
            
            # 执行点击
            await page.locator(selector).click()
            
            duration = time.time() - start_time
            
            result = InteractionResult(
                interaction_type=InteractionType.CLICK,
                success=True,
                duration=duration,
                page_url=page.url,
                page_title=await page.title(),
                metadata={"selector": selector}
            )
            
            logger.debug(f"元素点击成功 | 选择器: {selector} | 耗时: {duration:.2f}s")
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            error_message = str(e)
            
            logger.error(f"元素点击失败 | 选择器: {selector} | 错误: {error_message}")
            
            return InteractionResult(
                interaction_type=InteractionType.CLICK,
                success=False,
                duration=duration,
                error_message=error_message,
                metadata={"selector": selector}
            )
    
    async def type_text(self, page: Any, selector: str, text: str,
                       human_like: bool = True) -> InteractionResult:
        """
        在元素中输入文本
        
        Args:
            page: 页面对象
            selector: 元素选择器
            text: 要输入的文本
            human_like: 是否使用人性化输入
            
        Returns:
            交互结果
        """
        start_time = time.time()
        
        try:
            logger.debug(f"输入文本: {selector} -> {text[:20]}...")
            
            # 等待元素可见
            await page.wait_for_selector(selector, timeout=self.default_timeout)
            
            # 聚焦元素
            await page.locator(selector).focus()
            
            # 清空现有内容
            await page.locator(selector).clear()
            
            # 人性化输入
            if human_like and self.human_delays:
                # 逐字符输入，模拟真实打字
                for char in text:
                    await page.locator(selector).type(char)
                    # 随机延迟
                    await asyncio.sleep(random.uniform(0.05, 0.15))
            else:
                # 快速输入
                await page.locator(selector).type(text)
            
            duration = time.time() - start_time
            
            result = InteractionResult(
                interaction_type=InteractionType.TYPE,
                success=True,
                duration=duration,
                page_url=page.url,
                page_title=await page.title(),
                metadata={"selector": selector, "text_length": len(text)}
            )
            
            logger.debug(f"文本输入成功 | 选择器: {selector} | 耗时: {duration:.2f}s")
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            error_message = str(e)
            
            logger.error(f"文本输入失败 | 选择器: {selector} | 错误: {error_message}")
            
            return InteractionResult(
                interaction_type=InteractionType.TYPE,
                success=False,
                duration=duration,
                error_message=error_message,
                metadata={"selector": selector}
            )
    
    async def scroll_page(self, page: Any, direction: str = "down", 
                         distance: int = 500, human_like: bool = True) -> InteractionResult:
        """
        滚动页面
        
        Args:
            page: 页面对象
            direction: 滚动方向 (up/down/left/right)
            distance: 滚动距离
            human_like: 是否使用人性化滚动
            
        Returns:
            交互结果
        """
        start_time = time.time()
        
        try:
            logger.debug(f"滚动页面: {direction} {distance}px")
            
            # 计算滚动参数
            if direction == "down":
                delta_y = distance
                delta_x = 0
            elif direction == "up":
                delta_y = -distance
                delta_x = 0
            elif direction == "right":
                delta_y = 0
                delta_x = distance
            elif direction == "left":
                delta_y = 0
                delta_x = -distance
            else:
                raise ValueError(f"不支持的滚动方向: {direction}")
            
            if human_like and self.human_delays:
                # 分段滚动，模拟真实滚动
                segments = random.randint(3, 8)
                segment_distance = distance // segments
                
                for i in range(segments):
                    segment_delta_y = (delta_y // segments) if delta_y != 0 else 0
                    segment_delta_x = (delta_x // segments) if delta_x != 0 else 0
                    
                    await page.mouse.wheel(segment_delta_x, segment_delta_y)
                    await asyncio.sleep(random.uniform(0.1, 0.3))
            else:
                # 一次性滚动
                await page.mouse.wheel(delta_x, delta_y)
            
            duration = time.time() - start_time
            
            result = InteractionResult(
                interaction_type=InteractionType.SCROLL,
                success=True,
                duration=duration,
                page_url=page.url,
                page_title=await page.title(),
                metadata={"direction": direction, "distance": distance}
            )
            
            logger.debug(f"页面滚动成功 | 方向: {direction} | 距离: {distance}px | 耗时: {duration:.2f}s")
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            error_message = str(e)
            
            logger.error(f"页面滚动失败 | 错误: {error_message}")
            
            return InteractionResult(
                interaction_type=InteractionType.SCROLL,
                success=False,
                duration=duration,
                error_message=error_message,
                metadata={"direction": direction, "distance": distance}
            )
    
    async def hover_element(self, page: Any, selector: str) -> InteractionResult:
        """
        悬停在元素上
        
        Args:
            page: 页面对象
            selector: 元素选择器
            
        Returns:
            交互结果
        """
        start_time = time.time()
        
        try:
            logger.debug(f"悬停元素: {selector}")
            
            # 等待元素可见
            await page.wait_for_selector(selector, timeout=self.default_timeout)
            
            # 执行悬停
            await page.locator(selector).hover()
            
            # 短暂等待
            if self.human_delays:
                await asyncio.sleep(random.uniform(0.5, 1.5))
            
            duration = time.time() - start_time
            
            result = InteractionResult(
                interaction_type=InteractionType.HOVER,
                success=True,
                duration=duration,
                page_url=page.url,
                page_title=await page.title(),
                metadata={"selector": selector}
            )
            
            logger.debug(f"元素悬停成功 | 选择器: {selector} | 耗时: {duration:.2f}s")
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            error_message = str(e)
            
            logger.error(f"元素悬停失败 | 选择器: {selector} | 错误: {error_message}")
            
            return InteractionResult(
                interaction_type=InteractionType.HOVER,
                success=False,
                duration=duration,
                error_message=error_message,
                metadata={"selector": selector}
            )
    
    async def wait_for_element(self, page: Any, selector: str, 
                             timeout: Optional[int] = None) -> InteractionResult:
        """
        等待元素出现
        
        Args:
            page: 页面对象
            selector: 元素选择器
            timeout: 超时时间（毫秒）
            
        Returns:
            交互结果
        """
        start_time = time.time()
        timeout = timeout or self.default_timeout
        
        try:
            logger.debug(f"等待元素: {selector}")
            
            await page.wait_for_selector(selector, timeout=timeout)
            
            duration = time.time() - start_time
            
            result = InteractionResult(
                interaction_type=InteractionType.WAIT,
                success=True,
                duration=duration,
                page_url=page.url,
                page_title=await page.title(),
                metadata={"selector": selector, "timeout": timeout}
            )
            
            logger.debug(f"元素等待成功 | 选择器: {selector} | 耗时: {duration:.2f}s")
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            error_message = str(e)
            
            logger.error(f"元素等待失败 | 选择器: {selector} | 错误: {error_message}")
            
            return InteractionResult(
                interaction_type=InteractionType.WAIT,
                success=False,
                duration=duration,
                error_message=error_message,
                metadata={"selector": selector, "timeout": timeout}
            )
    
    async def take_screenshot(self, page: Any, file_path: Optional[str] = None,
                            full_page: bool = False) -> ScreenshotResult:
        """
        截取页面截图
        
        Args:
            page: 页面对象
            file_path: 保存路径
            full_page: 是否截取整页
            
        Returns:
            截图结果
        """
        try:
            logger.debug(f"截取页面截图 | 路径: {file_path} | 整页: {full_page}")
            
            screenshot_options = {
                "full_page": full_page,
                "type": "png"
            }
            
            if file_path:
                screenshot_options["path"] = file_path
                image_data = None
            else:
                image_data = await page.screenshot(**screenshot_options)
            
            # 获取页面尺寸
            viewport = page.viewport_size
            
            result = ScreenshotResult(
                success=True,
                file_path=file_path,
                image_data=image_data,
                width=viewport["width"] if viewport else 0,
                height=viewport["height"] if viewport else 0,
                page_url=page.url
            )
            
            logger.debug(f"截图成功 | 路径: {file_path}")
            
            return result
            
        except Exception as e:
            error_message = str(e)
            
            logger.error(f"截图失败 | 错误: {error_message}")
            
            return ScreenshotResult(
                success=False,
                error_message=error_message
            )
    
    async def get_element_info(self, page: Any, selector: str) -> Optional[ElementInfo]:
        """
        获取元素信息
        
        Args:
            page: 页面对象
            selector: 元素选择器
            
        Returns:
            元素信息
        """
        try:
            # 等待元素存在
            await page.wait_for_selector(selector, timeout=5000)
            
            # 获取元素信息
            element_info = await page.evaluate("""
                (selector) => {
                    const element = document.querySelector(selector);
                    if (!element) return null;
                    
                    const rect = element.getBoundingClientRect();
                    const style = window.getComputedStyle(element);
                    
                    return {
                        tag_name: element.tagName.toLowerCase(),
                        text_content: element.textContent?.trim() || null,
                        inner_html: element.innerHTML,
                        x: rect.x,
                        y: rect.y,
                        width: rect.width,
                        height: rect.height,
                        is_visible: style.display !== 'none' && style.visibility !== 'hidden',
                        is_enabled: !element.disabled,
                        attributes: Array.from(element.attributes).reduce((acc, attr) => {
                            acc[attr.name] = attr.value;
                            return acc;
                        }, {})
                    };
                }
            """, selector)
            
            if element_info:
                return ElementInfo(
                    selector=selector,
                    tag_name=element_info["tag_name"],
                    text_content=element_info["text_content"],
                    inner_html=element_info["inner_html"],
                    x=element_info["x"],
                    y=element_info["y"],
                    width=element_info["width"],
                    height=element_info["height"],
                    is_visible=element_info["is_visible"],
                    is_enabled=element_info["is_enabled"],
                    attributes=element_info["attributes"],
                    is_clickable=element_info["is_visible"] and element_info["is_enabled"]
                )
            
            return None
            
        except Exception as e:
            logger.warning(f"获取元素信息失败 | 选择器: {selector} | 错误: {e}")
            return None
    
    async def evaluate_script(self, page: Any, script: str) -> InteractionResult:
        """
        执行JavaScript脚本
        
        Args:
            page: 页面对象
            script: JavaScript代码
            
        Returns:
            交互结果
        """
        start_time = time.time()
        
        try:
            logger.debug(f"执行脚本: {script[:50]}...")
            
            result_data = await page.evaluate(script)
            
            duration = time.time() - start_time
            
            result = InteractionResult(
                interaction_type=InteractionType.EVALUATE,
                success=True,
                duration=duration,
                result_data=result_data,
                page_url=page.url,
                page_title=await page.title(),
                metadata={"script_length": len(script)}
            )
            
            logger.debug(f"脚本执行成功 | 耗时: {duration:.2f}s")
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            error_message = str(e)
            
            logger.error(f"脚本执行失败 | 错误: {error_message}")
            
            return InteractionResult(
                interaction_type=InteractionType.EVALUATE,
                success=False,
                duration=duration,
                error_message=error_message,
                metadata={"script_length": len(script)}
            )
