"""
代理管理模块 - 代理健康检查器
负责检查代理的连通性、速度和匿名度
"""

import asyncio
import aiohttp
import time
import json
from typing import List, Dict, Any, Optional
from datetime import datetime

from .models import ProxyInfo, ProxyStatus, ProxyHealthReport
from ...core.exceptions import ProxyHealthCheckError
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("proxy_health_checker")


class ProxyHealthChecker:
    """代理健康检查器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化健康检查器
        
        Args:
            config: 健康检查配置
        """
        self.config = config
        self.enabled = config.get("enabled", True)
        self.timeout = config.get("timeout", 10)
        self.check_urls = config.get("check_urls", [
            "https://httpbin.org/ip",
            "https://api.ipify.org",
            "https://icanhazip.com"
        ])
        self.anonymity_check = config.get("anonymity_check", True)
        self.speed_threshold = config.get("speed_threshold", 5000)  # ms
        
    async def check_proxy(self, proxy: ProxyInfo) -> ProxyHealthReport:
        """
        检查单个代理的健康状态
        
        Args:
            proxy: 要检查的代理
            
        Returns:
            健康检查报告
        """
        if not self.enabled:
            return ProxyHealthReport(
                proxy_info=proxy,
                check_time=datetime.now(),
                is_healthy=True,
                check_details={"skipped": "健康检查已禁用"}
            )
        
        logger.debug(f"开始健康检查 | 代理: {proxy.ip}:{proxy.port}")
        
        start_time = time.time()
        check_details = {}
        is_healthy = False
        error_message = None
        response_time = None
        
        try:
            # 连通性检查
            connectivity_result = await self._check_connectivity(proxy)
            check_details["connectivity"] = connectivity_result
            
            if connectivity_result["success"]:
                response_time = connectivity_result["response_time"]
                
                # 速度检查
                speed_result = await self._check_speed(proxy)
                check_details["speed"] = speed_result
                
                # 匿名度检查
                if self.anonymity_check:
                    anonymity_result = await self._check_anonymity(proxy)
                    check_details["anonymity"] = anonymity_result
                else:
                    anonymity_result = {"success": True, "anonymous": True}
                
                # 综合判断健康状态
                is_healthy = (
                    connectivity_result["success"] and
                    speed_result["success"] and
                    anonymity_result["success"] and
                    response_time < self.speed_threshold
                )
                
                if not is_healthy:
                    reasons = []
                    if not connectivity_result["success"]:
                        reasons.append("连通性失败")
                    if not speed_result["success"]:
                        reasons.append("速度测试失败")
                    if not anonymity_result["success"]:
                        reasons.append("匿名度检查失败")
                    if response_time >= self.speed_threshold:
                        reasons.append(f"响应时间过长({response_time}ms)")
                    
                    error_message = "; ".join(reasons)
            else:
                error_message = connectivity_result.get("error", "连通性检查失败")
        
        except Exception as e:
            logger.error(f"健康检查异常 | 代理: {proxy.ip} | 错误: {e}")
            error_message = str(e)
            is_healthy = False
        
        # 更新代理状态
        if is_healthy:
            proxy.update_status(ProxyStatus.HEALTHY, response_time)
        else:
            proxy.update_status(ProxyStatus.UNHEALTHY, response_time)
        
        total_time = time.time() - start_time
        check_details["total_check_time"] = total_time
        
        logger.debug(
            f"健康检查完成 | 代理: {proxy.ip} | "
            f"状态: {'健康' if is_healthy else '不健康'} | "
            f"耗时: {total_time:.2f}s"
        )
        
        return ProxyHealthReport(
            proxy_info=proxy,
            check_time=datetime.now(),
            is_healthy=is_healthy,
            response_time=response_time,
            error_message=error_message,
            check_details=check_details
        )
    
    async def _check_connectivity(self, proxy: ProxyInfo) -> Dict[str, Any]:
        """检查代理连通性"""
        for url in self.check_urls:
            try:
                start_time = time.time()
                
                connector = aiohttp.ProxyConnector.from_url(proxy.proxy_url)
                
                async with aiohttp.ClientSession(
                    connector=connector,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as session:
                    async with session.get(url) as response:
                        response_time = (time.time() - start_time) * 1000
                        
                        if response.status == 200:
                            content = await response.text()
                            return {
                                "success": True,
                                "response_time": response_time,
                                "url": url,
                                "status_code": response.status,
                                "content_length": len(content)
                            }
            
            except Exception as e:
                logger.debug(f"连通性检查失败 | URL: {url} | 代理: {proxy.ip} | 错误: {e}")
                continue
        
        return {
            "success": False,
            "error": "所有检查URL都无法访问"
        }
    
    async def _check_speed(self, proxy: ProxyInfo) -> Dict[str, Any]:
        """检查代理速度"""
        try:
            # 使用较大的文件测试下载速度
            test_url = "https://httpbin.org/bytes/1024"  # 1KB测试文件
            
            start_time = time.time()
            
            connector = aiohttp.ProxyConnector.from_url(proxy.proxy_url)
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            ) as session:
                async with session.get(test_url) as response:
                    content = await response.read()
                    
                    download_time = time.time() - start_time
                    download_speed = len(content) / download_time  # bytes/second
                    
                    return {
                        "success": True,
                        "download_time": download_time,
                        "download_speed": download_speed,
                        "content_size": len(content)
                    }
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _check_anonymity(self, proxy: ProxyInfo) -> Dict[str, Any]:
        """检查代理匿名度"""
        try:
            # 检查是否泄露真实IP
            check_url = "https://httpbin.org/headers"
            
            connector = aiohttp.ProxyConnector.from_url(proxy.proxy_url)
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            ) as session:
                async with session.get(check_url) as response:
                    data = await response.json()
                    headers = data.get("headers", {})
                    
                    # 检查是否有泄露代理信息的头部
                    suspicious_headers = [
                        "X-Forwarded-For",
                        "X-Real-IP", 
                        "X-Originating-IP",
                        "Client-IP",
                        "Via"
                    ]
                    
                    leaked_headers = []
                    for header in suspicious_headers:
                        if header in headers:
                            leaked_headers.append(header)
                    
                    # 判断匿名度级别
                    if not leaked_headers:
                        anonymity_level = "elite"  # 精英级匿名
                    elif len(leaked_headers) <= 2:
                        anonymity_level = "anonymous"  # 匿名
                    else:
                        anonymity_level = "transparent"  # 透明代理
                    
                    return {
                        "success": True,
                        "anonymity_level": anonymity_level,
                        "leaked_headers": leaked_headers,
                        "anonymous": anonymity_level in ["elite", "anonymous"]
                    }
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def check_proxy_list(self, proxies: List[ProxyInfo]) -> List[ProxyHealthReport]:
        """
        批量检查代理列表
        
        Args:
            proxies: 代理列表
            
        Returns:
            健康检查报告列表
        """
        logger.info(f"开始批量健康检查 | 代理数量: {len(proxies)}")
        
        # 并发检查所有代理
        tasks = [self.check_proxy(proxy) for proxy in proxies]
        reports = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        valid_reports = []
        for i, report in enumerate(reports):
            if isinstance(report, Exception):
                logger.error(f"代理健康检查异常 | 代理: {proxies[i].ip} | 错误: {report}")
                # 创建失败报告
                valid_reports.append(ProxyHealthReport(
                    proxy_info=proxies[i],
                    check_time=datetime.now(),
                    is_healthy=False,
                    error_message=str(report)
                ))
            else:
                valid_reports.append(report)
        
        # 统计结果
        healthy_count = sum(1 for report in valid_reports if report.is_healthy)
        logger.info(
            f"批量健康检查完成 | 总数: {len(proxies)} | "
            f"健康: {healthy_count} | 不健康: {len(proxies) - healthy_count}"
        )
        
        return valid_reports
