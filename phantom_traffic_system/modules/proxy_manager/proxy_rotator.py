"""
代理管理模块 - 代理轮换器
负责根据不同策略轮换代理IP
"""

import asyncio
import random
import time
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict

from .models import ProxyInfo, ProxyStatus, RotationStrategy, ProxyRotationConfig
from ...core.exceptions import ProxyException
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("proxy_rotator")


class ProxyRotator:
    """代理轮换器"""
    
    def __init__(self, config: ProxyRotationConfig):
        """
        初始化代理轮换器
        
        Args:
            config: 轮换配置
        """
        self.config = config
        self.strategy = config.strategy
        
        # 代理池
        self.proxy_pool: List[ProxyInfo] = []
        self.healthy_proxies: List[ProxyInfo] = []
        self.unhealthy_proxies: List[ProxyInfo] = []
        
        # 会话管理
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        
        # 使用统计
        self.proxy_usage: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            "usage_count": 0,
            "success_count": 0,
            "failure_count": 0,
            "last_used": None,
            "cooldown_until": None
        })
        
        # 轮换状态
        self.last_rotation_time = datetime.now()
        self.rotation_index = 0
        
    def add_proxies(self, proxies: List[ProxyInfo]):
        """
        添加代理到代理池
        
        Args:
            proxies: 代理列表
        """
        for proxy in proxies:
            if proxy not in self.proxy_pool:
                self.proxy_pool.append(proxy)
                
                if proxy.is_healthy:
                    self.healthy_proxies.append(proxy)
                else:
                    self.unhealthy_proxies.append(proxy)
        
        logger.info(
            f"代理池更新 | 总数: {len(self.proxy_pool)} | "
            f"健康: {len(self.healthy_proxies)} | 不健康: {len(self.unhealthy_proxies)}"
        )
    
    def remove_proxy(self, proxy: ProxyInfo):
        """
        从代理池移除代理
        
        Args:
            proxy: 要移除的代理
        """
        if proxy in self.proxy_pool:
            self.proxy_pool.remove(proxy)
        
        if proxy in self.healthy_proxies:
            self.healthy_proxies.remove(proxy)
        
        if proxy in self.unhealthy_proxies:
            self.unhealthy_proxies.remove(proxy)
        
        # 清理使用统计
        proxy_key = f"{proxy.ip}:{proxy.port}"
        if proxy_key in self.proxy_usage:
            del self.proxy_usage[proxy_key]
        
        logger.debug(f"代理已移除 | IP: {proxy.ip}")
    
    async def get_proxy(self, session_id: Optional[str] = None, **kwargs) -> Optional[ProxyInfo]:
        """
        根据轮换策略获取代理
        
        Args:
            session_id: 会话ID
            **kwargs: 其他参数
            
        Returns:
            代理信息，如果没有可用代理则返回None
        """
        if not self.healthy_proxies:
            logger.warning("没有可用的健康代理")
            return None
        
        # 根据策略选择代理
        if self.strategy == RotationStrategy.HIGH_FREQUENCY:
            proxy = await self._get_proxy_high_frequency(**kwargs)
        elif self.strategy == RotationStrategy.SESSION_STICKY:
            proxy = await self._get_proxy_session_sticky(session_id, **kwargs)
        elif self.strategy == RotationStrategy.TIME_BASED:
            proxy = await self._get_proxy_time_based(**kwargs)
        elif self.strategy == RotationStrategy.REQUEST_COUNT:
            proxy = await self._get_proxy_request_count(**kwargs)
        else:
            proxy = await self._get_proxy_random(**kwargs)
        
        if proxy:
            self._record_proxy_usage(proxy, session_id)
            logger.debug(f"分配代理 | IP: {proxy.ip} | 策略: {self.strategy.value}")
        
        return proxy
    
    async def _get_proxy_high_frequency(self, **kwargs) -> Optional[ProxyInfo]:
        """高频轮换策略 - 每个请求换一个IP"""
        available_proxies = self._get_available_proxies()
        
        if not available_proxies:
            return None
        
        # 随机选择一个代理
        return random.choice(available_proxies)
    
    async def _get_proxy_session_sticky(self, session_id: Optional[str], **kwargs) -> Optional[ProxyInfo]:
        """会话粘性策略 - 会话期间保持同一IP"""
        if not session_id:
            # 如果没有会话ID，降级为随机选择
            return await self._get_proxy_random(**kwargs)
        
        # 检查是否已有活跃会话
        if session_id in self.active_sessions:
            session_info = self.active_sessions[session_id]
            proxy = session_info["proxy"]
            
            # 检查会话是否过期
            if datetime.now() < session_info["expires_at"] and proxy.is_healthy:
                return proxy
            else:
                # 会话过期，清理并分配新代理
                del self.active_sessions[session_id]
        
        # 分配新代理
        available_proxies = self._get_available_proxies()
        if not available_proxies:
            return None
        
        proxy = random.choice(available_proxies)
        
        # 创建新会话
        self.active_sessions[session_id] = {
            "proxy": proxy,
            "created_at": datetime.now(),
            "expires_at": datetime.now() + timedelta(seconds=self.config.session_duration)
        }
        
        return proxy
    
    async def _get_proxy_time_based(self, **kwargs) -> Optional[ProxyInfo]:
        """基于时间的轮换策略"""
        now = datetime.now()
        
        # 检查是否需要轮换
        if (now - self.last_rotation_time).total_seconds() >= self.config.rotation_interval:
            self.rotation_index = (self.rotation_index + 1) % len(self.healthy_proxies)
            self.last_rotation_time = now
        
        return self.healthy_proxies[self.rotation_index]
    
    async def _get_proxy_request_count(self, **kwargs) -> Optional[ProxyInfo]:
        """基于请求数量的轮换策略"""
        # 找到使用次数最少的代理
        available_proxies = self._get_available_proxies()
        if not available_proxies:
            return None
        
        # 按使用次数排序
        sorted_proxies = sorted(
            available_proxies,
            key=lambda p: self.proxy_usage[f"{p.ip}:{p.port}"]["usage_count"]
        )
        
        return sorted_proxies[0]
    
    async def _get_proxy_random(self, **kwargs) -> Optional[ProxyInfo]:
        """随机选择策略"""
        available_proxies = self._get_available_proxies()
        
        if not available_proxies:
            return None
        
        return random.choice(available_proxies)
    
    def _get_available_proxies(self) -> List[ProxyInfo]:
        """获取可用代理列表"""
        now = datetime.now()
        available = []
        
        for proxy in self.healthy_proxies:
            proxy_key = f"{proxy.ip}:{proxy.port}"
            usage_info = self.proxy_usage[proxy_key]
            
            # 检查是否在冷却期
            if usage_info["cooldown_until"] and now < usage_info["cooldown_until"]:
                continue
            
            # 检查是否超过最大使用次数
            if usage_info["usage_count"] >= self.config.max_requests_per_proxy:
                # 设置冷却期
                usage_info["cooldown_until"] = now + timedelta(seconds=self.config.cooldown_period)
                continue
            
            # 检查失败次数
            if usage_info["failure_count"] >= self.config.max_failures:
                # 设置冷却期
                usage_info["cooldown_until"] = now + timedelta(seconds=self.config.cooldown_period)
                continue
            
            available.append(proxy)
        
        return available
    
    def _record_proxy_usage(self, proxy: ProxyInfo, session_id: Optional[str] = None):
        """记录代理使用情况"""
        proxy_key = f"{proxy.ip}:{proxy.port}"
        usage_info = self.proxy_usage[proxy_key]
        
        usage_info["usage_count"] += 1
        usage_info["last_used"] = datetime.now()
        
        proxy.increment_usage()
        
        logger.debug(
            f"代理使用记录 | IP: {proxy.ip} | "
            f"使用次数: {usage_info['usage_count']} | 会话: {session_id}"
        )
    
    def record_proxy_result(self, proxy: ProxyInfo, success: bool, error: Optional[str] = None):
        """
        记录代理使用结果
        
        Args:
            proxy: 代理信息
            success: 是否成功
            error: 错误信息
        """
        proxy_key = f"{proxy.ip}:{proxy.port}"
        usage_info = self.proxy_usage[proxy_key]
        
        if success:
            usage_info["success_count"] += 1
        else:
            usage_info["failure_count"] += 1
        
        # 更新代理成功率
        proxy.update_success_rate(success)
        
        # 如果失败次数过多，标记为不健康
        if usage_info["failure_count"] >= self.config.max_failures:
            proxy.update_status(ProxyStatus.UNHEALTHY)
            if proxy in self.healthy_proxies:
                self.healthy_proxies.remove(proxy)
                self.unhealthy_proxies.append(proxy)
        
        logger.debug(
            f"代理结果记录 | IP: {proxy.ip} | 成功: {success} | "
            f"成功率: {proxy.success_rate:.2f} | 错误: {error}"
        )
    
    def cleanup_expired_sessions(self):
        """清理过期会话"""
        now = datetime.now()
        expired_sessions = []
        
        for session_id, session_info in self.active_sessions.items():
            if now >= session_info["expires_at"]:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del self.active_sessions[session_id]
        
        if expired_sessions:
            logger.debug(f"清理过期会话 | 数量: {len(expired_sessions)}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取轮换器统计信息"""
        total_usage = sum(info["usage_count"] for info in self.proxy_usage.values())
        total_success = sum(info["success_count"] for info in self.proxy_usage.values())
        total_failure = sum(info["failure_count"] for info in self.proxy_usage.values())
        
        return {
            "strategy": self.strategy.value,
            "total_proxies": len(self.proxy_pool),
            "healthy_proxies": len(self.healthy_proxies),
            "unhealthy_proxies": len(self.unhealthy_proxies),
            "active_sessions": len(self.active_sessions),
            "total_usage": total_usage,
            "total_success": total_success,
            "total_failure": total_failure,
            "overall_success_rate": total_success / max(total_usage, 1) * 100
        }
