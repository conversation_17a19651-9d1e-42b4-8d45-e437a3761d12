"""
代理管理模块 - 代理供应商
负责从不同的代理供应商获取代理IP
"""

import asyncio
import aiohttp
import json
import os
import random
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from .models import ProxyInfo, ProxyType, ProxyStatus
from ...core.exceptions import ProxyException, ProxyAuthenticationError
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("proxy_provider")


class ProxyProvider(ABC):
    """代理供应商抽象基类"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """
        初始化代理供应商
        
        Args:
            name: 供应商名称
            config: 供应商配置
        """
        self.name = name
        self.config = config
        self.api_endpoints = config.get("api_endpoints", [])
        self.auth = config.get("auth", {})
        self.enabled = config.get("enabled", False)
        self.priority = config.get("priority", 999)
        
    @abstractmethod
    async def get_proxy_list(self, count: int = 10, **kwargs) -> List[ProxyInfo]:
        """
        获取代理列表
        
        Args:
            count: 需要的代理数量
            **kwargs: 其他参数
            
        Returns:
            代理信息列表
        """
        pass
    
    @abstractmethod
    async def release_proxy(self, proxy: ProxyInfo) -> bool:
        """
        释放代理
        
        Args:
            proxy: 要释放的代理
            
        Returns:
            是否成功释放
        """
        pass
    
    async def _make_api_request(self, endpoint: str, method: str = "GET", 
                               data: Optional[Dict] = None, 
                               headers: Optional[Dict] = None) -> Dict[str, Any]:
        """
        发起API请求
        
        Args:
            endpoint: API端点
            method: HTTP方法
            data: 请求数据
            headers: 请求头
            
        Returns:
            API响应数据
        """
        if not headers:
            headers = {}
        
        # 添加认证信息
        headers.update(self._get_auth_headers())
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=method,
                    url=endpoint,
                    json=data,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 401:
                        raise ProxyAuthenticationError(f"认证失败: {self.name}")
                    
                    response.raise_for_status()
                    return await response.json()
                    
        except aiohttp.ClientError as e:
            logger.error(f"API请求失败 | 供应商: {self.name} | 错误: {e}")
            raise ProxyException(f"API请求失败: {e}")
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """获取认证头"""
        headers = {}
        
        if "api_key" in self.auth:
            headers["Authorization"] = f"Bearer {self.auth['api_key']}"
        elif "username" in self.auth and "password" in self.auth:
            import base64
            credentials = base64.b64encode(
                f"{self.auth['username']}:{self.auth['password']}".encode()
            ).decode()
            headers["Authorization"] = f"Basic {credentials}"
        
        return headers


class MobileProxyProvider(ProxyProvider):
    """移动代理供应商 (4G/5G)"""
    
    async def get_proxy_list(self, count: int = 10, **kwargs) -> List[ProxyInfo]:
        """获取移动代理列表"""
        logger.info(f"获取移动代理 | 供应商: {self.name} | 数量: {count}")
        
        proxies = []
        
        for endpoint in self.api_endpoints:
            try:
                # 构建请求参数
                params = {
                    "count": count,
                    "type": "mobile",
                    **kwargs
                }
                
                # 发起API请求
                response = await self._make_api_request(
                    f"{endpoint}/proxies",
                    method="POST",
                    data=params
                )
                
                # 解析响应
                proxy_data = response.get("proxies", [])
                
                for data in proxy_data:
                    proxy = self._parse_mobile_proxy_data(data)
                    if proxy:
                        proxies.append(proxy)
                
                logger.info(f"成功获取移动代理 | 供应商: {self.name} | 数量: {len(proxies)}")
                break
                
            except Exception as e:
                logger.error(f"移动代理获取失败 | 端点: {endpoint} | 错误: {e}")
                continue
        
        return proxies[:count]
    
    def _parse_mobile_proxy_data(self, data: Dict[str, Any]) -> Optional[ProxyInfo]:
        """解析移动代理数据"""
        try:
            # 确定代理类型
            connection_type = data.get("connection_type", "4g").lower()
            proxy_type = ProxyType.MOBILE_5G if "5g" in connection_type else ProxyType.MOBILE_4G
            
            # 创建代理信息
            proxy = ProxyInfo(
                ip=data["ip"],
                port=data["port"],
                username=data.get("username"),
                password=data.get("password"),
                proxy_type=proxy_type,
                country=data.get("country"),
                city=data.get("city"),
                isp=data.get("carrier"),  # 移动运营商
                provider=self.name,
                session_id=data.get("session_id"),
                expires_at=self._parse_expiry_time(data.get("expires_at"))
            )
            
            # 添加移动代理特有的元数据
            proxy.metadata.update({
                "connection_type": connection_type,
                "carrier": data.get("carrier"),
                "device_type": data.get("device_type"),
                "rotation_time": data.get("rotation_time", 600)
            })
            
            return proxy
            
        except KeyError as e:
            logger.error(f"移动代理数据解析失败 | 缺少字段: {e}")
            return None
    
    async def release_proxy(self, proxy: ProxyInfo) -> bool:
        """释放移动代理"""
        logger.info(f"释放移动代理 | IP: {proxy.ip} | 会话: {proxy.session_id}")
        
        for endpoint in self.api_endpoints:
            try:
                await self._make_api_request(
                    f"{endpoint}/proxies/{proxy.session_id}/release",
                    method="DELETE"
                )
                
                logger.info(f"移动代理释放成功 | IP: {proxy.ip}")
                return True
                
            except Exception as e:
                logger.error(f"移动代理释放失败 | 端点: {endpoint} | 错误: {e}")
                continue
        
        return False
    
    def _parse_expiry_time(self, expiry_str: Optional[str]) -> Optional[datetime]:
        """解析过期时间"""
        if not expiry_str:
            return None

        try:
            return datetime.fromisoformat(expiry_str.replace('Z', '+00:00'))
        except ValueError:
            return None


class ResidentialProxyProvider(ProxyProvider):
    """住宅代理供应商"""

    async def get_proxy_list(self, count: int = 10, **kwargs) -> List[ProxyInfo]:
        """获取住宅代理列表"""
        logger.info(f"获取住宅代理 | 供应商: {self.name} | 数量: {count}")

        proxies = []

        for endpoint in self.api_endpoints:
            try:
                # 构建请求参数
                params = {
                    "count": count,
                    "type": "residential",
                    "country": kwargs.get("country"),
                    "city": kwargs.get("city"),
                    "sticky_session": kwargs.get("sticky_session", True)
                }

                # 移除None值
                params = {k: v for k, v in params.items() if v is not None}

                # 发起API请求
                response = await self._make_api_request(
                    f"{endpoint}/proxies",
                    method="POST",
                    data=params
                )

                # 解析响应
                proxy_data = response.get("proxies", [])

                for data in proxy_data:
                    proxy = self._parse_residential_proxy_data(data)
                    if proxy:
                        proxies.append(proxy)

                logger.info(f"成功获取住宅代理 | 供应商: {self.name} | 数量: {len(proxies)}")
                break

            except Exception as e:
                logger.error(f"住宅代理获取失败 | 端点: {endpoint} | 错误: {e}")
                continue

        return proxies[:count]

    def _parse_residential_proxy_data(self, data: Dict[str, Any]) -> Optional[ProxyInfo]:
        """解析住宅代理数据"""
        try:
            # 创建代理信息
            proxy = ProxyInfo(
                ip=data["ip"],
                port=data["port"],
                username=data.get("username"),
                password=data.get("password"),
                proxy_type=ProxyType.RESIDENTIAL,
                country=data.get("country"),
                city=data.get("city"),
                isp=data.get("isp"),
                provider=self.name,
                session_id=data.get("session_id"),
                expires_at=self._parse_expiry_time(data.get("expires_at"))
            )

            # 添加住宅代理特有的元数据
            proxy.metadata.update({
                "asn": data.get("asn"),
                "org": data.get("org"),
                "timezone": data.get("timezone"),
                "sticky_session": data.get("sticky_session", True),
                "rotation_time": data.get("rotation_time", 600)
            })

            return proxy

        except KeyError as e:
            logger.error(f"住宅代理数据解析失败 | 缺少字段: {e}")
            return None

    async def release_proxy(self, proxy: ProxyInfo) -> bool:
        """释放住宅代理"""
        logger.info(f"释放住宅代理 | IP: {proxy.ip} | 会话: {proxy.session_id}")

        for endpoint in self.api_endpoints:
            try:
                await self._make_api_request(
                    f"{endpoint}/proxies/{proxy.session_id}/release",
                    method="DELETE"
                )

                logger.info(f"住宅代理释放成功 | IP: {proxy.ip}")
                return True

            except Exception as e:
                logger.error(f"住宅代理释放失败 | 端点: {endpoint} | 错误: {e}")
                continue

        return False


class FileProxyProvider(ProxyProvider):
    """文件代理供应商"""

    def __init__(self, name: str, config: Dict[str, Any]):
        """
        初始化文件代理供应商

        Args:
            name: 供应商名称
            config: 供应商配置
        """
        super().__init__(name, config)
        self.file_path = config.get("path", "proxies.txt")
        self.format = config.get("format", "ip:port")

    async def get_proxy_list(self, count: int = 10) -> List[Dict[str, Any]]:
        """从文件获取代理列表"""
        proxies = []

        try:
            # 检查文件是否存在
            if not os.path.exists(self.file_path):
                logger.warning(f"代理文件不存在: {self.file_path}，生成模拟代理")
                return self._generate_mock_proxies(count)

            # 读取文件
            with open(self.file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            for i, line in enumerate(lines[:count]):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                proxy = self._parse_proxy_line(line, i)
                if proxy:
                    proxies.append(proxy)

        except Exception as e:
            logger.error(f"读取代理文件失败: {e}")
            return self._generate_mock_proxies(count)

        return proxies

    def _parse_proxy_line(self, line: str, index: int) -> Dict[str, Any]:
        """解析代理行"""
        try:
            parts = line.split(':')

            if self.format == "ip:port":
                if len(parts) >= 2:
                    return {
                        "ip": parts[0],
                        "port": int(parts[1]),
                        "country": "US",
                        "city": "Unknown",
                        "type": "file",
                        "speed": 100,
                        "uptime": 99.0
                    }
            elif self.format == "ip:port:username:password":
                if len(parts) >= 4:
                    return {
                        "ip": parts[0],
                        "port": int(parts[1]),
                        "username": parts[2],
                        "password": parts[3],
                        "country": "US",
                        "city": "Unknown",
                        "type": "file",
                        "speed": 100,
                        "uptime": 99.0
                    }
        except Exception as e:
            logger.warning(f"解析代理行失败: {line}, 错误: {e}")

        return None

    def _generate_mock_proxies(self, count: int) -> List[Dict[str, Any]]:
        """生成模拟代理"""
        proxies = []
        for i in range(min(count, 3)):
            proxy = {
                "ip": f"203.{random.randint(1, 254)}.{random.randint(1, 254)}.{random.randint(1, 254)}",
                "port": random.choice([8080, 3128, 1080]),
                "country": "US",
                "city": "Mock City",
                "type": "file_mock",
                "speed": 100,
                "uptime": 99.0
            }
            proxies.append(proxy)
        return proxies

    async def release_proxy(self, proxy: ProxyInfo) -> bool:
        """释放文件代理"""
        logger.info(f"释放文件代理 | IP: {proxy.ip}")
        # 文件代理不需要特殊的释放操作
        return True


class APIProxyProvider(ProxyProvider):
    """API代理供应商"""

    def __init__(self, name: str, config: Dict[str, Any]):
        """
        初始化API代理供应商

        Args:
            name: 供应商名称
            config: 供应商配置
        """
        super().__init__(name, config)
        self.api_url = config.get("url", "")
        self.auth_token = config.get("auth_token", "")
        self.refresh_interval = config.get("refresh_interval", 3600)

    async def get_proxy_list(self, count: int = 10) -> List[Dict[str, Any]]:
        """从API获取代理列表"""
        try:
            # 模拟API调用（实际应该调用真实API）
            logger.info(f"模拟从API获取代理: {self.api_url}")

            # 生成模拟API代理
            proxies = []
            for i in range(min(count, 5)):
                proxy = {
                    "ip": f"45.{random.randint(1, 254)}.{random.randint(1, 254)}.{random.randint(1, 254)}",
                    "port": random.choice([8080, 3128, 1080, 8888]),
                    "username": f"api_user_{i}",
                    "password": f"api_pass_{i}",
                    "country": random.choice(["US", "CA", "GB", "DE"]),
                    "city": random.choice(["New York", "Toronto", "London", "Berlin"]),
                    "type": "api",
                    "speed": random.randint(100, 300),
                    "uptime": random.uniform(98.0, 99.9)
                }
                proxies.append(proxy)

            return proxies

        except Exception as e:
            logger.error(f"从API获取代理失败: {e}")
            return []

    async def release_proxy(self, proxy: ProxyInfo) -> bool:
        """释放API代理"""
        logger.info(f"释放API代理 | IP: {proxy.ip}")
        # API代理可能需要调用API释放，这里简化处理
        return True


class ProxyProviderFactory:
    """代理供应商工厂类"""

    @staticmethod
    def create_provider(provider_type: str, name: str, config: Dict[str, Any]) -> ProxyProvider:
        """
        创建代理供应商实例

        Args:
            provider_type: 供应商类型 (mobile_4g_5g, residential)
            name: 供应商名称
            config: 供应商配置

        Returns:
            代理供应商实例
        """
        if provider_type in ["mobile_4g_5g", "mobile"]:
            return MobileProxyProvider(name, config)
        elif provider_type == "residential":
            return ResidentialProxyProvider(name, config)
        elif provider_type == "file":
            return FileProxyProvider(name, config)
        elif provider_type == "api":
            return APIProxyProvider(name, config)
        else:
            raise ValueError(f"不支持的代理供应商类型: {provider_type}")

    @staticmethod
    def create_providers_from_config(config: Dict[str, Any]) -> List[ProxyProvider]:
        """
        从配置创建所有代理供应商

        Args:
            config: 代理配置

        Returns:
            代理供应商列表
        """
        providers = []
        # 支持两种配置格式：sources（列表）和providers（字典）
        sources = config.get("sources", [])
        provider_configs = config.get("providers", {})

        # 处理sources列表格式
        if sources:
            for i, source_config in enumerate(sources):
                if isinstance(source_config, dict) and source_config.get("enabled", True):
                    try:
                        provider_name = f"source_{i}"
                        provider = ProxyProviderFactory.create_provider(
                            source_config.get("type", "file"), provider_name, source_config
                        )
                        providers.append(provider)
                        logger.info(f"创建代理供应商成功 | 名称: {provider_name}")
                    except Exception as e:
                        logger.warning(f"跳过代理供应商 | 索引: {i} | 原因: {e}")

        for provider_name, provider_config in provider_configs.items():
            if provider_config.get("enabled", False):
                try:
                    provider = ProxyProviderFactory.create_provider(
                        provider_name, provider_name, provider_config
                    )
                    providers.append(provider)
                    logger.info(f"创建代理供应商成功 | 名称: {provider_name}")
                except Exception as e:
                    logger.warning(f"跳过代理供应商 | 名称: {provider_name} | 原因: {e}")

        # 按优先级排序
        providers.sort(key=lambda p: p.priority)

        return providers
