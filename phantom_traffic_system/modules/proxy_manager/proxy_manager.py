"""
代理管理模块 - 代理管理器
统一管理代理供应商、健康检查和轮换策略
"""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from .models import ProxyInfo, ProxyStatus, RotationStrategy, ProxyRotationConfig
from .proxy_provider import <PERSON>xy<PERSON><PERSON><PERSON>Factory
from .proxy_health_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .proxy_rotator import ProxyRotator
from ...core.exceptions import ProxyException
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("proxy_manager")


class ProxyManager:
    """代理管理器 - 代理系统的中央控制器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化代理管理器
        
        Args:
            config: 代理配置
        """
        self.config = config
        
        # 初始化组件
        self.providers = ProxyProviderFactory.create_providers_from_config(config)
        self.health_checker = ProxyHealthChecker(config.get("health_check", {}))
        
        # 初始化轮换器
        rotation_config = self._create_rotation_config()
        self.rotator = ProxyRotator(rotation_config)
        
        # 状态管理
        self.is_running = False
        self.last_refresh_time = None
        self.refresh_interval = 300  # 5分钟刷新一次代理池
        
        # 统计信息
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "proxy_switches": 0,
            "last_update": datetime.now()
        }
        
        logger.info(f"代理管理器初始化完成 | 供应商数量: {len(self.providers)}")
    
    def _create_rotation_config(self) -> ProxyRotationConfig:
        """创建轮换配置"""
        # 安全地获取provider配置
        provider_configs = list(self.config.get("providers", {}).values())
        provider_config = provider_configs[0] if provider_configs else {}

        strategy_name = provider_config.get("rotation_strategy", "session_sticky")
        strategy = RotationStrategy(strategy_name)

        return ProxyRotationConfig(
            strategy=strategy,
            session_duration=provider_config.get("session_duration", 600),
            max_requests_per_proxy=100,
            rotation_interval=300,
            health_check_interval=60,
            max_failures=3,
            cooldown_period=1800
        )
    
    async def start(self):
        """启动代理管理器"""
        if self.is_running:
            logger.warning("代理管理器已在运行")
            return
        
        self.is_running = True
        logger.info("启动代理管理器")
        
        # 初始化代理池
        await self.refresh_proxy_pool()
        
        # 启动后台任务
        asyncio.create_task(self._background_tasks())
    
    async def stop(self):
        """停止代理管理器"""
        self.is_running = False
        logger.info("停止代理管理器")
    
    async def get_proxy(self, session_id: Optional[str] = None, **kwargs) -> Optional[ProxyInfo]:
        """
        获取可用代理
        
        Args:
            session_id: 会话ID
            **kwargs: 其他参数
            
        Returns:
            代理信息
        """
        if not self.is_running:
            await self.start()
        
        proxy = await self.rotator.get_proxy(session_id, **kwargs)

        if proxy:
            self.stats["total_requests"] += 1
            logger.debug(f"分配代理 | IP: {proxy.ip} | 会话: {session_id}")
        else:
            # 如果没有配置代理，返回一个模拟的本地代理
            if not self.providers:
                proxy = {
                    "ip": "127.0.0.1",
                    "port": "8080",
                    "country": "Local",
                    "city": "Local",
                    "type": "mock"
                }
                logger.debug("使用模拟代理（测试模式）")
            else:
                logger.warning("没有可用代理")

        return proxy
    
    async def release_proxy(self, proxy: ProxyInfo, success: bool = True, error: Optional[str] = None):
        """
        释放代理并记录使用结果
        
        Args:
            proxy: 要释放的代理
            success: 是否成功使用
            error: 错误信息
        """
        # 记录使用结果
        self.rotator.record_proxy_result(proxy, success, error)
        
        # 更新统计
        if success:
            self.stats["successful_requests"] += 1
        else:
            self.stats["failed_requests"] += 1
        
        # 如果代理有会话ID，尝试释放
        if proxy.session_id:
            for provider in self.providers:
                try:
                    await provider.release_proxy(proxy)
                    break
                except Exception as e:
                    logger.debug(f"代理释放失败 | 供应商: {provider.name} | 错误: {e}")
        
        logger.debug(f"代理释放 | IP: {proxy.ip} | 成功: {success}")
    
    async def refresh_proxy_pool(self, force: bool = False):
        """
        刷新代理池
        
        Args:
            force: 是否强制刷新
        """
        now = datetime.now()
        
        # 检查是否需要刷新
        if not force and self.last_refresh_time:
            if (now - self.last_refresh_time).total_seconds() < self.refresh_interval:
                return
        
        logger.info("开始刷新代理池")

        new_proxies = []

        # 如果没有配置供应商，跳过刷新
        if not self.providers:
            logger.warning("未配置代理供应商，跳过代理池刷新")
            self.last_refresh_time = now
            return

        # 从所有供应商获取代理
        for provider in self.providers:
            if not provider.enabled:
                continue

            try:
                proxies = await provider.get_proxy_list(count=20)
                new_proxies.extend(proxies)
                logger.info(f"从供应商获取代理 | 供应商: {provider.name} | 数量: {len(proxies)}")
            except Exception as e:
                logger.error(f"从供应商获取代理失败 | 供应商: {provider.name} | 错误: {e}")
        
        if new_proxies:
            # 健康检查
            health_reports = await self.health_checker.check_proxy_list(new_proxies)
            
            # 添加健康的代理到轮换器
            healthy_proxies = [
                report.proxy_info for report in health_reports 
                if report.is_healthy
            ]
            
            self.rotator.add_proxies(healthy_proxies)
            
            logger.info(
                f"代理池刷新完成 | 新增: {len(new_proxies)} | "
                f"健康: {len(healthy_proxies)} | 不健康: {len(new_proxies) - len(healthy_proxies)}"
            )
        else:
            logger.warning("未获取到新代理")
        
        self.last_refresh_time = now
    
    async def health_check_all(self):
        """对所有代理进行健康检查"""
        logger.info("开始全量健康检查")
        
        all_proxies = self.rotator.proxy_pool.copy()
        
        if not all_proxies:
            logger.warning("代理池为空，跳过健康检查")
            return
        
        health_reports = await self.health_checker.check_proxy_list(all_proxies)
        
        # 更新代理状态
        healthy_count = 0
        unhealthy_count = 0
        
        for report in health_reports:
            if report.is_healthy:
                healthy_count += 1
            else:
                unhealthy_count += 1
                # 从健康代理列表中移除
                if report.proxy_info in self.rotator.healthy_proxies:
                    self.rotator.healthy_proxies.remove(report.proxy_info)
                    self.rotator.unhealthy_proxies.append(report.proxy_info)
        
        logger.info(
            f"全量健康检查完成 | 总数: {len(all_proxies)} | "
            f"健康: {healthy_count} | 不健康: {unhealthy_count}"
        )
    
    async def _background_tasks(self):
        """后台任务"""
        logger.info("启动后台任务")
        
        while self.is_running:
            try:
                # 清理过期会话
                self.rotator.cleanup_expired_sessions()
                
                # 定期刷新代理池
                await self.refresh_proxy_pool()
                
                # 定期健康检查
                await self.health_check_all()
                
                # 等待一段时间
                await asyncio.sleep(60)  # 每分钟执行一次
                
            except Exception as e:
                logger.error(f"后台任务异常: {e}")
                await asyncio.sleep(10)  # 异常时短暂等待
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        rotator_stats = self.rotator.get_statistics()
        
        success_rate = 0
        if self.stats["total_requests"] > 0:
            success_rate = self.stats["successful_requests"] / self.stats["total_requests"] * 100
        
        return {
            "proxy_manager": {
                "total_requests": self.stats["total_requests"],
                "successful_requests": self.stats["successful_requests"],
                "failed_requests": self.stats["failed_requests"],
                "success_rate": success_rate,
                "proxy_switches": self.stats["proxy_switches"],
                "last_update": self.stats["last_update"].isoformat()
            },
            "rotator": rotator_stats,
            "providers": [
                {
                    "name": provider.name,
                    "enabled": provider.enabled,
                    "priority": provider.priority
                }
                for provider in self.providers
            ]
        }
    
    async def switch_proxy(self, current_proxy: ProxyInfo, session_id: Optional[str] = None) -> Optional[ProxyInfo]:
        """
        切换代理
        
        Args:
            current_proxy: 当前代理
            session_id: 会话ID
            
        Returns:
            新代理
        """
        # 标记当前代理为不健康
        await self.release_proxy(current_proxy, success=False, error="主动切换")
        
        # 获取新代理
        new_proxy = await self.get_proxy(session_id)
        
        if new_proxy:
            self.stats["proxy_switches"] += 1
            logger.info(f"代理切换 | {current_proxy.ip} -> {new_proxy.ip}")
        
        return new_proxy
