"""
幻影流量系统 - 代理管理模块
负责管理高质量代理IP，包括4G/5G移动代理和住宅代理
"""

from .proxy_manager import ProxyManager
from .proxy_provider import ProxyProvider, MobileProxyProvider, ResidentialProxyProvider
from .proxy_health_checker import Proxy<PERSON><PERSON>thChecker
from .proxy_rotator import ProxyRotator
from .models import ProxyInfo, ProxyStatus, RotationStrategy

__all__ = [
    "ProxyManager",
    "ProxyProvider",
    "MobileProxyProvider", 
    "ResidentialProxyProvider",
    "ProxyHealthChecker",
    "ProxyRotator",
    "ProxyInfo",
    "ProxyStatus",
    "RotationStrategy"
]
