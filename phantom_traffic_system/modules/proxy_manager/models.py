"""
代理管理模块 - 数据模型
定义代理相关的数据结构和枚举
"""

from enum import Enum
from dataclasses import dataclass
from typing import Optional, Dict, Any
from datetime import datetime


class ProxyStatus(Enum):
    """代理状态枚举"""
    UNKNOWN = "unknown"
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    BANNED = "banned"
    TIMEOUT = "timeout"
    AUTH_FAILED = "auth_failed"


class ProxyType(Enum):
    """代理类型枚举"""
    MOBILE_4G = "mobile_4g"
    MOBILE_5G = "mobile_5g"
    RESIDENTIAL = "residential"
    DATACENTER = "datacenter"


class RotationStrategy(Enum):
    """代理轮换策略枚举"""
    HIGH_FREQUENCY = "high_frequency"  # 每个请求换一个IP
    SESSION_STICKY = "session_sticky"  # 会话期间保持同一IP
    TIME_BASED = "time_based"          # 基于时间轮换
    REQUEST_COUNT = "request_count"    # 基于请求数量轮换


@dataclass
class ProxyInfo:
    """代理信息数据类"""
    ip: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    proxy_type: ProxyType = ProxyType.RESIDENTIAL
    country: Optional[str] = None
    city: Optional[str] = None
    isp: Optional[str] = None
    status: ProxyStatus = ProxyStatus.UNKNOWN
    last_check: Optional[datetime] = None
    response_time: Optional[float] = None  # 响应时间(毫秒)
    success_rate: float = 0.0  # 成功率
    usage_count: int = 0  # 使用次数
    provider: Optional[str] = None
    session_id: Optional[str] = None
    created_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.created_at is None:
            self.created_at = datetime.now()
    
    @property
    def proxy_url(self) -> str:
        """获取代理URL"""
        if self.username and self.password:
            return f"http://{self.username}:{self.password}@{self.ip}:{self.port}"
        return f"http://{self.ip}:{self.port}"
    
    @property
    def is_healthy(self) -> bool:
        """检查代理是否健康"""
        return self.status == ProxyStatus.HEALTHY
    
    @property
    def is_expired(self) -> bool:
        """检查代理是否过期"""
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at
    
    def update_status(self, status: ProxyStatus, response_time: Optional[float] = None):
        """更新代理状态"""
        self.status = status
        self.last_check = datetime.now()
        if response_time is not None:
            self.response_time = response_time
    
    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
    
    def update_success_rate(self, success: bool):
        """更新成功率"""
        if self.usage_count == 0:
            self.success_rate = 1.0 if success else 0.0
        else:
            # 使用移动平均计算成功率
            weight = 0.1  # 新数据的权重
            new_rate = 1.0 if success else 0.0
            self.success_rate = (1 - weight) * self.success_rate + weight * new_rate
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "ip": self.ip,
            "port": self.port,
            "username": self.username,
            "password": self.password,
            "proxy_type": self.proxy_type.value,
            "country": self.country,
            "city": self.city,
            "isp": self.isp,
            "status": self.status.value,
            "last_check": self.last_check.isoformat() if self.last_check else None,
            "response_time": self.response_time,
            "success_rate": self.success_rate,
            "usage_count": self.usage_count,
            "provider": self.provider,
            "session_id": self.session_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ProxyInfo":
        """从字典创建实例"""
        # 处理日期时间字段
        for field in ["last_check", "created_at", "expires_at"]:
            if data.get(field):
                data[field] = datetime.fromisoformat(data[field])
        
        # 处理枚举字段
        if "proxy_type" in data:
            data["proxy_type"] = ProxyType(data["proxy_type"])
        if "status" in data:
            data["status"] = ProxyStatus(data["status"])
        
        return cls(**data)


@dataclass
class ProxyHealthReport:
    """代理健康检查报告"""
    proxy_info: ProxyInfo
    check_time: datetime
    is_healthy: bool
    response_time: Optional[float] = None
    error_message: Optional[str] = None
    check_details: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.check_details is None:
            self.check_details = {}


@dataclass
class ProxyRotationConfig:
    """代理轮换配置"""
    strategy: RotationStrategy
    session_duration: int = 600  # 会话持续时间(秒)
    max_requests_per_proxy: int = 100  # 每个代理最大请求数
    rotation_interval: int = 300  # 轮换间隔(秒)
    health_check_interval: int = 60  # 健康检查间隔(秒)
    max_failures: int = 3  # 最大失败次数
    cooldown_period: int = 1800  # 冷却期(秒)
