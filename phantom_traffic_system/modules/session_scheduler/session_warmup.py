"""
会话调度模块 - 会话预热器
在访问目标页面前进行会话预热，建立合法的访问历史
"""

import asyncio
import random
from typing import List, Dict, Any, Optional
from datetime import datetime

from .models import Session, WarmupStep, WarmupStepType
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("session_warmup")


class SessionWarmup:
    """会话预热器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化会话预热器
        
        Args:
            config: 预热配置
        """
        self.config = config
        self.enabled = config.get("enabled", True)
        self.default_steps = config.get("steps", [])
        
        # 预定义的预热网站
        self.warmup_sites = {
            "search_engines": [
                "https://www.google.com",
                "https://www.bing.com",
                "https://duckduckgo.com"
            ],
            "news_sites": [
                "https://news.google.com",
                "https://www.bbc.com/news",
                "https://www.cnn.com",
                "https://www.reuters.com"
            ],
            "social_media": [
                "https://www.reddit.com",
                "https://twitter.com",
                "https://www.facebook.com"
            ],
            "general_sites": [
                "https://www.wikipedia.org",
                "https://stackoverflow.com",
                "https://github.com",
                "https://www.youtube.com"
            ]
        }
        
        # 搜索关键词库
        self.search_keywords = {
            "general": [
                "weather", "news", "technology", "sports", "health",
                "travel", "food", "entertainment", "science", "business"
            ],
            "tech": [
                "programming", "software", "artificial intelligence", "machine learning",
                "web development", "mobile apps", "cybersecurity", "cloud computing"
            ],
            "lifestyle": [
                "fitness", "cooking", "fashion", "home improvement", "gardening",
                "photography", "music", "movies", "books", "art"
            ]
        }
        
        logger.debug(f"会话预热器初始化 | 启用: {self.enabled}")
    
    def generate_warmup_steps(self, target_url: str, 
                            session_context: Optional[Dict[str, Any]] = None) -> List[WarmupStep]:
        """
        生成预热步骤
        
        Args:
            target_url: 目标URL
            session_context: 会话上下文
            
        Returns:
            预热步骤列表
        """
        if not self.enabled:
            return []
        
        steps = []
        
        # 使用配置中的默认步骤或生成随机步骤
        if self.default_steps:
            steps = self._create_steps_from_config(self.default_steps)
        else:
            steps = self._generate_random_warmup_steps(target_url, session_context)
        
        logger.debug(f"生成预热步骤 | 数量: {len(steps)} | 目标: {target_url}")
        
        return steps
    
    def _create_steps_from_config(self, config_steps: List[Dict[str, Any]]) -> List[WarmupStep]:
        """从配置创建预热步骤"""
        steps = []
        
        for step_config in config_steps:
            action = step_config.get("action")
            duration = step_config.get("duration", [30, 90])
            
            if action == "google_search":
                keywords = step_config.get("keywords", self.search_keywords["general"])
                step = self._create_google_search_step(keywords, duration)
                steps.append(step)
            
            elif action == "visit_random_site":
                sites = step_config.get("sites", self.warmup_sites["general_sites"])
                step = self._create_site_visit_step(sites, duration)
                steps.append(step)
            
            elif action == "news_browsing":
                step = self._create_news_browsing_step(duration)
                steps.append(step)
            
            elif action == "social_media":
                step = self._create_social_media_step(duration)
                steps.append(step)
        
        return steps
    
    def _generate_random_warmup_steps(self, target_url: str, 
                                    session_context: Optional[Dict[str, Any]] = None) -> List[WarmupStep]:
        """生成随机预热步骤"""
        steps = []
        num_steps = random.randint(2, 4)  # 2-4个预热步骤
        
        # 可能的步骤类型及其权重
        step_types = [
            (WarmupStepType.GOOGLE_SEARCH, 0.4),
            (WarmupStepType.VISIT_SITE, 0.3),
            (WarmupStepType.NEWS_BROWSING, 0.2),
            (WarmupStepType.SOCIAL_MEDIA, 0.1)
        ]
        
        for _ in range(num_steps):
            # 根据权重随机选择步骤类型
            step_type = self._weighted_random_choice(step_types)
            
            if step_type == WarmupStepType.GOOGLE_SEARCH:
                keywords = self._select_relevant_keywords(target_url)
                step = self._create_google_search_step(keywords, [30, 90])
                steps.append(step)
            
            elif step_type == WarmupStepType.VISIT_SITE:
                sites = self._select_relevant_sites(target_url)
                step = self._create_site_visit_step(sites, [60, 180])
                steps.append(step)
            
            elif step_type == WarmupStepType.NEWS_BROWSING:
                step = self._create_news_browsing_step([45, 120])
                steps.append(step)
            
            elif step_type == WarmupStepType.SOCIAL_MEDIA:
                step = self._create_social_media_step([30, 90])
                steps.append(step)
        
        return steps
    
    def _weighted_random_choice(self, choices: List[tuple]) -> Any:
        """根据权重随机选择"""
        total_weight = sum(weight for _, weight in choices)
        rand_val = random.uniform(0, total_weight)
        
        cumulative_weight = 0
        for choice, weight in choices:
            cumulative_weight += weight
            if rand_val <= cumulative_weight:
                return choice
        
        return choices[0][0]  # 默认返回第一个选择
    
    def _select_relevant_keywords(self, target_url: str) -> List[str]:
        """根据目标URL选择相关关键词"""
        # 简化实现：根据域名选择关键词类别
        if any(tech_domain in target_url.lower() for tech_domain in ["github", "stackoverflow", "tech"]):
            return self.search_keywords["tech"]
        elif any(lifestyle_domain in target_url.lower() for lifestyle_domain in ["food", "travel", "fashion"]):
            return self.search_keywords["lifestyle"]
        else:
            return self.search_keywords["general"]
    
    def _select_relevant_sites(self, target_url: str) -> List[str]:
        """根据目标URL选择相关网站"""
        # 简化实现：随机选择一个类别的网站
        categories = list(self.warmup_sites.keys())
        selected_category = random.choice(categories)
        return self.warmup_sites[selected_category]
    
    def _create_google_search_step(self, keywords: List[str], duration: List[float]) -> WarmupStep:
        """创建Google搜索步骤"""
        search_keyword = random.choice(keywords)
        search_url = f"https://www.google.com/search?q={search_keyword.replace(' ', '+')}"
        
        return WarmupStep(
            step_type=WarmupStepType.GOOGLE_SEARCH,
            target_url=search_url,
            duration_range=(duration[0], duration[1]),
            keywords=[search_keyword],
            success_criteria={
                "min_duration": duration[0],
                "required_elements": ["search results", "search box"]
            },
            metadata={
                "search_query": search_keyword,
                "expected_actions": ["search", "scroll", "maybe_click_result"]
            }
        )
    
    def _create_site_visit_step(self, sites: List[str], duration: List[float]) -> WarmupStep:
        """创建网站访问步骤"""
        target_site = random.choice(sites)
        
        return WarmupStep(
            step_type=WarmupStepType.VISIT_SITE,
            target_url=target_site,
            duration_range=(duration[0], duration[1]),
            success_criteria={
                "min_duration": duration[0],
                "page_load_success": True
            },
            metadata={
                "site_category": self._get_site_category(target_site),
                "expected_actions": ["browse", "scroll", "read"]
            }
        )
    
    def _create_news_browsing_step(self, duration: List[float]) -> WarmupStep:
        """创建新闻浏览步骤"""
        news_site = random.choice(self.warmup_sites["news_sites"])
        
        return WarmupStep(
            step_type=WarmupStepType.NEWS_BROWSING,
            target_url=news_site,
            duration_range=(duration[0], duration[1]),
            success_criteria={
                "min_duration": duration[0],
                "articles_viewed": 1
            },
            metadata={
                "content_type": "news",
                "expected_actions": ["browse_headlines", "read_article", "scroll"]
            }
        )
    
    def _create_social_media_step(self, duration: List[float]) -> WarmupStep:
        """创建社交媒体步骤"""
        social_site = random.choice(self.warmup_sites["social_media"])
        
        return WarmupStep(
            step_type=WarmupStepType.SOCIAL_MEDIA,
            target_url=social_site,
            duration_range=(duration[0], duration[1]),
            success_criteria={
                "min_duration": duration[0],
                "engagement": True
            },
            metadata={
                "platform": self._get_platform_name(social_site),
                "expected_actions": ["scroll_feed", "view_posts", "maybe_interact"]
            }
        )
    
    def _get_site_category(self, url: str) -> str:
        """获取网站类别"""
        for category, sites in self.warmup_sites.items():
            if any(site in url for site in sites):
                return category
        return "general"
    
    def _get_platform_name(self, url: str) -> str:
        """获取社交平台名称"""
        if "reddit" in url:
            return "reddit"
        elif "twitter" in url:
            return "twitter"
        elif "facebook" in url:
            return "facebook"
        else:
            return "unknown"
    
    async def execute_warmup_sequence(self, session: Session, 
                                    browser_controller: Any = None) -> Dict[str, Any]:
        """
        执行预热序列
        
        Args:
            session: 会话对象
            browser_controller: 浏览器控制器
            
        Returns:
            执行结果
        """
        if not self.enabled or not session.warmup_steps:
            logger.info(f"跳过预热 | 会话: {session.session_id}")
            return {"skipped": True, "reason": "disabled or no steps"}
        
        logger.info(f"开始会话预热 | 会话: {session.session_id} | 步骤数: {len(session.warmup_steps)}")
        
        warmup_start_time = datetime.now()
        results = {
            "session_id": session.session_id,
            "total_steps": len(session.warmup_steps),
            "completed_steps": 0,
            "failed_steps": 0,
            "step_results": [],
            "total_duration": 0.0,
            "success": True
        }
        
        for i, step in enumerate(session.warmup_steps):
            try:
                logger.debug(f"执行预热步骤 | 步骤: {i+1}/{len(session.warmup_steps)} | 类型: {step.step_type.value}")
                
                step_result = await self._execute_warmup_step(step, browser_controller)
                results["step_results"].append(step_result)
                
                if step_result["success"]:
                    results["completed_steps"] += 1
                else:
                    results["failed_steps"] += 1
                    logger.warning(f"预热步骤失败 | 步骤: {i+1} | 错误: {step_result.get('error')}")
                
            except Exception as e:
                results["failed_steps"] += 1
                error_result = {
                    "step_index": i,
                    "step_type": step.step_type.value,
                    "success": False,
                    "error": str(e),
                    "duration": 0.0
                }
                results["step_results"].append(error_result)
                logger.error(f"预热步骤异常 | 步骤: {i+1} | 异常: {e}")
        
        # 计算总时长
        warmup_end_time = datetime.now()
        results["total_duration"] = (warmup_end_time - warmup_start_time).total_seconds()
        
        # 判断整体成功
        results["success"] = results["failed_steps"] == 0
        
        # 更新会话信息
        session.warmup_duration = results["total_duration"]
        
        logger.info(
            f"会话预热完成 | 会话: {session.session_id} | "
            f"成功: {results['completed_steps']}/{results['total_steps']} | "
            f"耗时: {results['total_duration']:.2f}s"
        )
        
        return results
    
    async def _execute_warmup_step(self, step: WarmupStep, 
                                 browser_controller: Any = None) -> Dict[str, Any]:
        """执行单个预热步骤"""
        step_start_time = datetime.now()
        
        # 随机选择步骤持续时间
        duration = random.uniform(step.duration_range[0], step.duration_range[1])
        
        result = {
            "step_type": step.step_type.value,
            "target_url": step.target_url,
            "planned_duration": duration,
            "actual_duration": 0.0,
            "success": False,
            "error": None,
            "metadata": step.metadata.copy()
        }
        
        try:
            if browser_controller:
                # 实际执行浏览器操作
                await self._execute_browser_step(step, duration, browser_controller)
            else:
                # 模拟执行（仅等待）
                await asyncio.sleep(duration)
            
            result["success"] = True
            
        except Exception as e:
            result["error"] = str(e)
            result["success"] = False
        
        # 计算实际执行时间
        step_end_time = datetime.now()
        result["actual_duration"] = (step_end_time - step_start_time).total_seconds()
        
        return result
    
    async def _execute_browser_step(self, step: WarmupStep, duration: float, 
                                  browser_controller: Any):
        """执行浏览器预热步骤"""
        # 这里需要与具体的浏览器控制器集成
        # 暂时只是等待相应的时间
        
        if step.step_type == WarmupStepType.GOOGLE_SEARCH:
            # 模拟Google搜索
            await self._simulate_google_search(step, duration, browser_controller)
        
        elif step.step_type == WarmupStepType.VISIT_SITE:
            # 模拟网站访问
            await self._simulate_site_visit(step, duration, browser_controller)
        
        elif step.step_type == WarmupStepType.NEWS_BROWSING:
            # 模拟新闻浏览
            await self._simulate_news_browsing(step, duration, browser_controller)
        
        elif step.step_type == WarmupStepType.SOCIAL_MEDIA:
            # 模拟社交媒体浏览
            await self._simulate_social_media(step, duration, browser_controller)
        
        else:
            # 默认行为：访问页面并等待
            await asyncio.sleep(duration)
    
    async def _simulate_google_search(self, step: WarmupStep, duration: float, 
                                    browser_controller: Any):
        """模拟Google搜索行为"""
        # 导航到搜索页面
        # 等待页面加载
        await asyncio.sleep(random.uniform(2, 5))
        
        # 模拟搜索结果浏览
        browse_time = duration - 5  # 减去页面加载时间
        if browse_time > 0:
            await asyncio.sleep(browse_time)
    
    async def _simulate_site_visit(self, step: WarmupStep, duration: float, 
                                 browser_controller: Any):
        """模拟网站访问行为"""
        # 导航到目标网站
        # 等待页面加载
        await asyncio.sleep(random.uniform(2, 6))
        
        # 模拟浏览行为
        browse_time = duration - 4  # 减去页面加载时间
        if browse_time > 0:
            await asyncio.sleep(browse_time)
    
    async def _simulate_news_browsing(self, step: WarmupStep, duration: float, 
                                    browser_controller: Any):
        """模拟新闻浏览行为"""
        # 导航到新闻网站
        # 等待页面加载
        await asyncio.sleep(random.uniform(3, 7))
        
        # 模拟阅读行为
        read_time = duration - 5  # 减去页面加载时间
        if read_time > 0:
            await asyncio.sleep(read_time)
    
    async def _simulate_social_media(self, step: WarmupStep, duration: float, 
                                   browser_controller: Any):
        """模拟社交媒体浏览行为"""
        # 导航到社交媒体网站
        # 等待页面加载
        await asyncio.sleep(random.uniform(2, 5))
        
        # 模拟社交媒体浏览
        browse_time = duration - 3  # 减去页面加载时间
        if browse_time > 0:
            await asyncio.sleep(browse_time)
