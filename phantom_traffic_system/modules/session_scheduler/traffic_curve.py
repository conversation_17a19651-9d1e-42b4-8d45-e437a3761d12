"""
会话调度模块 - 流量曲线控制器
控制流量的时间分布，模拟真实的访问模式
"""

import asyncio
import math
import random
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta

from .models import TrafficPattern, TrafficCurve, TrafficCurvePoint, TaskPriority
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("traffic_curve")


class TrafficCurveController:
    """流量曲线控制器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化流量曲线控制器
        
        Args:
            config: 流量控制配置
        """
        self.config = config
        self.default_pattern = TrafficPattern(config.get("default_pattern", "realistic"))
        self.base_rate = config.get("base_rate", 100)  # 基础流量速率（任务/小时）
        self.max_rate = config.get("max_rate", 1000)   # 最大流量速率
        
        # 当前状态
        self.current_curve: Optional[TrafficCurve] = None
        self.curve_start_time: Optional[datetime] = None
        self.is_running = False
        
        # 预定义的流量曲线
        self._init_predefined_curves()
        
        # 任务生成回调
        self.task_generator: Optional[Callable] = None
        
        logger.debug(f"流量曲线控制器初始化 | 默认模式: {self.default_pattern.value}")
    
    def _init_predefined_curves(self):
        """初始化预定义的流量曲线"""
        self.predefined_curves = {
            TrafficPattern.FLAT: self._create_flat_curve(),
            TrafficPattern.REALISTIC: self._create_realistic_curve(),
            TrafficPattern.BURST: self._create_burst_curve(),
            TrafficPattern.GRADUAL: self._create_gradual_curve()
        }
    
    def _create_flat_curve(self) -> TrafficCurve:
        """创建平稳流量曲线"""
        points = [
            TrafficCurvePoint(0, self.base_rate, "开始"),
            TrafficCurvePoint(3600, self.base_rate, "结束")
        ]
        
        return TrafficCurve(
            name="平稳流量",
            pattern=TrafficPattern.FLAT,
            points=points,
            duration=3600
        )
    
    def _create_realistic_curve(self) -> TrafficCurve:
        """创建真实流量曲线（模拟一天的访问模式）"""
        # 24小时的真实流量模式
        points = [
            TrafficCurvePoint(0, self.base_rate * 0.3, "深夜低谷"),
            TrafficCurvePoint(3600, self.base_rate * 0.2, "凌晨最低"),
            TrafficCurvePoint(7200, self.base_rate * 0.4, "早晨开始"),
            TrafficCurvePoint(10800, self.base_rate * 0.8, "上午增长"),
            TrafficCurvePoint(14400, self.base_rate * 1.2, "中午高峰"),
            TrafficCurvePoint(18000, self.base_rate * 1.0, "下午平稳"),
            TrafficCurvePoint(21600, self.base_rate * 1.5, "晚间高峰"),
            TrafficCurvePoint(25200, self.base_rate * 1.3, "夜间活跃"),
            TrafficCurvePoint(28800, self.base_rate * 0.8, "夜间下降"),
            TrafficCurvePoint(32400, self.base_rate * 0.5, "深夜回落"),
            TrafficCurvePoint(36000, self.base_rate * 0.3, "深夜低谷")
        ]
        
        return TrafficCurve(
            name="真实流量曲线",
            pattern=TrafficPattern.REALISTIC,
            points=points,
            duration=36000  # 10小时
        )
    
    def _create_burst_curve(self) -> TrafficCurve:
        """创建突发流量曲线"""
        points = [
            TrafficCurvePoint(0, self.base_rate * 0.5, "开始低流量"),
            TrafficCurvePoint(300, self.base_rate * 0.5, "保持低流量"),
            TrafficCurvePoint(600, self.base_rate * 3.0, "突发开始"),
            TrafficCurvePoint(900, self.base_rate * 5.0, "突发峰值"),
            TrafficCurvePoint(1200, self.base_rate * 3.0, "突发下降"),
            TrafficCurvePoint(1500, self.base_rate * 1.0, "回归正常"),
            TrafficCurvePoint(1800, self.base_rate * 0.8, "稍微下降"),
            TrafficCurvePoint(2100, self.base_rate * 0.5, "结束低流量")
        ]
        
        return TrafficCurve(
            name="突发流量",
            pattern=TrafficPattern.BURST,
            points=points,
            duration=2100
        )
    
    def _create_gradual_curve(self) -> TrafficCurve:
        """创建渐进式流量曲线"""
        points = [
            TrafficCurvePoint(0, self.base_rate * 0.1, "极低开始"),
            TrafficCurvePoint(600, self.base_rate * 0.3, "缓慢增长"),
            TrafficCurvePoint(1200, self.base_rate * 0.6, "持续增长"),
            TrafficCurvePoint(1800, self.base_rate * 1.0, "达到基准"),
            TrafficCurvePoint(2400, self.base_rate * 1.5, "超过基准"),
            TrafficCurvePoint(3000, self.base_rate * 2.0, "达到峰值"),
            TrafficCurvePoint(3600, self.base_rate * 2.0, "保持峰值")
        ]
        
        return TrafficCurve(
            name="渐进式流量",
            pattern=TrafficPattern.GRADUAL,
            points=points,
            duration=3600
        )
    
    def set_traffic_curve(self, pattern: TrafficPattern, 
                         custom_curve: Optional[TrafficCurve] = None):
        """
        设置流量曲线
        
        Args:
            pattern: 流量模式
            custom_curve: 自定义曲线（当pattern为CUSTOM时使用）
        """
        if pattern == TrafficPattern.CUSTOM and custom_curve:
            self.current_curve = custom_curve
        else:
            self.current_curve = self.predefined_curves.get(pattern)
        
        if not self.current_curve:
            logger.warning(f"未找到流量模式: {pattern.value}，使用默认模式")
            self.current_curve = self.predefined_curves[TrafficPattern.FLAT]
        
        self.curve_start_time = datetime.now()
        
        logger.info(f"流量曲线已设置 | 模式: {pattern.value} | 名称: {self.current_curve.name}")
    
    def get_current_traffic_rate(self) -> float:
        """
        获取当前流量速率
        
        Returns:
            当前流量速率（任务/小时）
        """
        if not self.current_curve or not self.curve_start_time:
            return self.base_rate
        
        # 计算时间偏移
        elapsed_time = (datetime.now() - self.curve_start_time).total_seconds()
        
        # 获取当前速率
        current_rate = self.current_curve.get_rate_at_time(elapsed_time)
        
        # 应用随机变化（±10%）
        variance = random.uniform(0.9, 1.1)
        actual_rate = current_rate * variance
        
        # 限制在最大速率内
        return min(actual_rate, self.max_rate)
    
    def calculate_next_task_delay(self) -> float:
        """
        计算下一个任务的延迟时间
        
        Returns:
            延迟时间（秒）
        """
        current_rate = self.get_current_traffic_rate()
        
        if current_rate <= 0:
            return 3600  # 如果速率为0，等待1小时
        
        # 计算平均间隔时间（秒）
        average_interval = 3600 / current_rate
        
        # 使用泊松分布生成随机间隔
        # 泊松过程的间隔时间服从指数分布
        delay = random.expovariate(1.0 / average_interval)
        
        # 限制最小和最大延迟
        min_delay = 1.0   # 最小1秒
        max_delay = 300.0 # 最大5分钟
        
        return max(min_delay, min(delay, max_delay))
    
    def start_traffic_generation(self, task_generator: Callable):
        """
        开始流量生成
        
        Args:
            task_generator: 任务生成器函数
        """
        if self.is_running:
            logger.warning("流量生成已在运行")
            return
        
        self.task_generator = task_generator
        self.is_running = True
        
        # 如果没有设置曲线，使用默认模式
        if not self.current_curve:
            self.set_traffic_curve(self.default_pattern)
        
        # 启动异步任务
        asyncio.create_task(self._traffic_generation_loop())
        
        logger.info("流量生成已启动")
    
    def stop_traffic_generation(self):
        """停止流量生成"""
        self.is_running = False
        logger.info("流量生成已停止")
    
    async def _traffic_generation_loop(self):
        """流量生成循环"""
        logger.debug("流量生成循环启动")
        
        while self.is_running:
            try:
                # 计算下一个任务的延迟
                delay = self.calculate_next_task_delay()
                
                # 等待
                await asyncio.sleep(delay)
                
                if not self.is_running:
                    break
                
                # 生成任务
                if self.task_generator:
                    current_rate = self.get_current_traffic_rate()
                    
                    # 根据当前流量速率决定任务优先级
                    if current_rate > self.base_rate * 2:
                        priority = TaskPriority.HIGH
                    elif current_rate > self.base_rate:
                        priority = TaskPriority.NORMAL
                    else:
                        priority = TaskPriority.LOW
                    
                    try:
                        await self.task_generator(priority)
                    except Exception as e:
                        logger.error(f"任务生成失败: {e}")
                
            except Exception as e:
                logger.error(f"流量生成循环异常: {e}")
                await asyncio.sleep(5)  # 异常时短暂等待
        
        logger.debug("流量生成循环停止")
    
    def create_custom_curve(self, name: str, points: List[Dict[str, Any]], 
                          duration: float) -> TrafficCurve:
        """
        创建自定义流量曲线
        
        Args:
            name: 曲线名称
            points: 曲线点列表，每个点包含 time_offset 和 traffic_rate
            duration: 曲线持续时间
            
        Returns:
            自定义流量曲线
        """
        curve_points = []
        for point_data in points:
            point = TrafficCurvePoint(
                time_offset=point_data["time_offset"],
                traffic_rate=point_data["traffic_rate"],
                description=point_data.get("description", "")
            )
            curve_points.append(point)
        
        # 按时间排序
        curve_points.sort(key=lambda p: p.time_offset)
        
        curve = TrafficCurve(
            name=name,
            pattern=TrafficPattern.CUSTOM,
            points=curve_points,
            duration=duration
        )
        
        logger.info(f"自定义流量曲线创建 | 名称: {name} | 点数: {len(curve_points)}")
        
        return curve
    
    def get_curve_preview(self, curve: TrafficCurve, 
                         sample_points: int = 100) -> List[Dict[str, Any]]:
        """
        获取流量曲线预览数据
        
        Args:
            curve: 流量曲线
            sample_points: 采样点数量
            
        Returns:
            预览数据点列表
        """
        preview_data = []
        time_step = curve.duration / sample_points
        
        for i in range(sample_points + 1):
            time_offset = i * time_step
            traffic_rate = curve.get_rate_at_time(time_offset)
            
            preview_data.append({
                "time_offset": time_offset,
                "traffic_rate": traffic_rate,
                "time_label": self._format_time_offset(time_offset)
            })
        
        return preview_data
    
    def _format_time_offset(self, time_offset: float) -> str:
        """格式化时间偏移为可读字符串"""
        hours = int(time_offset // 3600)
        minutes = int((time_offset % 3600) // 60)
        seconds = int(time_offset % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取流量控制统计信息"""
        current_rate = self.get_current_traffic_rate()
        
        stats = {
            "is_running": self.is_running,
            "current_rate": current_rate,
            "base_rate": self.base_rate,
            "max_rate": self.max_rate,
            "current_curve": None,
            "curve_progress": 0.0
        }
        
        if self.current_curve:
            stats["current_curve"] = {
                "name": self.current_curve.name,
                "pattern": self.current_curve.pattern.value,
                "duration": self.current_curve.duration
            }
            
            if self.curve_start_time:
                elapsed_time = (datetime.now() - self.curve_start_time).total_seconds()
                stats["curve_progress"] = min(100.0, (elapsed_time / self.current_curve.duration) * 100)
        
        return stats
