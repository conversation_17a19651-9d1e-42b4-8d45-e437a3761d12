"""
会话调度模块 - 数据模型
定义会话、任务、流量模式等相关数据结构
"""

from enum import Enum
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import uuid


class SessionStatus(Enum):
    """会话状态枚举"""
    PENDING = "pending"
    WARMING_UP = "warming_up"
    ACTIVE = "active"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskStatus(Enum):
    """任务状态枚举"""
    QUEUED = "queued"
    ASSIGNED = "assigned"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


class TrafficPattern(Enum):
    """流量模式枚举"""
    FLAT = "flat"           # 平稳流量
    REALISTIC = "realistic" # 真实流量曲线
    BURST = "burst"         # 突发流量
    GRADUAL = "gradual"     # 渐进式流量
    CUSTOM = "custom"       # 自定义模式


class WarmupStepType(Enum):
    """预热步骤类型枚举"""
    GOOGLE_SEARCH = "google_search"
    VISIT_SITE = "visit_site"
    SOCIAL_MEDIA = "social_media"
    NEWS_BROWSING = "news_browsing"
    RANDOM_BROWSING = "random_browsing"


@dataclass
class WarmupStep:
    """预热步骤"""
    step_type: WarmupStepType
    target_url: str
    duration_range: Tuple[float, float]
    keywords: List[str] = field(default_factory=list)
    success_criteria: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Session:
    """会话对象"""
    session_id: str
    target_url: str
    status: SessionStatus = SessionStatus.PENDING
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 配置信息
    proxy_info: Optional[Dict[str, Any]] = None
    fingerprint_profile: Optional[Dict[str, Any]] = None
    behavior_profile: Optional[Dict[str, Any]] = None
    
    # 预热配置
    warmup_enabled: bool = True
    warmup_steps: List[WarmupStep] = field(default_factory=list)
    
    # 执行信息
    worker_id: Optional[str] = None
    browser_instance: Optional[str] = None
    
    # 统计信息
    total_duration: float = 0.0
    warmup_duration: float = 0.0
    main_task_duration: float = 0.0
    success: bool = False
    error_message: Optional[str] = None
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.session_id:
            self.session_id = str(uuid.uuid4())
    
    @property
    def is_active(self) -> bool:
        """检查会话是否活跃"""
        return self.status in [SessionStatus.WARMING_UP, SessionStatus.ACTIVE]
    
    @property
    def is_completed(self) -> bool:
        """检查会话是否已完成"""
        return self.status in [SessionStatus.COMPLETED, SessionStatus.FAILED, SessionStatus.CANCELLED]
    
    def start(self):
        """开始会话"""
        self.status = SessionStatus.WARMING_UP if self.warmup_enabled else SessionStatus.ACTIVE
        self.started_at = datetime.now()
    
    def complete(self, success: bool = True, error_message: Optional[str] = None):
        """完成会话"""
        self.status = SessionStatus.COMPLETED if success else SessionStatus.FAILED
        self.completed_at = datetime.now()
        self.success = success
        self.error_message = error_message
        
        if self.started_at:
            self.total_duration = (self.completed_at - self.started_at).total_seconds()


@dataclass
class Task:
    """任务对象"""
    task_id: str
    target_url: str
    priority: TaskPriority = TaskPriority.NORMAL
    status: TaskStatus = TaskStatus.QUEUED
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    scheduled_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 重试信息
    max_retries: int = 3
    retry_count: int = 0
    retry_delay: float = 60.0  # 重试延迟（秒）
    
    # 配置要求
    proxy_requirements: Dict[str, Any] = field(default_factory=dict)
    fingerprint_requirements: Dict[str, Any] = field(default_factory=dict)
    behavior_requirements: Dict[str, Any] = field(default_factory=dict)
    
    # 执行信息
    assigned_worker: Optional[str] = None
    session_id: Optional[str] = None
    
    # 结果信息
    success: bool = False
    error_message: Optional[str] = None
    execution_time: float = 0.0
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.task_id:
            self.task_id = str(uuid.uuid4())
    
    @property
    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return self.retry_count < self.max_retries and self.status == TaskStatus.FAILED
    
    def assign_to_worker(self, worker_id: str):
        """分配给工作节点"""
        self.assigned_worker = worker_id
        self.status = TaskStatus.ASSIGNED
    
    def start_execution(self, session_id: str):
        """开始执行"""
        self.session_id = session_id
        self.status = TaskStatus.RUNNING
        self.started_at = datetime.now()
    
    def complete_execution(self, success: bool = True, error_message: Optional[str] = None):
        """完成执行"""
        self.status = TaskStatus.COMPLETED if success else TaskStatus.FAILED
        self.completed_at = datetime.now()
        self.success = success
        self.error_message = error_message
        
        if self.started_at:
            self.execution_time = (self.completed_at - self.started_at).total_seconds()
    
    def retry(self):
        """重试任务"""
        if self.can_retry:
            self.retry_count += 1
            self.status = TaskStatus.RETRYING
            self.assigned_worker = None
            self.session_id = None
            self.started_at = None
            self.completed_at = None


@dataclass
class TrafficCurvePoint:
    """流量曲线点"""
    time_offset: float  # 相对于开始时间的偏移（秒）
    traffic_rate: float # 流量速率（任务/小时）
    description: str = ""


@dataclass
class TrafficCurve:
    """流量曲线"""
    name: str
    pattern: TrafficPattern
    points: List[TrafficCurvePoint] = field(default_factory=list)
    duration: float = 3600.0  # 曲线持续时间（秒）
    
    def get_rate_at_time(self, time_offset: float) -> float:
        """获取指定时间的流量速率"""
        if not self.points:
            return 0.0
        
        # 如果时间超出曲线范围，返回最后一个点的速率
        if time_offset >= self.duration:
            return self.points[-1].traffic_rate
        
        # 找到时间点所在的区间
        for i in range(len(self.points) - 1):
            if self.points[i].time_offset <= time_offset <= self.points[i + 1].time_offset:
                # 线性插值
                p1, p2 = self.points[i], self.points[i + 1]
                ratio = (time_offset - p1.time_offset) / (p2.time_offset - p1.time_offset)
                return p1.traffic_rate + ratio * (p2.traffic_rate - p1.traffic_rate)
        
        # 如果在第一个点之前，返回第一个点的速率
        if time_offset < self.points[0].time_offset:
            return self.points[0].traffic_rate
        
        # 默认返回最后一个点的速率
        return self.points[-1].traffic_rate


@dataclass
class WorkerNode:
    """工作节点"""
    worker_id: str
    hostname: str
    ip_address: str
    status: str = "idle"  # idle, busy, offline
    
    # 能力信息
    max_concurrent_sessions: int = 5
    current_sessions: int = 0
    supported_browsers: List[str] = field(default_factory=lambda: ["chromium"])
    
    # 统计信息
    total_tasks_completed: int = 0
    total_tasks_failed: int = 0
    average_task_duration: float = 0.0
    
    # 时间信息
    last_heartbeat: datetime = field(default_factory=datetime.now)
    started_at: datetime = field(default_factory=datetime.now)
    
    @property
    def is_available(self) -> bool:
        """检查节点是否可用"""
        return (
            self.status == "idle" and 
            self.current_sessions < self.max_concurrent_sessions and
            (datetime.now() - self.last_heartbeat).total_seconds() < 60
        )
    
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        total_tasks = self.total_tasks_completed + self.total_tasks_failed
        if total_tasks == 0:
            return 0.0
        return self.total_tasks_completed / total_tasks
    
    def update_heartbeat(self):
        """更新心跳时间"""
        self.last_heartbeat = datetime.now()
    
    def assign_session(self):
        """分配会话"""
        self.current_sessions += 1
        if self.current_sessions >= self.max_concurrent_sessions:
            self.status = "busy"
    
    def release_session(self, success: bool = True):
        """释放会话"""
        self.current_sessions = max(0, self.current_sessions - 1)
        
        if success:
            self.total_tasks_completed += 1
        else:
            self.total_tasks_failed += 1
        
        if self.current_sessions < self.max_concurrent_sessions:
            self.status = "idle"


@dataclass
class SchedulerStats:
    """调度器统计信息"""
    total_tasks_created: int = 0
    total_tasks_completed: int = 0
    total_tasks_failed: int = 0
    total_sessions_created: int = 0
    total_sessions_completed: int = 0
    
    # 队列统计
    queued_tasks: int = 0
    running_tasks: int = 0
    active_sessions: int = 0
    
    # 性能统计
    average_task_duration: float = 0.0
    average_session_duration: float = 0.0
    current_traffic_rate: float = 0.0
    
    # 时间信息
    scheduler_start_time: datetime = field(default_factory=datetime.now)
    last_update_time: datetime = field(default_factory=datetime.now)
    
    @property
    def success_rate(self) -> float:
        """计算总体成功率"""
        total_completed = self.total_tasks_completed + self.total_tasks_failed
        if total_completed == 0:
            return 0.0
        return self.total_tasks_completed / total_completed
    
    @property
    def uptime(self) -> float:
        """计算运行时间（秒）"""
        return (datetime.now() - self.scheduler_start_time).total_seconds()
    
    def update(self):
        """更新统计时间"""
        self.last_update_time = datetime.now()
