"""
会话调度模块 - 会话调度器
统一管理会话预热、任务调度、流量控制和分布式协调
"""

import asyncio
import random
from typing import Dict, List, Any, Optional
from datetime import datetime

from .models import Session, Task, TaskPriority, TrafficPattern, SchedulerStats
from .session_warmup import SessionWarmup
from .task_queue import TaskQueue
from .traffic_curve import TrafficCurveController
from .distributed_coordinator import DistributedCoordinator
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("session_scheduler")


class SessionScheduler:
    """会话调度器 - 会话调度系统的中央控制器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化会话调度器
        
        Args:
            config: 调度器配置
        """
        self.config = config
        
        # 初始化各个组件
        self.session_warmup = SessionWarmup(config.get("warmup", {}))
        self.task_queue = TaskQueue(config.get("task_queue", {}))
        self.traffic_controller = TrafficCurveController(config.get("traffic_control", {}))
        self.distributed_coordinator = DistributedCoordinator(config.get("distributed", {}))
        
        # 会话管理
        self.active_sessions: Dict[str, Session] = {}
        self.session_history: List[Session] = []
        
        # 统计信息
        self.stats = SchedulerStats()
        
        # 状态管理
        self.is_running = False
        self.scheduler_task: Optional[asyncio.Task] = None
        
        # 目标URL列表
        self.target_urls = config.get("target_urls", [])
        
        logger.info("会话调度器初始化完成")
    
    async def start(self):
        """启动会话调度器"""
        if self.is_running:
            logger.warning("会话调度器已在运行")
            return
        
        self.is_running = True
        self.stats.scheduler_start_time = datetime.now()
        
        # 启动各个组件
        await self.task_queue.start()
        await self.distributed_coordinator.start()
        
        # 设置任务生成器
        self.traffic_controller.start_traffic_generation(self._generate_task_from_traffic)
        
        # 启动主调度循环
        self.scheduler_task = asyncio.create_task(self._main_scheduler_loop())
        
        logger.info("会话调度器启动")
    
    async def stop(self):
        """停止会话调度器"""
        self.is_running = False
        
        # 停止流量生成
        self.traffic_controller.stop_traffic_generation()
        
        # 停止各个组件
        await self.task_queue.stop()
        await self.distributed_coordinator.stop()
        
        # 停止主调度循环
        if self.scheduler_task:
            self.scheduler_task.cancel()
            try:
                await self.scheduler_task
            except asyncio.CancelledError:
                pass
        
        # 完成所有活跃会话
        for session in self.active_sessions.values():
            session.complete(success=False, error_message="调度器停止")
        
        logger.info("会话调度器停止")
    
    async def _generate_task_from_traffic(self, priority: TaskPriority):
        """从流量控制器生成任务"""
        if not self.target_urls:
            logger.warning("没有配置目标URL")
            return
        
        # 随机选择目标URL
        target_url = random.choice(self.target_urls)
        
        # 创建任务
        task = self.task_queue.add_task(
            target_url=target_url,
            priority=priority,
            metadata={"generated_by": "traffic_controller"}
        )
        
        logger.debug(f"流量生成任务 | ID: {task.task_id} | URL: {target_url}")
    
    async def _main_scheduler_loop(self):
        """主调度循环"""
        logger.debug("主调度循环启动")
        
        while self.is_running:
            try:
                # 处理待分配的任务
                await self._process_pending_tasks()
                
                # 检查会话状态
                await self._check_session_status()
                
                # 更新统计信息
                self._update_statistics()
                
                # 等待一段时间
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"主调度循环异常: {e}")
                await asyncio.sleep(5)
        
        logger.debug("主调度循环停止")
    
    async def _process_pending_tasks(self):
        """处理待分配的任务"""
        # 获取可用的工作节点
        available_workers = self.task_queue.get_available_workers()
        
        if not available_workers:
            return
        
        # 为每个可用工作节点分配任务
        for worker in available_workers:
            if not worker.is_available:
                continue
            
            # 获取下一个任务
            task = self.task_queue.get_next_task(worker.worker_id)
            if not task:
                continue
            
            # 创建会话
            session = await self._create_session_for_task(task)
            
            # 启动会话执行
            asyncio.create_task(self._execute_session(session, task))
    
    async def _create_session_for_task(self, task: Task) -> Session:
        """为任务创建会话"""
        # 生成预热步骤
        warmup_steps = self.session_warmup.generate_warmup_steps(
            task.target_url,
            {"task_id": task.task_id}
        )
        
        # 创建会话
        session = Session(
            session_id="",  # 将在__post_init__中生成
            target_url=task.target_url,
            warmup_steps=warmup_steps,
            worker_id=task.assigned_worker,
            metadata={"task_id": task.task_id}
        )
        
        # 添加到活跃会话
        self.active_sessions[session.session_id] = session
        self.stats.total_sessions_created += 1
        
        logger.debug(f"会话创建 | ID: {session.session_id} | 任务: {task.task_id}")
        
        return session
    
    async def _execute_session(self, session: Session, task: Task):
        """执行会话"""
        try:
            session.start()
            task.start_execution(session.session_id)
            
            logger.info(f"会话执行开始 | 会话: {session.session_id} | 任务: {task.task_id}")
            
            # 执行预热
            if session.warmup_enabled and session.warmup_steps:
                warmup_result = await self.session_warmup.execute_warmup_sequence(session)
                
                if not warmup_result.get("success", False):
                    logger.warning(f"会话预热失败 | 会话: {session.session_id}")
                    # 继续执行主任务，预热失败不应该阻止主任务
            
            # 执行主任务
            await self._execute_main_task(session, task)
            
            # 完成会话
            session.complete(success=True)
            task.complete_execution(success=True)
            
            logger.info(f"会话执行完成 | 会话: {session.session_id} | 任务: {task.task_id}")
            
        except Exception as e:
            # 处理执行异常
            error_message = str(e)
            session.complete(success=False, error_message=error_message)
            task.complete_execution(success=False, error_message=error_message)
            
            logger.error(f"会话执行失败 | 会话: {session.session_id} | 错误: {e}")
        
        finally:
            # 清理会话
            self._cleanup_session(session)
            
            # 通知任务队列任务完成
            self.task_queue.complete_task(
                task.task_id, 
                session.success, 
                session.error_message
            )
    
    async def _execute_main_task(self, session: Session, task: Task):
        """执行主任务"""
        # 这里应该集成浏览器控制器来执行实际的访问任务
        # 暂时模拟执行
        
        # 模拟页面访问时间
        visit_duration = random.uniform(30, 180)  # 30秒到3分钟
        
        logger.debug(f"模拟主任务执行 | 会话: {session.session_id} | 时长: {visit_duration:.1f}s")
        
        await asyncio.sleep(visit_duration)
        
        session.main_task_duration = visit_duration
    
    def _cleanup_session(self, session: Session):
        """清理会话"""
        # 从活跃会话中移除
        if session.session_id in self.active_sessions:
            del self.active_sessions[session.session_id]
        
        # 添加到历史记录
        self.session_history.append(session)
        
        # 限制历史记录大小
        max_history = 1000
        if len(self.session_history) > max_history:
            self.session_history = self.session_history[-max_history:]
        
        self.stats.total_sessions_completed += 1
    
    async def _check_session_status(self):
        """检查会话状态"""
        # 检查超时的会话
        timeout_sessions = []
        max_session_duration = 600  # 10分钟超时
        
        for session in self.active_sessions.values():
            if session.started_at:
                duration = (datetime.now() - session.started_at).total_seconds()
                if duration > max_session_duration:
                    timeout_sessions.append(session)
        
        # 处理超时会话
        for session in timeout_sessions:
            logger.warning(f"会话超时 | ID: {session.session_id}")
            session.complete(success=False, error_message="会话超时")
            self._cleanup_session(session)
    
    def _update_statistics(self):
        """更新统计信息"""
        # 更新队列统计
        queue_stats = self.task_queue.get_statistics()
        self.stats.total_tasks_created = queue_stats["total_created"]
        self.stats.total_tasks_completed = queue_stats["total_completed"]
        self.stats.total_tasks_failed = queue_stats["total_failed"]
        self.stats.queued_tasks = queue_stats["current_queued"]
        self.stats.running_tasks = queue_stats["current_running"]
        
        # 更新会话统计
        self.stats.active_sessions = len(self.active_sessions)
        
        # 更新流量统计
        traffic_stats = self.traffic_controller.get_statistics()
        self.stats.current_traffic_rate = traffic_stats["current_rate"]
        
        # 计算平均时长
        if self.session_history:
            total_duration = sum(s.total_duration for s in self.session_history if s.total_duration > 0)
            self.stats.average_session_duration = total_duration / len(self.session_history)
        
        self.stats.update()
    
    def add_target_url(self, url: str):
        """添加目标URL"""
        if url not in self.target_urls:
            self.target_urls.append(url)
            logger.info(f"目标URL已添加: {url}")
    
    def remove_target_url(self, url: str):
        """移除目标URL"""
        if url in self.target_urls:
            self.target_urls.remove(url)
            logger.info(f"目标URL已移除: {url}")
    
    def set_traffic_pattern(self, pattern: TrafficPattern):
        """设置流量模式"""
        self.traffic_controller.set_traffic_curve(pattern)
        logger.info(f"流量模式已设置: {pattern.value}")
    
    def create_manual_task(self, target_url: str, priority: TaskPriority = TaskPriority.NORMAL,
                          **kwargs) -> Task:
        """手动创建任务"""
        task = self.task_queue.add_task(
            target_url=target_url,
            priority=priority,
            metadata={"created_by": "manual", **kwargs}
        )
        
        logger.info(f"手动任务创建 | ID: {task.task_id} | URL: {target_url}")
        
        return task
    
    def get_session_info(self, session_id: str) -> Optional[Session]:
        """获取会话信息"""
        # 先查找活跃会话
        if session_id in self.active_sessions:
            return self.active_sessions[session_id]
        
        # 再查找历史会话
        for session in self.session_history:
            if session.session_id == session_id:
                return session
        
        return None
    
    def get_active_sessions(self) -> List[Session]:
        """获取所有活跃会话"""
        return list(self.active_sessions.values())
    
    def get_recent_sessions(self, limit: int = 50) -> List[Session]:
        """获取最近的会话"""
        return self.session_history[-limit:]
    
    def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        return {
            "scheduler": {
                "uptime": self.stats.uptime,
                "is_running": self.is_running,
                "target_urls": len(self.target_urls)
            },
            "tasks": {
                "total_created": self.stats.total_tasks_created,
                "total_completed": self.stats.total_tasks_completed,
                "total_failed": self.stats.total_tasks_failed,
                "success_rate": self.stats.success_rate,
                "queued": self.stats.queued_tasks,
                "running": self.stats.running_tasks
            },
            "sessions": {
                "total_created": self.stats.total_sessions_created,
                "total_completed": self.stats.total_sessions_completed,
                "active": self.stats.active_sessions,
                "average_duration": self.stats.average_session_duration
            },
            "traffic": self.traffic_controller.get_statistics(),
            "queue": self.task_queue.get_statistics(),
            "cluster": self.distributed_coordinator.get_cluster_status() if self.distributed_coordinator.enabled else None
        }
