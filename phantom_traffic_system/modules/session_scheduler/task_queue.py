"""
会话调度模块 - 任务队列管理器
负责任务的创建、排队、分发和状态管理
"""

import asyncio
import heapq
import threading
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime, timedelta
from collections import defaultdict

from .models import Task, TaskStatus, TaskPriority, WorkerNode
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("task_queue")


class PriorityTask:
    """优先级任务包装器（用于优先级队列）"""
    
    def __init__(self, task: Task):
        self.task = task
        self.priority = task.priority.value
        self.created_at = task.created_at
    
    def __lt__(self, other):
        # 优先级高的任务排在前面（数值越大优先级越高）
        if self.priority != other.priority:
            return self.priority > other.priority
        # 相同优先级按创建时间排序（先创建的先执行）
        return self.created_at < other.created_at


class TaskQueue:
    """任务队列管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化任务队列管理器
        
        Args:
            config: 队列配置
        """
        self.config = config
        self.max_queue_size = config.get("max_queue_size", 10000)
        self.max_retries = config.get("max_retries", 3)
        self.retry_delay = config.get("retry_delay", 60.0)
        
        # 任务队列（优先级队列）
        self._queue: List[PriorityTask] = []
        self._queue_lock = threading.Lock()
        
        # 任务存储
        self._tasks: Dict[str, Task] = {}
        self._tasks_lock = threading.Lock()
        
        # 工作节点管理
        self._workers: Dict[str, WorkerNode] = {}
        self._workers_lock = threading.Lock()
        
        # 状态统计
        self._stats = {
            "total_created": 0,
            "total_completed": 0,
            "total_failed": 0,
            "current_queued": 0,
            "current_running": 0
        }
        
        # 事件回调
        self._callbacks: Dict[str, List[Callable]] = defaultdict(list)
        
        # 后台任务
        self._running = False
        self._background_task: Optional[asyncio.Task] = None
        
        logger.debug("任务队列管理器初始化完成")
    
    async def start(self):
        """启动任务队列管理器"""
        if self._running:
            logger.warning("任务队列管理器已在运行")
            return
        
        self._running = True
        self._background_task = asyncio.create_task(self._background_worker())
        
        logger.info("任务队列管理器启动")
    
    async def stop(self):
        """停止任务队列管理器"""
        self._running = False
        
        if self._background_task:
            self._background_task.cancel()
            try:
                await self._background_task
            except asyncio.CancelledError:
                pass
        
        logger.info("任务队列管理器停止")
    
    def add_task(self, target_url: str, priority: TaskPriority = TaskPriority.NORMAL,
                **kwargs) -> Task:
        """
        添加任务到队列
        
        Args:
            target_url: 目标URL
            priority: 任务优先级
            **kwargs: 其他任务参数
            
        Returns:
            创建的任务对象
        """
        # 检查队列大小
        with self._queue_lock:
            if len(self._queue) >= self.max_queue_size:
                raise RuntimeError(f"任务队列已满，最大容量: {self.max_queue_size}")
        
        # 创建任务
        task = Task(
            task_id="",  # 将在__post_init__中生成
            target_url=target_url,
            priority=priority,
            **kwargs
        )
        
        # 存储任务
        with self._tasks_lock:
            self._tasks[task.task_id] = task
        
        # 添加到队列
        priority_task = PriorityTask(task)
        with self._queue_lock:
            heapq.heappush(self._queue, priority_task)
            self._stats["current_queued"] += 1
        
        self._stats["total_created"] += 1
        
        logger.debug(f"任务已添加 | ID: {task.task_id} | URL: {target_url} | 优先级: {priority.name}")
        
        # 触发回调
        self._trigger_callback("task_added", task)
        
        return task
    
    def get_next_task(self, worker_id: str) -> Optional[Task]:
        """
        获取下一个待执行的任务
        
        Args:
            worker_id: 工作节点ID
            
        Returns:
            任务对象，如果没有可用任务则返回None
        """
        with self._queue_lock:
            if not self._queue:
                return None
            
            # 从优先级队列中取出任务
            priority_task = heapq.heappop(self._queue)
            task = priority_task.task
            self._stats["current_queued"] -= 1
        
        # 分配任务给工作节点
        task.assign_to_worker(worker_id)
        self._stats["current_running"] += 1
        
        logger.debug(f"任务已分配 | ID: {task.task_id} | 工作节点: {worker_id}")
        
        # 触发回调
        self._trigger_callback("task_assigned", task)
        
        return task
    
    def complete_task(self, task_id: str, success: bool = True, 
                     error_message: Optional[str] = None):
        """
        完成任务
        
        Args:
            task_id: 任务ID
            success: 是否成功
            error_message: 错误信息
        """
        with self._tasks_lock:
            task = self._tasks.get(task_id)
            if not task:
                logger.warning(f"任务不存在: {task_id}")
                return
        
        # 更新任务状态
        task.complete_execution(success, error_message)
        self._stats["current_running"] -= 1
        
        if success:
            self._stats["total_completed"] += 1
            logger.debug(f"任务完成 | ID: {task_id}")
            self._trigger_callback("task_completed", task)
        else:
            # 检查是否需要重试
            if task.can_retry:
                self._schedule_retry(task)
                logger.debug(f"任务重试 | ID: {task_id} | 重试次数: {task.retry_count}")
                self._trigger_callback("task_retry", task)
            else:
                self._stats["total_failed"] += 1
                logger.warning(f"任务失败 | ID: {task_id} | 错误: {error_message}")
                self._trigger_callback("task_failed", task)
    
    def _schedule_retry(self, task: Task):
        """安排任务重试"""
        task.retry()
        
        # 计算重试延迟（指数退避）
        delay = self.retry_delay * (2 ** (task.retry_count - 1))
        task.scheduled_at = datetime.now() + timedelta(seconds=delay)
        
        # 重新添加到队列（将在后台任务中处理）
        logger.debug(f"任务重试安排 | ID: {task.task_id} | 延迟: {delay}s")
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """
        获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务对象
        """
        with self._tasks_lock:
            return self._tasks.get(task_id)
    
    def get_tasks_by_status(self, status: TaskStatus) -> List[Task]:
        """
        根据状态获取任务列表
        
        Args:
            status: 任务状态
            
        Returns:
            任务列表
        """
        with self._tasks_lock:
            return [task for task in self._tasks.values() if task.status == status]
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功取消
        """
        with self._tasks_lock:
            task = self._tasks.get(task_id)
            if not task:
                return False
            
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                return False  # 已完成的任务无法取消
            
            task.status = TaskStatus.CANCELLED
        
        logger.debug(f"任务已取消 | ID: {task_id}")
        self._trigger_callback("task_cancelled", task)
        
        return True
    
    def register_worker(self, worker: WorkerNode):
        """
        注册工作节点
        
        Args:
            worker: 工作节点
        """
        with self._workers_lock:
            self._workers[worker.worker_id] = worker
        
        logger.info(f"工作节点注册 | ID: {worker.worker_id} | 主机: {worker.hostname}")
    
    def unregister_worker(self, worker_id: str):
        """
        注销工作节点
        
        Args:
            worker_id: 工作节点ID
        """
        with self._workers_lock:
            if worker_id in self._workers:
                del self._workers[worker_id]
        
        logger.info(f"工作节点注销 | ID: {worker_id}")
    
    def get_available_workers(self) -> List[WorkerNode]:
        """
        获取可用的工作节点
        
        Returns:
            可用工作节点列表
        """
        with self._workers_lock:
            return [worker for worker in self._workers.values() if worker.is_available]
    
    def update_worker_heartbeat(self, worker_id: str):
        """
        更新工作节点心跳
        
        Args:
            worker_id: 工作节点ID
        """
        with self._workers_lock:
            worker = self._workers.get(worker_id)
            if worker:
                worker.update_heartbeat()
    
    def add_callback(self, event: str, callback: Callable):
        """
        添加事件回调
        
        Args:
            event: 事件名称
            callback: 回调函数
        """
        self._callbacks[event].append(callback)
    
    def _trigger_callback(self, event: str, *args, **kwargs):
        """触发事件回调"""
        for callback in self._callbacks.get(event, []):
            try:
                callback(*args, **kwargs)
            except Exception as e:
                logger.error(f"回调执行失败 | 事件: {event} | 错误: {e}")
    
    async def _background_worker(self):
        """后台工作任务"""
        logger.debug("后台工作任务启动")
        
        while self._running:
            try:
                # 处理重试任务
                await self._process_retry_tasks()
                
                # 清理过期任务
                await self._cleanup_expired_tasks()
                
                # 检查工作节点健康状态
                await self._check_worker_health()
                
                # 等待一段时间
                await asyncio.sleep(10)
                
            except Exception as e:
                logger.error(f"后台任务异常: {e}")
                await asyncio.sleep(5)
        
        logger.debug("后台工作任务停止")
    
    async def _process_retry_tasks(self):
        """处理重试任务"""
        now = datetime.now()
        retry_tasks = []
        
        with self._tasks_lock:
            for task in self._tasks.values():
                if (task.status == TaskStatus.RETRYING and 
                    task.scheduled_at and 
                    now >= task.scheduled_at):
                    retry_tasks.append(task)
        
        for task in retry_tasks:
            # 重新添加到队列
            task.status = TaskStatus.QUEUED
            task.scheduled_at = None
            
            priority_task = PriorityTask(task)
            with self._queue_lock:
                heapq.heappush(self._queue, priority_task)
                self._stats["current_queued"] += 1
            
            logger.debug(f"重试任务重新入队 | ID: {task.task_id}")
    
    async def _cleanup_expired_tasks(self):
        """清理过期任务"""
        # 清理超过24小时的已完成任务
        cutoff_time = datetime.now() - timedelta(hours=24)
        expired_tasks = []
        
        with self._tasks_lock:
            for task_id, task in list(self._tasks.items()):
                if (task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] and
                    task.completed_at and task.completed_at < cutoff_time):
                    expired_tasks.append(task_id)
        
        for task_id in expired_tasks:
            with self._tasks_lock:
                del self._tasks[task_id]
            logger.debug(f"过期任务已清理 | ID: {task_id}")
    
    async def _check_worker_health(self):
        """检查工作节点健康状态"""
        now = datetime.now()
        offline_workers = []
        
        with self._workers_lock:
            for worker_id, worker in self._workers.items():
                # 检查心跳超时
                if (now - worker.last_heartbeat).total_seconds() > 120:  # 2分钟超时
                    worker.status = "offline"
                    offline_workers.append(worker_id)
        
        for worker_id in offline_workers:
            logger.warning(f"工作节点离线 | ID: {worker_id}")
            # 可以在这里处理节点离线的任务重新分配
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取队列统计信息"""
        with self._queue_lock:
            current_queued = len(self._queue)
        
        with self._workers_lock:
            total_workers = len(self._workers)
            available_workers = len([w for w in self._workers.values() if w.is_available])
        
        return {
            "total_created": self._stats["total_created"],
            "total_completed": self._stats["total_completed"],
            "total_failed": self._stats["total_failed"],
            "current_queued": current_queued,
            "current_running": self._stats["current_running"],
            "success_rate": (
                self._stats["total_completed"] / 
                max(1, self._stats["total_completed"] + self._stats["total_failed"])
            ) * 100,
            "total_workers": total_workers,
            "available_workers": available_workers,
            "queue_utilization": (current_queued / self.max_queue_size) * 100
        }
