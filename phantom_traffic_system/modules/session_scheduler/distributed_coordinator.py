"""
会话调度模块 - 分布式协调器
负责多节点间的任务分发和协调
"""

import asyncio
import json
import time
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timedelta
import aiohttp
import socket

from .models import WorkerNode, Task, TaskStatus
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("distributed_coordinator")


class DistributedCoordinator:
    """分布式协调器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化分布式协调器
        
        Args:
            config: 分布式配置
        """
        self.config = config
        self.enabled = config.get("enabled", False)
        self.node_id = config.get("node_id", self._generate_node_id())
        self.is_master = config.get("is_master", False)
        
        # 网络配置
        self.listen_port = config.get("listen_port", 8080)
        self.master_host = config.get("master_host", "localhost")
        self.master_port = config.get("master_port", 8080)
        
        # 节点管理
        self.worker_nodes: Dict[str, WorkerNode] = {}
        self.node_last_seen: Dict[str, datetime] = {}
        
        # 任务分发
        self.task_assignment_strategy = config.get("task_assignment_strategy", "round_robin")
        self.load_balancing = config.get("load_balancing", True)
        
        # 健康检查
        self.heartbeat_interval = config.get("heartbeat_interval", 30)
        self.node_timeout = config.get("node_timeout", 120)
        
        # 状态
        self.is_running = False
        self.server_task: Optional[asyncio.Task] = None
        self.heartbeat_task: Optional[asyncio.Task] = None
        
        logger.debug(f"分布式协调器初始化 | 节点ID: {self.node_id} | 主节点: {self.is_master}")
    
    def _generate_node_id(self) -> str:
        """生成节点ID"""
        hostname = socket.gethostname()
        timestamp = int(time.time())
        return f"{hostname}_{timestamp}"
    
    async def start(self):
        """启动分布式协调器"""
        if not self.enabled:
            logger.info("分布式协调器未启用")
            return
        
        if self.is_running:
            logger.warning("分布式协调器已在运行")
            return
        
        self.is_running = True
        
        if self.is_master:
            # 启动主节点服务
            self.server_task = asyncio.create_task(self._start_master_server())
        else:
            # 启动工作节点，连接到主节点
            self.heartbeat_task = asyncio.create_task(self._start_worker_heartbeat())
        
        logger.info(f"分布式协调器启动 | 模式: {'主节点' if self.is_master else '工作节点'}")
    
    async def stop(self):
        """停止分布式协调器"""
        self.is_running = False
        
        if self.server_task:
            self.server_task.cancel()
            try:
                await self.server_task
            except asyncio.CancelledError:
                pass
        
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
        
        logger.info("分布式协调器停止")
    
    async def _start_master_server(self):
        """启动主节点HTTP服务器"""
        from aiohttp import web
        
        app = web.Application()
        
        # 注册路由
        app.router.add_post('/api/register', self._handle_worker_register)
        app.router.add_post('/api/heartbeat', self._handle_worker_heartbeat)
        app.router.add_post('/api/task_result', self._handle_task_result)
        app.router.add_get('/api/task', self._handle_get_task)
        app.router.add_get('/api/status', self._handle_get_status)
        
        # 启动服务器
        runner = web.AppRunner(app)
        await runner.setup()
        
        site = web.TCPSite(runner, '0.0.0.0', self.listen_port)
        await site.start()
        
        logger.info(f"主节点服务器启动 | 端口: {self.listen_port}")
        
        # 启动后台任务
        cleanup_task = asyncio.create_task(self._cleanup_offline_nodes())
        
        try:
            # 保持服务器运行
            while self.is_running:
                await asyncio.sleep(1)
        finally:
            cleanup_task.cancel()
            await runner.cleanup()
    
    async def _start_worker_heartbeat(self):
        """启动工作节点心跳"""
        # 首先注册到主节点
        await self._register_to_master()
        
        # 定期发送心跳
        while self.is_running:
            try:
                await self._send_heartbeat()
                await asyncio.sleep(self.heartbeat_interval)
            except Exception as e:
                logger.error(f"心跳发送失败: {e}")
                await asyncio.sleep(5)
    
    async def _register_to_master(self):
        """向主节点注册"""
        registration_data = {
            "node_id": self.node_id,
            "hostname": socket.gethostname(),
            "ip_address": self._get_local_ip(),
            "capabilities": {
                "max_concurrent_sessions": 5,
                "supported_browsers": ["chromium"]
            }
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"http://{self.master_host}:{self.master_port}/api/register",
                    json=registration_data
                ) as response:
                    if response.status == 200:
                        logger.info("成功注册到主节点")
                    else:
                        logger.error(f"注册失败: {response.status}")
        except Exception as e:
            logger.error(f"注册到主节点失败: {e}")
    
    async def _send_heartbeat(self):
        """发送心跳到主节点"""
        heartbeat_data = {
            "node_id": self.node_id,
            "timestamp": datetime.now().isoformat(),
            "status": "active",
            "current_load": 0  # 这里应该是实际的负载信息
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"http://{self.master_host}:{self.master_port}/api/heartbeat",
                    json=heartbeat_data
                ) as response:
                    if response.status != 200:
                        logger.warning(f"心跳响应异常: {response.status}")
        except Exception as e:
            logger.error(f"心跳发送失败: {e}")
    
    def _get_local_ip(self) -> str:
        """获取本地IP地址"""
        try:
            # 连接到一个远程地址来获取本地IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                return s.getsockname()[0]
        except Exception:
            return "127.0.0.1"
    
    async def _handle_worker_register(self, request):
        """处理工作节点注册"""
        from aiohttp import web
        
        try:
            data = await request.json()
            node_id = data["node_id"]
            
            # 创建工作节点对象
            worker = WorkerNode(
                worker_id=node_id,
                hostname=data["hostname"],
                ip_address=data["ip_address"],
                max_concurrent_sessions=data["capabilities"].get("max_concurrent_sessions", 5),
                supported_browsers=data["capabilities"].get("supported_browsers", ["chromium"])
            )
            
            # 注册节点
            self.worker_nodes[node_id] = worker
            self.node_last_seen[node_id] = datetime.now()
            
            logger.info(f"工作节点注册 | ID: {node_id} | 主机: {worker.hostname}")
            
            return web.json_response({"status": "success", "message": "注册成功"})
            
        except Exception as e:
            logger.error(f"处理节点注册失败: {e}")
            return web.json_response({"status": "error", "message": str(e)}, status=400)
    
    async def _handle_worker_heartbeat(self, request):
        """处理工作节点心跳"""
        from aiohttp import web
        
        try:
            data = await request.json()
            node_id = data["node_id"]
            
            if node_id in self.worker_nodes:
                self.node_last_seen[node_id] = datetime.now()
                self.worker_nodes[node_id].update_heartbeat()
                
                return web.json_response({"status": "success"})
            else:
                return web.json_response({"status": "error", "message": "节点未注册"}, status=404)
                
        except Exception as e:
            logger.error(f"处理心跳失败: {e}")
            return web.json_response({"status": "error", "message": str(e)}, status=400)
    
    async def _handle_get_task(self, request):
        """处理获取任务请求"""
        from aiohttp import web
        
        try:
            node_id = request.query.get("node_id")
            if not node_id or node_id not in self.worker_nodes:
                return web.json_response({"status": "error", "message": "无效节点"}, status=400)
            
            # 这里应该从任务队列获取任务
            # 暂时返回空任务
            return web.json_response({"status": "success", "task": None})
            
        except Exception as e:
            logger.error(f"处理获取任务失败: {e}")
            return web.json_response({"status": "error", "message": str(e)}, status=500)
    
    async def _handle_task_result(self, request):
        """处理任务结果"""
        from aiohttp import web
        
        try:
            data = await request.json()
            task_id = data["task_id"]
            success = data["success"]
            error_message = data.get("error_message")
            
            # 这里应该更新任务状态
            logger.info(f"任务结果 | ID: {task_id} | 成功: {success}")
            
            return web.json_response({"status": "success"})
            
        except Exception as e:
            logger.error(f"处理任务结果失败: {e}")
            return web.json_response({"status": "error", "message": str(e)}, status=400)
    
    async def _handle_get_status(self, request):
        """处理状态查询"""
        from aiohttp import web
        
        status = {
            "node_id": self.node_id,
            "is_master": self.is_master,
            "worker_count": len(self.worker_nodes),
            "active_workers": len([w for w in self.worker_nodes.values() if w.is_available]),
            "uptime": (datetime.now() - datetime.now()).total_seconds()  # 这里应该是实际的启动时间
        }
        
        return web.json_response(status)
    
    async def _cleanup_offline_nodes(self):
        """清理离线节点"""
        while self.is_running:
            try:
                now = datetime.now()
                offline_nodes = []
                
                for node_id, last_seen in self.node_last_seen.items():
                    if (now - last_seen).total_seconds() > self.node_timeout:
                        offline_nodes.append(node_id)
                
                for node_id in offline_nodes:
                    if node_id in self.worker_nodes:
                        del self.worker_nodes[node_id]
                    if node_id in self.node_last_seen:
                        del self.node_last_seen[node_id]
                    
                    logger.warning(f"清理离线节点 | ID: {node_id}")
                
                await asyncio.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"清理离线节点失败: {e}")
                await asyncio.sleep(10)
    
    def select_worker_for_task(self, task: Task) -> Optional[WorkerNode]:
        """
        为任务选择工作节点
        
        Args:
            task: 任务对象
            
        Returns:
            选择的工作节点
        """
        available_workers = [w for w in self.worker_nodes.values() if w.is_available]
        
        if not available_workers:
            return None
        
        if self.task_assignment_strategy == "round_robin":
            # 轮询策略
            return self._select_worker_round_robin(available_workers)
        elif self.task_assignment_strategy == "least_loaded":
            # 最少负载策略
            return self._select_worker_least_loaded(available_workers)
        elif self.task_assignment_strategy == "random":
            # 随机策略
            import random
            return random.choice(available_workers)
        else:
            # 默认选择第一个可用节点
            return available_workers[0]
    
    def _select_worker_round_robin(self, workers: List[WorkerNode]) -> WorkerNode:
        """轮询选择工作节点"""
        # 简化实现：按节点ID排序后轮询
        sorted_workers = sorted(workers, key=lambda w: w.worker_id)
        current_time = int(time.time())
        index = current_time % len(sorted_workers)
        return sorted_workers[index]
    
    def _select_worker_least_loaded(self, workers: List[WorkerNode]) -> WorkerNode:
        """选择负载最少的工作节点"""
        return min(workers, key=lambda w: w.current_sessions)
    
    async def distribute_task(self, task: Task) -> bool:
        """
        分发任务到工作节点
        
        Args:
            task: 要分发的任务
            
        Returns:
            是否成功分发
        """
        if not self.enabled or not self.is_master:
            return False
        
        worker = self.select_worker_for_task(task)
        if not worker:
            logger.warning("没有可用的工作节点")
            return False
        
        try:
            # 发送任务到工作节点
            task_data = {
                "task_id": task.task_id,
                "target_url": task.target_url,
                "priority": task.priority.value,
                "requirements": {
                    "proxy": task.proxy_requirements,
                    "fingerprint": task.fingerprint_requirements,
                    "behavior": task.behavior_requirements
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"http://{worker.ip_address}:{self.listen_port}/api/execute_task",
                    json=task_data
                ) as response:
                    if response.status == 200:
                        task.assign_to_worker(worker.worker_id)
                        worker.assign_session()
                        logger.info(f"任务分发成功 | 任务: {task.task_id} | 节点: {worker.worker_id}")
                        return True
                    else:
                        logger.error(f"任务分发失败 | 状态码: {response.status}")
                        return False
        
        except Exception as e:
            logger.error(f"任务分发异常: {e}")
            return False
    
    def get_cluster_status(self) -> Dict[str, Any]:
        """获取集群状态"""
        total_workers = len(self.worker_nodes)
        active_workers = len([w for w in self.worker_nodes.values() if w.is_available])
        total_capacity = sum(w.max_concurrent_sessions for w in self.worker_nodes.values())
        current_load = sum(w.current_sessions for w in self.worker_nodes.values())
        
        return {
            "cluster_id": self.node_id,
            "is_master": self.is_master,
            "total_workers": total_workers,
            "active_workers": active_workers,
            "total_capacity": total_capacity,
            "current_load": current_load,
            "load_percentage": (current_load / max(total_capacity, 1)) * 100,
            "workers": [
                {
                    "worker_id": w.worker_id,
                    "hostname": w.hostname,
                    "status": w.status,
                    "current_sessions": w.current_sessions,
                    "max_sessions": w.max_concurrent_sessions,
                    "success_rate": w.success_rate
                }
                for w in self.worker_nodes.values()
            ]
        }
