"""
行为模拟模块 - 人类行为模式库
定义和管理各种真实的人类行为模式
"""

import random
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from .models import BehaviorProfile, BehaviorPattern, MouseActionType, ScrollDirection
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("human_patterns")


@dataclass
class BehaviorSequenceTemplate:
    """行为序列模板"""
    name: str
    pattern: BehaviorPattern
    actions: List[Dict[str, Any]]
    probability_weights: List[float]
    duration_range: Tuple[float, float]
    description: str


class HumanPatterns:
    """人类行为模式库"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化人类行为模式库
        
        Args:
            config: 行为配置
        """
        self.config = config
        
        # 初始化行为模式库
        self._init_behavior_patterns()
        
        # 初始化行为序列模板
        self._init_sequence_templates()
        
        logger.debug("人类行为模式库初始化完成")
    
    def _init_behavior_patterns(self):
        """初始化行为模式"""
        self.behavior_patterns = {
            BehaviorPattern.CASUAL_BROWSING: BehaviorProfile(
                pattern=BehaviorPattern.CASUAL_BROWSING,
                mouse_speed_range=(200, 600),
                mouse_acceleration=1.0,
                click_delay_range=(0.2, 0.5),
                scroll_speed_range=(150, 400),
                scroll_distance_range=(100, 300),
                scroll_pause_probability=0.25,
                page_load_wait_range=(2.0, 6.0),
                interaction_delay_range=(0.8, 2.0),
                reading_speed=0.25,
                mistake_probability=0.03,
                back_button_probability=0.08,
                tab_switch_probability=0.05,
                attention_span=600.0,  # 10分钟
                distraction_probability=0.15
            ),
            
            BehaviorPattern.FOCUSED_READING: BehaviorProfile(
                pattern=BehaviorPattern.FOCUSED_READING,
                mouse_speed_range=(100, 400),
                mouse_acceleration=0.8,
                click_delay_range=(0.3, 0.8),
                scroll_speed_range=(80, 250),
                scroll_distance_range=(50, 200),
                scroll_pause_probability=0.45,
                page_load_wait_range=(3.0, 8.0),
                interaction_delay_range=(1.0, 3.0),
                reading_speed=0.4,
                mistake_probability=0.01,
                back_button_probability=0.03,
                tab_switch_probability=0.02,
                attention_span=1200.0,  # 20分钟
                distraction_probability=0.05
            ),
            
            BehaviorPattern.QUICK_SCANNING: BehaviorProfile(
                pattern=BehaviorPattern.QUICK_SCANNING,
                mouse_speed_range=(400, 1000),
                mouse_acceleration=1.5,
                click_delay_range=(0.1, 0.3),
                scroll_speed_range=(300, 800),
                scroll_distance_range=(200, 600),
                scroll_pause_probability=0.10,
                page_load_wait_range=(1.0, 4.0),
                interaction_delay_range=(0.3, 1.0),
                reading_speed=0.15,
                mistake_probability=0.05,
                back_button_probability=0.12,
                tab_switch_probability=0.08,
                attention_span=300.0,  # 5分钟
                distraction_probability=0.25
            ),
            
            BehaviorPattern.SHOPPING: BehaviorProfile(
                pattern=BehaviorPattern.SHOPPING,
                mouse_speed_range=(150, 500),
                mouse_acceleration=1.1,
                click_delay_range=(0.2, 0.6),
                scroll_speed_range=(100, 350),
                scroll_distance_range=(80, 250),
                scroll_pause_probability=0.35,
                page_load_wait_range=(2.5, 7.0),
                interaction_delay_range=(0.8, 2.5),
                reading_speed=0.3,
                mistake_probability=0.02,
                back_button_probability=0.15,  # 购物时经常返回比较
                tab_switch_probability=0.10,   # 经常在多个商品间切换
                attention_span=900.0,  # 15分钟
                distraction_probability=0.12
            ),
            
            BehaviorPattern.RESEARCH: BehaviorProfile(
                pattern=BehaviorPattern.RESEARCH,
                mouse_speed_range=(120, 450),
                mouse_acceleration=0.9,
                click_delay_range=(0.4, 1.0),
                scroll_speed_range=(90, 300),
                scroll_distance_range=(60, 180),
                scroll_pause_probability=0.50,
                page_load_wait_range=(3.5, 9.0),
                interaction_delay_range=(1.2, 4.0),
                reading_speed=0.45,
                mistake_probability=0.015,
                back_button_probability=0.06,
                tab_switch_probability=0.15,  # 研究时经常切换标签
                attention_span=1800.0,  # 30分钟
                distraction_probability=0.08
            ),
            
            BehaviorPattern.SOCIAL_MEDIA: BehaviorProfile(
                pattern=BehaviorPattern.SOCIAL_MEDIA,
                mouse_speed_range=(250, 700),
                mouse_acceleration=1.3,
                click_delay_range=(0.15, 0.4),
                scroll_speed_range=(200, 600),
                scroll_distance_range=(150, 400),
                scroll_pause_probability=0.20,
                page_load_wait_range=(1.5, 5.0),
                interaction_delay_range=(0.5, 1.5),
                reading_speed=0.2,
                mistake_probability=0.04,
                back_button_probability=0.05,
                tab_switch_probability=0.12,
                attention_span=450.0,  # 7.5分钟
                distraction_probability=0.30
            )
        }
    
    def _init_sequence_templates(self):
        """初始化行为序列模板"""
        self.sequence_templates = {
            "page_entry": BehaviorSequenceTemplate(
                name="页面进入",
                pattern=BehaviorPattern.CASUAL_BROWSING,
                actions=[
                    {"type": "wait", "duration_range": (2.0, 5.0), "reason": "page_load"},
                    {"type": "scroll", "direction": "down", "distance_range": (100, 300)},
                    {"type": "pause", "duration_range": (1.0, 3.0), "reason": "initial_scan"},
                ],
                probability_weights=[1.0, 0.8, 0.9],
                duration_range=(3.0, 8.0),
                description="用户进入新页面的典型行为"
            ),
            
            "content_reading": BehaviorSequenceTemplate(
                name="内容阅读",
                pattern=BehaviorPattern.FOCUSED_READING,
                actions=[
                    {"type": "scroll", "direction": "down", "distance_range": (50, 150)},
                    {"type": "pause", "duration_range": (3.0, 8.0), "reason": "reading"},
                    {"type": "scroll", "direction": "down", "distance_range": (80, 200)},
                    {"type": "pause", "duration_range": (2.0, 6.0), "reason": "reading"},
                    {"type": "scroll", "direction": "up", "distance_range": (20, 80), "probability": 0.3},
                ],
                probability_weights=[1.0, 1.0, 0.9, 0.8, 0.3],
                duration_range=(5.0, 20.0),
                description="专注阅读内容的行为模式"
            ),
            
            "quick_browse": BehaviorSequenceTemplate(
                name="快速浏览",
                pattern=BehaviorPattern.QUICK_SCANNING,
                actions=[
                    {"type": "scroll", "direction": "down", "distance_range": (200, 500)},
                    {"type": "pause", "duration_range": (0.5, 2.0), "reason": "quick_scan"},
                    {"type": "scroll", "direction": "down", "distance_range": (300, 600)},
                    {"type": "pause", "duration_range": (0.3, 1.5), "reason": "quick_scan"},
                ],
                probability_weights=[1.0, 0.7, 0.8, 0.6],
                duration_range=(1.0, 5.0),
                description="快速扫描页面内容"
            ),
            
            "shopping_comparison": BehaviorSequenceTemplate(
                name="购物比较",
                pattern=BehaviorPattern.SHOPPING,
                actions=[
                    {"type": "scroll", "direction": "down", "distance_range": (100, 250)},
                    {"type": "pause", "duration_range": (2.0, 5.0), "reason": "product_examination"},
                    {"type": "click", "target": "product_image", "probability": 0.6},
                    {"type": "pause", "duration_range": (1.0, 3.0), "reason": "image_viewing"},
                    {"type": "scroll", "direction": "down", "distance_range": (150, 300)},
                    {"type": "pause", "duration_range": (3.0, 7.0), "reason": "price_comparison"},
                ],
                probability_weights=[1.0, 1.0, 0.6, 0.6, 0.8, 0.9],
                duration_range=(6.0, 18.0),
                description="购物时比较商品的行为"
            )
        }
    
    def get_behavior_profile(self, pattern: BehaviorPattern) -> BehaviorProfile:
        """
        获取行为配置文件
        
        Args:
            pattern: 行为模式
            
        Returns:
            行为配置文件
        """
        return self.behavior_patterns.get(pattern, self.behavior_patterns[BehaviorPattern.CASUAL_BROWSING])
    
    def get_random_behavior_profile(self, exclude_patterns: Optional[List[BehaviorPattern]] = None) -> BehaviorProfile:
        """
        获取随机行为配置文件
        
        Args:
            exclude_patterns: 要排除的模式
            
        Returns:
            随机行为配置文件
        """
        available_patterns = list(self.behavior_patterns.keys())
        
        if exclude_patterns:
            available_patterns = [p for p in available_patterns if p not in exclude_patterns]
        
        if not available_patterns:
            available_patterns = [BehaviorPattern.CASUAL_BROWSING]
        
        pattern = random.choice(available_patterns)
        return self.behavior_patterns[pattern]
    
    def generate_realistic_behavior_sequence(self, page_context: Dict[str, Any],
                                           session_duration: float = 0.0) -> List[Dict[str, Any]]:
        """
        生成真实的行为序列
        
        Args:
            page_context: 页面上下文信息
            session_duration: 会话持续时间
            
        Returns:
            行为序列
        """
        sequence = []
        
        # 根据页面类型选择合适的行为模式
        page_type = page_context.get("type", "general")
        content_length = page_context.get("content_length", "medium")
        
        # 选择主要行为模式
        primary_pattern = self._select_pattern_for_page(page_type, content_length)
        
        # 生成页面进入行为
        if session_duration < 60:  # 新会话
            entry_actions = self._generate_page_entry_behavior(primary_pattern)
            sequence.extend(entry_actions)
        
        # 生成主要内容交互行为
        content_actions = self._generate_content_interaction_behavior(
            primary_pattern, page_context
        )
        sequence.extend(content_actions)
        
        # 可能的离开行为
        if random.random() < 0.3:  # 30%概率生成离开行为
            exit_actions = self._generate_page_exit_behavior(primary_pattern)
            sequence.extend(exit_actions)
        
        return sequence
    
    def _select_pattern_for_page(self, page_type: str, content_length: str) -> BehaviorPattern:
        """根据页面类型选择行为模式"""
        pattern_probabilities = {
            "article": {
                BehaviorPattern.FOCUSED_READING: 0.4,
                BehaviorPattern.CASUAL_BROWSING: 0.3,
                BehaviorPattern.QUICK_SCANNING: 0.2,
                BehaviorPattern.RESEARCH: 0.1
            },
            "product": {
                BehaviorPattern.SHOPPING: 0.5,
                BehaviorPattern.CASUAL_BROWSING: 0.3,
                BehaviorPattern.QUICK_SCANNING: 0.2
            },
            "social": {
                BehaviorPattern.SOCIAL_MEDIA: 0.6,
                BehaviorPattern.CASUAL_BROWSING: 0.3,
                BehaviorPattern.QUICK_SCANNING: 0.1
            },
            "search": {
                BehaviorPattern.QUICK_SCANNING: 0.4,
                BehaviorPattern.RESEARCH: 0.3,
                BehaviorPattern.CASUAL_BROWSING: 0.3
            },
            "general": {
                BehaviorPattern.CASUAL_BROWSING: 0.4,
                BehaviorPattern.QUICK_SCANNING: 0.3,
                BehaviorPattern.FOCUSED_READING: 0.2,
                BehaviorPattern.SOCIAL_MEDIA: 0.1
            }
        }
        
        probabilities = pattern_probabilities.get(page_type, pattern_probabilities["general"])
        
        # 根据内容长度调整概率
        if content_length == "long":
            # 长内容更可能专注阅读
            if BehaviorPattern.FOCUSED_READING in probabilities:
                probabilities[BehaviorPattern.FOCUSED_READING] *= 1.5
            if BehaviorPattern.QUICK_SCANNING in probabilities:
                probabilities[BehaviorPattern.QUICK_SCANNING] *= 0.7
        elif content_length == "short":
            # 短内容更可能快速扫描
            if BehaviorPattern.QUICK_SCANNING in probabilities:
                probabilities[BehaviorPattern.QUICK_SCANNING] *= 1.3
            if BehaviorPattern.FOCUSED_READING in probabilities:
                probabilities[BehaviorPattern.FOCUSED_READING] *= 0.8
        
        # 归一化概率
        total_prob = sum(probabilities.values())
        normalized_probs = {k: v/total_prob for k, v in probabilities.items()}
        
        # 随机选择
        rand_val = random.random()
        cumulative_prob = 0.0
        
        for pattern, prob in normalized_probs.items():
            cumulative_prob += prob
            if rand_val <= cumulative_prob:
                return pattern
        
        return BehaviorPattern.CASUAL_BROWSING  # 默认
    
    def _generate_page_entry_behavior(self, pattern: BehaviorPattern) -> List[Dict[str, Any]]:
        """生成页面进入行为"""
        template = self.sequence_templates["page_entry"]
        return self._instantiate_template(template, pattern)
    
    def _generate_content_interaction_behavior(self, pattern: BehaviorPattern, 
                                             page_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成内容交互行为"""
        content_length = page_context.get("content_length", "medium")
        
        if pattern == BehaviorPattern.FOCUSED_READING:
            template = self.sequence_templates["content_reading"]
        elif pattern == BehaviorPattern.SHOPPING:
            template = self.sequence_templates["shopping_comparison"]
        else:
            template = self.sequence_templates["quick_browse"]
        
        # 根据内容长度重复模板
        repetitions = 1
        if content_length == "long":
            repetitions = random.randint(2, 4)
        elif content_length == "medium":
            repetitions = random.randint(1, 3)
        
        actions = []
        for _ in range(repetitions):
            template_actions = self._instantiate_template(template, pattern)
            actions.extend(template_actions)
        
        return actions
    
    def _generate_page_exit_behavior(self, pattern: BehaviorPattern) -> List[Dict[str, Any]]:
        """生成页面离开行为"""
        exit_actions = []
        
        # 可能的最后一次滚动
        if random.random() < 0.4:
            exit_actions.append({
                "type": "scroll",
                "direction": random.choice(["up", "down"]),
                "distance": random.randint(50, 200),
                "duration": random.uniform(0.5, 2.0)
            })
        
        # 短暂停留
        exit_actions.append({
            "type": "pause",
            "duration": random.uniform(0.5, 2.0),
            "reason": "decision_making"
        })
        
        return exit_actions
    
    def _instantiate_template(self, template: BehaviorSequenceTemplate, 
                            pattern: BehaviorPattern) -> List[Dict[str, Any]]:
        """实例化行为模板"""
        actions = []
        behavior_profile = self.get_behavior_profile(pattern)
        
        for i, action_template in enumerate(template.actions):
            # 检查概率
            probability = action_template.get("probability", 1.0)
            if random.random() > probability:
                continue
            
            # 实例化动作
            action = action_template.copy()
            
            # 处理持续时间范围
            if "duration_range" in action:
                duration_range = action["duration_range"]
                action["duration"] = random.uniform(duration_range[0], duration_range[1])
                del action["duration_range"]
            
            # 处理距离范围
            if "distance_range" in action:
                distance_range = action["distance_range"]
                action["distance"] = random.randint(distance_range[0], distance_range[1])
                del action["distance_range"]
            
            actions.append(action)
        
        return actions
    
    def adapt_behavior_to_fatigue(self, base_profile: BehaviorProfile, 
                                 fatigue_level: float) -> BehaviorProfile:
        """
        根据疲劳度调整行为配置
        
        Args:
            base_profile: 基础行为配置
            fatigue_level: 疲劳度 (0.0-1.0)
            
        Returns:
            调整后的行为配置
        """
        # 创建配置副本
        adapted_profile = BehaviorProfile(
            pattern=base_profile.pattern,
            mouse_speed_range=base_profile.mouse_speed_range,
            mouse_acceleration=base_profile.mouse_acceleration,
            click_delay_range=base_profile.click_delay_range,
            scroll_speed_range=base_profile.scroll_speed_range,
            scroll_distance_range=base_profile.scroll_distance_range,
            scroll_pause_probability=base_profile.scroll_pause_probability,
            page_load_wait_range=base_profile.page_load_wait_range,
            interaction_delay_range=base_profile.interaction_delay_range,
            reading_speed=base_profile.reading_speed,
            mistake_probability=base_profile.mistake_probability,
            back_button_probability=base_profile.back_button_probability,
            tab_switch_probability=base_profile.tab_switch_probability,
            attention_span=base_profile.attention_span,
            distraction_probability=base_profile.distraction_probability
        )
        
        # 根据疲劳度调整参数
        fatigue_factor = 1.0 + fatigue_level * 0.5
        
        # 降低鼠标速度
        adapted_profile.mouse_speed_range = (
            base_profile.mouse_speed_range[0] / fatigue_factor,
            base_profile.mouse_speed_range[1] / fatigue_factor
        )
        
        # 增加延迟时间
        adapted_profile.interaction_delay_range = (
            base_profile.interaction_delay_range[0] * fatigue_factor,
            base_profile.interaction_delay_range[1] * fatigue_factor
        )
        
        # 增加错误概率
        adapted_profile.mistake_probability = min(0.1, 
            base_profile.mistake_probability * (1.0 + fatigue_level))
        
        # 增加分心概率
        adapted_profile.distraction_probability = min(0.5,
            base_profile.distraction_probability * (1.0 + fatigue_level))
        
        return adapted_profile
