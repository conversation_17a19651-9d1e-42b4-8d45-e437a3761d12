"""
行为模拟模块 - 数据模型
定义行为模拟相关的数据结构和枚举
"""

from enum import Enum
from dataclasses import dataclass, field
from typing import List, Tuple, Dict, Any, Optional
from datetime import datetime


class MouseActionType(Enum):
    """鼠标动作类型枚举"""
    MOVE = "move"
    CLICK = "click"
    DOUBLE_CLICK = "double_click"
    RIGHT_CLICK = "right_click"
    HOVER = "hover"
    DRAG = "drag"
    SCROLL_WHEEL = "scroll_wheel"


class ScrollDirection(Enum):
    """滚动方向枚举"""
    UP = "up"
    DOWN = "down"
    LEFT = "left"
    RIGHT = "right"


class BehaviorPattern(Enum):
    """行为模式枚举"""
    CASUAL_BROWSING = "casual_browsing"      # 休闲浏览
    FOCUSED_READING = "focused_reading"      # 专注阅读
    QUICK_SCANNING = "quick_scanning"        # 快速扫描
    SHOPPING = "shopping"                    # 购物行为
    RESEARCH = "research"                    # 研究查找
    SOCIAL_MEDIA = "social_media"            # 社交媒体


@dataclass
class Point:
    """坐标点"""
    x: float
    y: float
    
    def distance_to(self, other: "Point") -> float:
        """计算到另一个点的距离"""
        return ((self.x - other.x) ** 2 + (self.y - other.y) ** 2) ** 0.5


@dataclass
class MouseAction:
    """鼠标动作"""
    action_type: MouseActionType
    start_point: Point
    end_point: Optional[Point] = None
    duration: float = 0.0  # 动作持续时间(秒)
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 贝塞尔曲线控制点（用于移动轨迹）
    control_points: List[Point] = field(default_factory=list)
    
    # 动作特定参数
    button: str = "left"  # 鼠标按键
    clicks: int = 1       # 点击次数
    scroll_delta: int = 0 # 滚动量
    
    # 人性化参数
    speed_variance: float = 0.0    # 速度变化
    pause_probability: float = 0.0 # 暂停概率
    micro_movements: bool = False  # 微小移动


@dataclass
class ScrollAction:
    """滚动动作"""
    direction: ScrollDirection
    distance: int           # 滚动距离(像素)
    duration: float         # 滚动持续时间(秒)
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 滚动特性
    scroll_type: str = "smooth"  # smooth, instant, segmented
    segments: int = 1            # 分段数量
    pause_between_segments: float = 0.0  # 段间暂停时间
    
    # 惯性参数
    initial_speed: float = 0.0   # 初始速度
    deceleration: float = 0.0    # 减速度
    
    # 人性化参数
    speed_variance: float = 0.0  # 速度变化
    overshoot: bool = False      # 是否过冲


@dataclass
class TimingAction:
    """时间控制动作"""
    action_name: str
    delay: float            # 延迟时间(秒)
    variance: float = 0.0   # 时间变化范围
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 延迟类型
    delay_type: str = "normal"  # normal, exponential, uniform
    
    # 上下文信息
    context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BehaviorProfile:
    """行为配置文件"""
    pattern: BehaviorPattern
    
    # 鼠标行为参数
    mouse_speed_range: Tuple[float, float] = (100, 800)  # 像素/秒
    mouse_acceleration: float = 1.2
    click_delay_range: Tuple[float, float] = (0.1, 0.3)
    double_click_interval: float = 0.25
    
    # 滚动行为参数
    scroll_speed_range: Tuple[int, int] = (100, 800)     # 像素/秒
    scroll_distance_range: Tuple[int, int] = (100, 500)  # 像素
    scroll_pause_probability: float = 0.15
    
    # 时间控制参数
    page_load_wait_range: Tuple[float, float] = (2.0, 8.0)
    interaction_delay_range: Tuple[float, float] = (0.5, 2.5)
    reading_speed: float = 0.3  # 每个单词的阅读时间(秒)
    
    # 真实性参数
    mistake_probability: float = 0.02    # 操作失误概率
    back_button_probability: float = 0.05 # 使用后退按钮概率
    tab_switch_probability: float = 0.03  # 切换标签概率
    
    # 注意力模型
    attention_span: float = 300.0        # 注意力持续时间(秒)
    distraction_probability: float = 0.1 # 分心概率
    
    # 创建时间
    created_at: datetime = field(default_factory=datetime.now)
    profile_id: str = ""
    
    def __post_init__(self):
        if not self.profile_id:
            import uuid
            self.profile_id = str(uuid.uuid4())


@dataclass
class BehaviorSequence:
    """行为序列"""
    sequence_id: str
    actions: List[Any] = field(default_factory=list)  # MouseAction, ScrollAction, TimingAction的混合
    total_duration: float = 0.0
    created_at: datetime = field(default_factory=datetime.now)
    
    # 序列元数据
    page_url: str = ""
    viewport_size: Tuple[int, int] = (1920, 1080)
    behavior_pattern: Optional[BehaviorPattern] = None
    
    def add_action(self, action):
        """添加动作到序列"""
        self.actions.append(action)
        if hasattr(action, 'duration'):
            self.total_duration += action.duration
    
    def get_actions_by_type(self, action_type: type) -> List[Any]:
        """根据类型获取动作"""
        return [action for action in self.actions if isinstance(action, action_type)]
    
    def get_mouse_actions(self) -> List[MouseAction]:
        """获取鼠标动作"""
        return self.get_actions_by_type(MouseAction)
    
    def get_scroll_actions(self) -> List[ScrollAction]:
        """获取滚动动作"""
        return self.get_actions_by_type(ScrollAction)
    
    def get_timing_actions(self) -> List[TimingAction]:
        """获取时间控制动作"""
        return self.get_actions_by_type(TimingAction)


@dataclass
class PageInteractionStats:
    """页面交互统计"""
    page_url: str
    visit_duration: float = 0.0
    mouse_movements: int = 0
    mouse_clicks: int = 0
    scroll_events: int = 0
    scroll_distance: int = 0
    
    # 阅读行为
    reading_time: float = 0.0
    words_read: int = 0
    
    # 交互热点
    interaction_points: List[Point] = field(default_factory=list)
    
    # 时间戳
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    
    def calculate_engagement_score(self) -> float:
        """计算参与度分数"""
        if self.visit_duration == 0:
            return 0.0
        
        # 基于多个因素计算参与度
        movement_score = min(self.mouse_movements / 100, 1.0) * 0.3
        click_score = min(self.mouse_clicks / 10, 1.0) * 0.2
        scroll_score = min(self.scroll_events / 20, 1.0) * 0.2
        time_score = min(self.visit_duration / 300, 1.0) * 0.3  # 5分钟为满分
        
        return movement_score + click_score + scroll_score + time_score


@dataclass
class HumanBehaviorConfig:
    """人类行为配置"""
    # 贝塞尔曲线参数
    bezier_control_points: int = 2
    curve_smoothness: float = 0.8
    
    # 速度模型
    base_speed: float = 400.0        # 基础速度(像素/秒)
    speed_variance: float = 0.3      # 速度变化幅度
    acceleration_time: float = 0.1   # 加速时间
    deceleration_time: float = 0.15  # 减速时间
    
    # 暂停模型
    pause_probability: float = 0.15  # 暂停概率
    pause_duration_range: Tuple[float, float] = (0.1, 0.5)
    
    # 微动模型
    micro_movement_probability: float = 0.2
    micro_movement_range: Tuple[int, int] = (1, 5)  # 像素
    
    # 错误模型
    overshoot_probability: float = 0.1
    overshoot_distance_range: Tuple[int, int] = (5, 20)
    correction_delay_range: Tuple[float, float] = (0.1, 0.3)
    
    # 疲劳模型
    fatigue_factor: float = 0.0      # 疲劳因子(0-1)
    fatigue_increase_rate: float = 0.001  # 疲劳增长率
    
    def apply_fatigue(self, base_value: float) -> float:
        """应用疲劳效果"""
        return base_value * (1 + self.fatigue_factor * 0.5)
    
    def increase_fatigue(self, duration: float):
        """增加疲劳度"""
        self.fatigue_factor = min(1.0, self.fatigue_factor + duration * self.fatigue_increase_rate)
