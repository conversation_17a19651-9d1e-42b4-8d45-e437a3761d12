"""
行为模拟模块 - 行为模拟器
统一管理所有真人行为模拟功能
"""

import asyncio
import random
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from .models import (
    BehaviorSequence, BehaviorProfile, BehaviorPattern, 
    MouseAction, ScrollAction, TimingAction, PageInteractionStats
)
from .mouse_movement import MouseMovementSimulator
from .scrolling_behavior import ScrollingBehaviorSimulator
from .timing_controller import TimingController
from .human_patterns import HumanPatterns
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("behavior_simulator")


class BehaviorSimulator:
    """行为模拟器 - 真人行为模拟系统的中央控制器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化行为模拟器
        
        Args:
            config: 行为模拟配置
        """
        self.config = config
        
        # 初始化各个组件
        self.mouse_simulator = MouseMovementSimulator(config.get("mouse_movement", {}))
        self.scroll_simulator = ScrollingBehaviorSimulator(config.get("scrolling", {}))
        self.timing_controller = TimingController(config.get("timing", {}))
        self.human_patterns = HumanPatterns(config)
        
        # 当前会话状态
        self.current_behavior_profile: Optional[BehaviorProfile] = None
        self.session_start_time = datetime.now()
        self.total_interactions = 0
        self.page_stats: Dict[str, PageInteractionStats] = {}
        
        logger.info("行为模拟器初始化完成")
    
    def start_session(self, behavior_pattern: Optional[BehaviorPattern] = None) -> BehaviorProfile:
        """
        开始新的行为会话
        
        Args:
            behavior_pattern: 指定的行为模式
            
        Returns:
            选择的行为配置文件
        """
        if behavior_pattern:
            self.current_behavior_profile = self.human_patterns.get_behavior_profile(behavior_pattern)
        else:
            self.current_behavior_profile = self.human_patterns.get_random_behavior_profile()
        
        self.session_start_time = datetime.now()
        self.total_interactions = 0
        
        logger.info(f"开始行为会话 | 模式: {self.current_behavior_profile.pattern.value}")
        
        return self.current_behavior_profile
    
    def generate_page_interaction_sequence(self, page_context: Dict[str, Any]) -> BehaviorSequence:
        """
        生成页面交互序列
        
        Args:
            page_context: 页面上下文信息
            
        Returns:
            行为序列
        """
        page_url = page_context.get("url", "unknown")
        viewport_size = page_context.get("viewport_size", (1920, 1080))
        page_height = page_context.get("page_height", 2000)
        
        logger.info(f"生成页面交互序列 | URL: {page_url}")
        
        # 创建行为序列
        sequence = BehaviorSequence(
            sequence_id=f"seq_{int(datetime.now().timestamp())}",
            page_url=page_url,
            viewport_size=viewport_size,
            behavior_pattern=self.current_behavior_profile.pattern if self.current_behavior_profile else None
        )
        
        # 获取当前会话持续时间
        session_duration = (datetime.now() - self.session_start_time).total_seconds()
        
        # 更新时间控制器状态
        self.timing_controller.update_session_state(session_duration)
        
        # 根据疲劳度调整行为配置
        if self.current_behavior_profile:
            fatigue_level = self.timing_controller.fatigue_factor
            adapted_profile = self.human_patterns.adapt_behavior_to_fatigue(
                self.current_behavior_profile, fatigue_level
            )
        else:
            adapted_profile = self.human_patterns.get_random_behavior_profile()
        
        # 1. 页面加载等待
        page_complexity = self._assess_page_complexity(page_context)
        load_wait = self.timing_controller.calculate_page_load_wait(
            page_complexity, adapted_profile
        )
        
        load_action = TimingAction(
            action_name="page_load_wait",
            delay=load_wait,
            context={"page_complexity": page_complexity}
        )
        sequence.add_action(load_action)
        
        # 2. 生成真实的行为模式序列
        realistic_actions = self.human_patterns.generate_realistic_behavior_sequence(
            page_context, session_duration
        )
        
        # 3. 将行为模式转换为具体动作
        for action_template in realistic_actions:
            concrete_actions = self._convert_template_to_actions(
                action_template, page_context, adapted_profile
            )
            for action in concrete_actions:
                sequence.add_action(action)
        
        # 4. 可能的随机交互
        if random.random() < 0.3:  # 30%概率添加随机交互
            random_actions = self._generate_random_interactions(
                page_context, adapted_profile
            )
            for action in random_actions:
                sequence.add_action(action)
        
        # 5. 更新统计信息
        self._update_page_stats(page_url, sequence)
        
        logger.info(f"行为序列生成完成 | 动作数量: {len(sequence.actions)} | 总时长: {sequence.total_duration:.2f}s")
        
        return sequence
    
    def _assess_page_complexity(self, page_context: Dict[str, Any]) -> str:
        """评估页面复杂度"""
        page_height = page_context.get("page_height", 1000)
        element_count = page_context.get("element_count", 50)
        has_media = page_context.get("has_media", False)
        
        complexity_score = 0
        
        # 基于页面高度
        if page_height > 3000:
            complexity_score += 2
        elif page_height > 1500:
            complexity_score += 1
        
        # 基于元素数量
        if element_count > 100:
            complexity_score += 2
        elif element_count > 50:
            complexity_score += 1
        
        # 基于媒体内容
        if has_media:
            complexity_score += 1
        
        if complexity_score >= 4:
            return "complex"
        elif complexity_score >= 2:
            return "medium"
        else:
            return "simple"
    
    def _convert_template_to_actions(self, action_template: Dict[str, Any], 
                                   page_context: Dict[str, Any],
                                   behavior_profile: BehaviorProfile) -> List[Any]:
        """将行为模板转换为具体动作"""
        actions = []
        action_type = action_template.get("type")
        
        if action_type == "wait" or action_type == "pause":
            # 等待/暂停动作
            duration = action_template.get("duration", 1.0)
            reason = action_template.get("reason", "unknown")
            
            timing_action = TimingAction(
                action_name=f"{action_type}_{reason}",
                delay=duration,
                context={"reason": reason}
            )
            actions.append(timing_action)
        
        elif action_type == "scroll":
            # 滚动动作
            direction = action_template.get("direction", "down")
            distance = action_template.get("distance", 200)
            
            from .models import ScrollDirection
            scroll_direction = ScrollDirection.DOWN if direction == "down" else ScrollDirection.UP
            
            scroll_action = self.scroll_simulator._create_scroll_action(
                scroll_direction, distance, behavior_profile
            )
            actions.append(scroll_action)
        
        elif action_type == "click":
            # 点击动作
            target = action_template.get("target", "random")
            
            # 生成随机点击位置
            viewport_size = page_context.get("viewport_size", (1920, 1080))
            click_x = random.randint(100, viewport_size[0] - 100)
            click_y = random.randint(100, viewport_size[1] - 100)
            
            from .models import Point, MouseActionType
            target_point = Point(click_x, click_y)
            
            click_sequence = self.mouse_simulator.simulate_click_sequence(
                target_point, MouseActionType.CLICK, behavior_profile
            )
            actions.extend(click_sequence)
        
        return actions
    
    def _generate_random_interactions(self, page_context: Dict[str, Any],
                                    behavior_profile: BehaviorProfile) -> List[Any]:
        """生成随机交互动作"""
        actions = []
        viewport_size = page_context.get("viewport_size", (1920, 1080))
        
        # 随机鼠标移动
        if random.random() < 0.5:
            from .models import Point, MouseActionType
            start_point = Point(
                random.randint(100, viewport_size[0] - 100),
                random.randint(100, viewport_size[1] - 100)
            )
            end_point = Point(
                random.randint(100, viewport_size[0] - 100),
                random.randint(100, viewport_size[1] - 100)
            )
            
            move_action = self.mouse_simulator.create_mouse_action(
                start_point, end_point, MouseActionType.MOVE, behavior_profile
            )
            actions.append(move_action)
        
        # 随机滚动
        if random.random() < 0.4:
            page_height = page_context.get("page_height", 2000)
            scroll_actions = self.scroll_simulator.generate_random_scroll_exploration(
                page_height, viewport_size[1], 0.3, behavior_profile
            )
            actions.extend(scroll_actions[:2])  # 最多2个滚动动作
        
        # 随机暂停
        if random.random() < 0.6:
            pause_duration = self.timing_controller.calculate_thinking_pause(
                "simple", behavior_profile
            )
            pause_action = TimingAction(
                action_name="random_pause",
                delay=pause_duration,
                context={"reason": "random_thinking"}
            )
            actions.append(pause_action)
        
        return actions
    
    def _update_page_stats(self, page_url: str, sequence: BehaviorSequence):
        """更新页面统计信息"""
        if page_url not in self.page_stats:
            self.page_stats[page_url] = PageInteractionStats(page_url=page_url)
        
        stats = self.page_stats[page_url]
        
        # 统计各种动作
        mouse_actions = sequence.get_mouse_actions()
        scroll_actions = sequence.get_scroll_actions()
        
        stats.mouse_movements += len([a for a in mouse_actions if a.action_type.value == "move"])
        stats.mouse_clicks += len([a for a in mouse_actions if "click" in a.action_type.value])
        stats.scroll_events += len(scroll_actions)
        stats.scroll_distance += sum(a.distance for a in scroll_actions)
        stats.visit_duration += sequence.total_duration
        
        # 更新结束时间
        stats.end_time = datetime.now()
    
    async def execute_behavior_sequence(self, sequence: BehaviorSequence,
                                      browser_controller: Any = None) -> Dict[str, Any]:
        """
        执行行为序列
        
        Args:
            sequence: 行为序列
            browser_controller: 浏览器控制器
            
        Returns:
            执行结果
        """
        logger.info(f"开始执行行为序列 | ID: {sequence.sequence_id}")
        
        execution_results = {
            "sequence_id": sequence.sequence_id,
            "start_time": datetime.now(),
            "actions_executed": 0,
            "actions_failed": 0,
            "total_duration": 0.0,
            "errors": []
        }
        
        start_time = datetime.now()
        
        for i, action in enumerate(sequence.actions):
            try:
                # 执行动作
                await self._execute_single_action(action, browser_controller)
                execution_results["actions_executed"] += 1
                
                logger.debug(f"动作执行成功 | 类型: {type(action).__name__} | 序号: {i+1}")
                
            except Exception as e:
                execution_results["actions_failed"] += 1
                execution_results["errors"].append({
                    "action_index": i,
                    "action_type": type(action).__name__,
                    "error": str(e)
                })
                
                logger.error(f"动作执行失败 | 类型: {type(action).__name__} | 错误: {e}")
        
        # 计算总执行时间
        end_time = datetime.now()
        execution_results["end_time"] = end_time
        execution_results["total_duration"] = (end_time - start_time).total_seconds()
        
        # 更新总交互次数
        self.total_interactions += execution_results["actions_executed"]
        
        logger.info(
            f"行为序列执行完成 | 成功: {execution_results['actions_executed']} | "
            f"失败: {execution_results['actions_failed']} | "
            f"耗时: {execution_results['total_duration']:.2f}s"
        )
        
        return execution_results
    
    async def _execute_single_action(self, action: Any, browser_controller: Any = None):
        """执行单个动作"""
        if isinstance(action, TimingAction):
            # 等待动作
            await asyncio.sleep(action.delay)
        
        elif isinstance(action, MouseAction):
            # 鼠标动作
            if browser_controller:
                await self._execute_mouse_action(action, browser_controller)
            else:
                # 模拟执行（仅等待）
                await asyncio.sleep(action.duration)
        
        elif isinstance(action, ScrollAction):
            # 滚动动作
            if browser_controller:
                await self._execute_scroll_action(action, browser_controller)
            else:
                # 模拟执行（仅等待）
                await asyncio.sleep(action.duration)
        
        else:
            logger.warning(f"未知动作类型: {type(action)}")
    
    async def _execute_mouse_action(self, action: MouseAction, browser_controller: Any):
        """执行鼠标动作"""
        # 这里需要与具体的浏览器控制器集成
        # 暂时只是等待相应的时间
        await asyncio.sleep(action.duration)
    
    async def _execute_scroll_action(self, action: ScrollAction, browser_controller: Any):
        """执行滚动动作"""
        # 这里需要与具体的浏览器控制器集成
        # 暂时只是等待相应的时间
        await asyncio.sleep(action.duration)
        
        # 如果有段间暂停，也要等待
        if action.pause_between_segments > 0:
            await asyncio.sleep(action.pause_between_segments)
    
    def get_session_statistics(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        session_duration = (datetime.now() - self.session_start_time).total_seconds()
        
        # 计算总体参与度
        total_engagement = 0.0
        if self.page_stats:
            total_engagement = sum(
                stats.calculate_engagement_score() 
                for stats in self.page_stats.values()
            ) / len(self.page_stats)
        
        return {
            "session_duration": session_duration,
            "total_interactions": self.total_interactions,
            "pages_visited": len(self.page_stats),
            "average_engagement": total_engagement,
            "current_behavior_pattern": self.current_behavior_profile.pattern.value if self.current_behavior_profile else None,
            "timing_stats": self.timing_controller.get_session_statistics(),
            "page_stats": {url: {
                "visit_duration": stats.visit_duration,
                "mouse_movements": stats.mouse_movements,
                "mouse_clicks": stats.mouse_clicks,
                "scroll_events": stats.scroll_events,
                "engagement_score": stats.calculate_engagement_score()
            } for url, stats in self.page_stats.items()}
        }
    
    def should_change_behavior_pattern(self) -> bool:
        """判断是否应该改变行为模式"""
        session_duration = (datetime.now() - self.session_start_time).total_seconds()
        
        # 基于时间的模式切换
        if session_duration > 1800:  # 30分钟后可能改变模式
            return random.random() < 0.3
        
        # 基于疲劳度的模式切换
        if self.timing_controller.fatigue_factor > 0.6:
            return random.random() < 0.4
        
        # 基于交互次数的模式切换
        if self.total_interactions > 100:
            return random.random() < 0.2
        
        return False
    
    def change_behavior_pattern(self, new_pattern: Optional[BehaviorPattern] = None):
        """改变行为模式"""
        old_pattern = self.current_behavior_profile.pattern if self.current_behavior_profile else None
        
        if new_pattern:
            self.current_behavior_profile = self.human_patterns.get_behavior_profile(new_pattern)
        else:
            # 排除当前模式，选择新模式
            exclude_patterns = [old_pattern] if old_pattern else []
            self.current_behavior_profile = self.human_patterns.get_random_behavior_profile(exclude_patterns)
        
        logger.info(f"行为模式切换 | {old_pattern} -> {self.current_behavior_profile.pattern.value}")
        
        return self.current_behavior_profile
