"""
行为模拟模块 - 鼠标移动模拟器
使用贝塞尔曲线算法生成自然的鼠标移动轨迹
"""

import math
import random
import time
from typing import List, Tuple, Optional
import numpy as np

from .models import MouseAction, MouseActionType, Point, BehaviorProfile, HumanBehaviorConfig
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("mouse_movement")


class MouseMovementSimulator:
    """鼠标移动模拟器"""
    
    def __init__(self, config: dict):
        """
        初始化鼠标移动模拟器
        
        Args:
            config: 鼠标移动配置
        """
        self.config = config
        self.algorithm = config.get("algorithm", "bezier_curves")
        self.speed_variance = config.get("speed_variance", 0.3)
        self.micro_movements = config.get("micro_movements", True)
        self.pause_probability = config.get("pause_probability", 0.15)
        
        # 人类行为配置
        self.human_config = HumanBehaviorConfig()
        
        logger.debug(f"鼠标移动模拟器初始化 | 算法: {self.algorithm}")
    
    def generate_movement_path(self, start: Point, end: Point, 
                             behavior_profile: Optional[BehaviorProfile] = None) -> List[Point]:
        """
        生成鼠标移动路径
        
        Args:
            start: 起始点
            end: 终点
            behavior_profile: 行为配置文件
            
        Returns:
            移动路径点列表
        """
        if self.algorithm == "bezier_curves":
            return self._generate_bezier_path(start, end, behavior_profile)
        elif self.algorithm == "natural_spline":
            return self._generate_spline_path(start, end, behavior_profile)
        else:
            return self._generate_linear_path(start, end, behavior_profile)
    
    def _generate_bezier_path(self, start: Point, end: Point, 
                            behavior_profile: Optional[BehaviorProfile] = None) -> List[Point]:
        """使用贝塞尔曲线生成移动路径"""
        distance = start.distance_to(end)
        
        # 根据距离确定路径点数量
        num_points = max(10, int(distance / 20))
        
        # 生成控制点
        control_points = self._generate_control_points(start, end, distance)
        
        # 生成贝塞尔曲线路径
        path_points = []
        for i in range(num_points + 1):
            t = i / num_points
            point = self._calculate_bezier_point(t, start, control_points, end)
            
            # 添加微小的随机偏移
            if self.micro_movements and i > 0 and i < num_points:
                noise_x = random.uniform(-2, 2)
                noise_y = random.uniform(-2, 2)
                point.x += noise_x
                point.y += noise_y
            
            path_points.append(point)
        
        return path_points
    
    def _generate_control_points(self, start: Point, end: Point, distance: float) -> List[Point]:
        """生成贝塞尔曲线控制点"""
        # 计算中点
        mid_x = (start.x + end.x) / 2
        mid_y = (start.y + end.y) / 2
        
        # 计算垂直方向
        dx = end.x - start.x
        dy = end.y - start.y
        
        # 避免除零错误
        if abs(dx) < 0.001 and abs(dy) < 0.001:
            return [Point(mid_x, mid_y)]
        
        # 计算垂直向量
        length = math.sqrt(dx * dx + dy * dy)
        perp_x = -dy / length
        perp_y = dx / length
        
        # 控制点偏移量（基于距离）
        offset = min(distance * 0.2, 100)  # 最大偏移100像素
        offset *= random.uniform(0.3, 1.0)  # 随机化偏移
        
        # 随机选择偏移方向
        if random.choice([True, False]):
            offset = -offset
        
        # 生成控制点
        control_points = []
        
        # 第一个控制点（靠近起点）
        cp1_x = start.x + dx * 0.25 + perp_x * offset * 0.5
        cp1_y = start.y + dy * 0.25 + perp_y * offset * 0.5
        control_points.append(Point(cp1_x, cp1_y))
        
        # 第二个控制点（靠近终点）
        cp2_x = start.x + dx * 0.75 + perp_x * offset
        cp2_y = start.y + dy * 0.75 + perp_y * offset
        control_points.append(Point(cp2_x, cp2_y))
        
        return control_points
    
    def _calculate_bezier_point(self, t: float, start: Point, 
                               control_points: List[Point], end: Point) -> Point:
        """计算贝塞尔曲线上的点"""
        if len(control_points) == 1:
            # 二次贝塞尔曲线
            cp = control_points[0]
            x = (1-t)**2 * start.x + 2*(1-t)*t * cp.x + t**2 * end.x
            y = (1-t)**2 * start.y + 2*(1-t)*t * cp.y + t**2 * end.y
        elif len(control_points) == 2:
            # 三次贝塞尔曲线
            cp1, cp2 = control_points
            x = (1-t)**3 * start.x + 3*(1-t)**2*t * cp1.x + 3*(1-t)*t**2 * cp2.x + t**3 * end.x
            y = (1-t)**3 * start.y + 3*(1-t)**2*t * cp1.y + 3*(1-t)*t**2 * cp2.y + t**3 * end.y
        else:
            # 线性插值作为后备
            x = start.x + t * (end.x - start.x)
            y = start.y + t * (end.y - start.y)
        
        return Point(x, y)
    
    def _generate_spline_path(self, start: Point, end: Point, 
                            behavior_profile: Optional[BehaviorProfile] = None) -> List[Point]:
        """使用自然样条曲线生成移动路径"""
        # 简化实现：生成几个中间点，然后用样条插值
        distance = start.distance_to(end)
        num_intermediate = max(2, int(distance / 100))
        
        # 生成中间点
        points = [start]
        for i in range(1, num_intermediate + 1):
            t = i / (num_intermediate + 1)
            
            # 基础插值点
            base_x = start.x + t * (end.x - start.x)
            base_y = start.y + t * (end.y - start.y)
            
            # 添加随机偏移
            offset_range = min(distance * 0.1, 50)
            offset_x = random.uniform(-offset_range, offset_range)
            offset_y = random.uniform(-offset_range, offset_range)
            
            points.append(Point(base_x + offset_x, base_y + offset_y))
        
        points.append(end)
        
        # 使用样条插值生成平滑路径
        return self._interpolate_spline(points)
    
    def _interpolate_spline(self, control_points: List[Point]) -> List[Point]:
        """样条插值"""
        if len(control_points) < 3:
            return control_points
        
        path_points = []
        
        for i in range(len(control_points) - 1):
            start_point = control_points[i]
            end_point = control_points[i + 1]
            
            # 在每两个控制点之间插值
            num_segments = 10
            for j in range(num_segments):
                t = j / num_segments
                
                # 简单的Catmull-Rom样条
                if i == 0:
                    p0 = start_point
                else:
                    p0 = control_points[i - 1]
                
                p1 = start_point
                p2 = end_point
                
                if i + 2 < len(control_points):
                    p3 = control_points[i + 2]
                else:
                    p3 = end_point
                
                # Catmull-Rom插值
                x = 0.5 * (
                    2 * p1.x +
                    (-p0.x + p2.x) * t +
                    (2 * p0.x - 5 * p1.x + 4 * p2.x - p3.x) * t * t +
                    (-p0.x + 3 * p1.x - 3 * p2.x + p3.x) * t * t * t
                )
                
                y = 0.5 * (
                    2 * p1.y +
                    (-p0.y + p2.y) * t +
                    (2 * p0.y - 5 * p1.y + 4 * p2.y - p3.y) * t * t +
                    (-p0.y + 3 * p1.y - 3 * p2.y + p3.y) * t * t * t
                )
                
                path_points.append(Point(x, y))
        
        return path_points
    
    def _generate_linear_path(self, start: Point, end: Point, 
                            behavior_profile: Optional[BehaviorProfile] = None) -> List[Point]:
        """生成线性移动路径（带随机偏移）"""
        distance = start.distance_to(end)
        num_points = max(5, int(distance / 30))
        
        path_points = []
        for i in range(num_points + 1):
            t = i / num_points
            
            # 线性插值
            x = start.x + t * (end.x - start.x)
            y = start.y + t * (end.y - start.y)
            
            # 添加随机偏移（除了起点和终点）
            if 0 < i < num_points:
                noise_range = min(distance * 0.05, 10)
                x += random.uniform(-noise_range, noise_range)
                y += random.uniform(-noise_range, noise_range)
            
            path_points.append(Point(x, y))
        
        return path_points
    
    def calculate_movement_timing(self, path: List[Point], 
                                behavior_profile: Optional[BehaviorProfile] = None) -> List[float]:
        """
        计算移动时间序列
        
        Args:
            path: 移动路径
            behavior_profile: 行为配置文件
            
        Returns:
            每个点的时间戳（相对于起始时间）
        """
        if len(path) < 2:
            return [0.0]
        
        # 获取速度参数
        if behavior_profile:
            min_speed, max_speed = behavior_profile.mouse_speed_range
        else:
            min_speed, max_speed = (100, 800)
        
        base_speed = (min_speed + max_speed) / 2
        
        timings = [0.0]
        current_time = 0.0
        
        for i in range(1, len(path)):
            # 计算距离
            distance = path[i-1].distance_to(path[i])
            
            # 计算速度（带变化）
            speed_factor = random.uniform(0.7, 1.3)  # ±30%变化
            current_speed = base_speed * speed_factor
            
            # 应用疲劳效果
            current_speed = self.human_config.apply_fatigue(current_speed)
            
            # 计算时间
            segment_time = distance / current_speed
            
            # 添加随机暂停
            if random.random() < self.pause_probability:
                pause_duration = random.uniform(*self.human_config.pause_duration_range)
                segment_time += pause_duration
            
            current_time += segment_time
            timings.append(current_time)
        
        return timings
    
    def create_mouse_action(self, start: Point, end: Point, 
                          action_type: MouseActionType = MouseActionType.MOVE,
                          behavior_profile: Optional[BehaviorProfile] = None) -> MouseAction:
        """
        创建鼠标动作
        
        Args:
            start: 起始点
            end: 终点
            action_type: 动作类型
            behavior_profile: 行为配置文件
            
        Returns:
            鼠标动作对象
        """
        # 生成移动路径
        path = self.generate_movement_path(start, end, behavior_profile)
        
        # 计算时间序列
        timings = self.calculate_movement_timing(path, behavior_profile)
        
        # 创建动作
        action = MouseAction(
            action_type=action_type,
            start_point=start,
            end_point=end,
            duration=timings[-1] if timings else 0.0,
            control_points=path[1:-1],  # 排除起点和终点
            speed_variance=self.speed_variance,
            pause_probability=self.pause_probability,
            micro_movements=self.micro_movements
        )
        
        return action
    
    def simulate_click_sequence(self, target: Point, 
                              click_type: MouseActionType = MouseActionType.CLICK,
                              behavior_profile: Optional[BehaviorProfile] = None) -> List[MouseAction]:
        """
        模拟点击序列（包括移动到目标和点击）
        
        Args:
            target: 目标点
            click_type: 点击类型
            behavior_profile: 行为配置文件
            
        Returns:
            动作序列
        """
        actions = []
        
        # 假设当前鼠标位置（实际应该从浏览器获取）
        current_pos = Point(
            random.uniform(100, 1820),
            random.uniform(100, 980)
        )
        
        # 1. 移动到目标
        move_action = self.create_mouse_action(
            current_pos, target, MouseActionType.MOVE, behavior_profile
        )
        actions.append(move_action)
        
        # 2. 可能的微调移动
        if random.random() < 0.3:  # 30%概率进行微调
            adjustment = Point(
                target.x + random.uniform(-3, 3),
                target.y + random.uniform(-3, 3)
            )
            adjust_action = self.create_mouse_action(
                target, adjustment, MouseActionType.MOVE, behavior_profile
            )
            actions.append(adjust_action)
            target = adjustment
        
        # 3. 点击动作
        click_action = MouseAction(
            action_type=click_type,
            start_point=target,
            end_point=target,
            duration=random.uniform(0.05, 0.15),  # 点击持续时间
            clicks=2 if click_type == MouseActionType.DOUBLE_CLICK else 1
        )
        actions.append(click_action)
        
        return actions
