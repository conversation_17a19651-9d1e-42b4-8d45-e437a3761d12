"""
行为模拟模块 - 滚动行为模拟器
模拟真实用户的页面滚动行为，包括分段滚动、惯性滚动等
"""

import random
import math
from typing import List, Tuple, Optional
import numpy as np

from .models import ScrollAction, ScrollDirection, BehaviorProfile, BehaviorPattern
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("scrolling_behavior")


class ScrollingBehaviorSimulator:
    """滚动行为模拟器"""
    
    def __init__(self, config: dict):
        """
        初始化滚动行为模拟器
        
        Args:
            config: 滚动行为配置
        """
        self.config = config
        self.method = config.get("method", "segmented")
        self.speed_range = config.get("speed_range", [100, 800])
        self.pause_duration = config.get("pause_duration", [0.5, 3.0])
        
        logger.debug(f"滚动行为模拟器初始化 | 方法: {self.method}")
    
    def generate_scroll_sequence(self, page_height: int, viewport_height: int,
                               behavior_profile: Optional[BehaviorProfile] = None,
                               reading_content: bool = True) -> List[ScrollAction]:
        """
        生成滚动序列
        
        Args:
            page_height: 页面总高度
            viewport_height: 视口高度
            behavior_profile: 行为配置文件
            reading_content: 是否在阅读内容
            
        Returns:
            滚动动作序列
        """
        if page_height <= viewport_height:
            # 页面内容不需要滚动
            return []
        
        scrollable_height = page_height - viewport_height
        
        if self.method == "segmented":
            return self._generate_segmented_scroll(
                scrollable_height, behavior_profile, reading_content
            )
        elif self.method == "smooth":
            return self._generate_smooth_scroll(
                scrollable_height, behavior_profile, reading_content
            )
        elif self.method == "inertial":
            return self._generate_inertial_scroll(
                scrollable_height, behavior_profile, reading_content
            )
        else:
            return self._generate_basic_scroll(
                scrollable_height, behavior_profile, reading_content
            )
    
    def _generate_segmented_scroll(self, total_distance: int, 
                                 behavior_profile: Optional[BehaviorProfile] = None,
                                 reading_content: bool = True) -> List[ScrollAction]:
        """生成分段滚动序列"""
        actions = []
        current_position = 0
        
        # 根据行为模式调整滚动参数
        if behavior_profile:
            pattern = behavior_profile.pattern
            if pattern == BehaviorPattern.QUICK_SCANNING:
                segment_size_range = (200, 500)
                pause_probability = 0.1
            elif pattern == BehaviorPattern.FOCUSED_READING:
                segment_size_range = (100, 300)
                pause_probability = 0.4
            elif pattern == BehaviorPattern.CASUAL_BROWSING:
                segment_size_range = (150, 400)
                pause_probability = 0.25
            else:
                segment_size_range = (150, 350)
                pause_probability = 0.2
        else:
            segment_size_range = (150, 350)
            pause_probability = 0.2
        
        while current_position < total_distance:
            # 计算本次滚动距离
            remaining_distance = total_distance - current_position
            max_segment = min(remaining_distance, segment_size_range[1])
            segment_distance = random.randint(
                min(segment_size_range[0], max_segment),
                max_segment
            )
            
            # 生成滚动动作
            action = self._create_scroll_action(
                ScrollDirection.DOWN,
                segment_distance,
                behavior_profile
            )
            actions.append(action)
            
            current_position += segment_distance
            
            # 添加阅读暂停
            if reading_content and random.random() < pause_probability:
                pause_duration = self._calculate_reading_pause(
                    segment_distance, behavior_profile
                )
                if pause_duration > 0:
                    action.pause_between_segments = pause_duration
            
            # 偶尔向上滚动一点（重新查看内容）
            if random.random() < 0.1 and current_position > 100:
                backtrack_distance = random.randint(20, 100)
                backtrack_action = self._create_scroll_action(
                    ScrollDirection.UP,
                    backtrack_distance,
                    behavior_profile
                )
                actions.append(backtrack_action)
                current_position -= backtrack_distance
        
        return actions
    
    def _generate_smooth_scroll(self, total_distance: int,
                              behavior_profile: Optional[BehaviorProfile] = None,
                              reading_content: bool = True) -> List[ScrollAction]:
        """生成平滑滚动序列"""
        actions = []
        
        # 平滑滚动通常是一次性的，但可能有多个阶段
        num_phases = random.randint(1, 3)
        distance_per_phase = total_distance // num_phases
        
        for i in range(num_phases):
            if i == num_phases - 1:
                # 最后一个阶段滚动剩余距离
                phase_distance = total_distance - (distance_per_phase * i)
            else:
                phase_distance = distance_per_phase
            
            # 创建平滑滚动动作
            action = ScrollAction(
                direction=ScrollDirection.DOWN,
                distance=phase_distance,
                duration=self._calculate_smooth_scroll_duration(phase_distance),
                scroll_type="smooth",
                initial_speed=random.uniform(200, 600),
                deceleration=random.uniform(50, 150)
            )
            
            actions.append(action)
            
            # 阶段间暂停
            if i < num_phases - 1 and reading_content:
                pause_duration = random.uniform(1.0, 3.0)
                action.pause_between_segments = pause_duration
        
        return actions
    
    def _generate_inertial_scroll(self, total_distance: int,
                                behavior_profile: Optional[BehaviorProfile] = None,
                                reading_content: bool = True) -> List[ScrollAction]:
        """生成惯性滚动序列"""
        actions = []
        current_position = 0
        
        while current_position < total_distance:
            # 初始滚动冲量
            initial_speed = random.uniform(300, 800)
            deceleration = random.uniform(100, 300)
            
            # 计算惯性滚动距离
            # 使用物理公式: distance = initial_speed^2 / (2 * deceleration)
            inertial_distance = (initial_speed ** 2) / (2 * deceleration)
            inertial_distance = min(inertial_distance, total_distance - current_position)
            
            # 计算滚动时间
            duration = initial_speed / deceleration
            
            # 创建惯性滚动动作
            action = ScrollAction(
                direction=ScrollDirection.DOWN,
                distance=int(inertial_distance),
                duration=duration,
                scroll_type="inertial",
                initial_speed=initial_speed,
                deceleration=deceleration,
                overshoot=random.random() < 0.2  # 20%概率过冲
            )
            
            actions.append(action)
            current_position += inertial_distance
            
            # 惯性滚动后的暂停
            if reading_content and current_position < total_distance:
                pause_duration = random.uniform(2.0, 5.0)
                action.pause_between_segments = pause_duration
        
        return actions
    
    def _generate_basic_scroll(self, total_distance: int,
                             behavior_profile: Optional[BehaviorProfile] = None,
                             reading_content: bool = True) -> List[ScrollAction]:
        """生成基础滚动序列"""
        # 简单的一次性滚动
        action = self._create_scroll_action(
            ScrollDirection.DOWN,
            total_distance,
            behavior_profile
        )
        
        return [action]
    
    def _create_scroll_action(self, direction: ScrollDirection, distance: int,
                            behavior_profile: Optional[BehaviorProfile] = None) -> ScrollAction:
        """创建滚动动作"""
        # 计算滚动速度
        if behavior_profile:
            speed_range = behavior_profile.scroll_speed_range
        else:
            speed_range = self.speed_range
        
        speed = random.uniform(speed_range[0], speed_range[1])
        
        # 应用速度变化
        speed_variance = random.uniform(0.8, 1.2)
        actual_speed = speed * speed_variance
        
        # 计算持续时间
        duration = distance / actual_speed
        
        # 创建动作
        action = ScrollAction(
            direction=direction,
            distance=distance,
            duration=duration,
            scroll_type=self.method,
            initial_speed=actual_speed,
            speed_variance=speed_variance - 1.0
        )
        
        return action
    
    def _calculate_reading_pause(self, scroll_distance: int,
                               behavior_profile: Optional[BehaviorProfile] = None) -> float:
        """计算阅读暂停时间"""
        if not behavior_profile:
            return random.uniform(0.5, 2.0)
        
        # 估算可见文本量（简化计算）
        estimated_lines = scroll_distance // 20  # 假设每行20像素
        estimated_words = estimated_lines * 10   # 假设每行10个单词
        
        # 根据阅读速度计算时间
        reading_time = estimated_words * behavior_profile.reading_speed
        
        # 添加随机变化
        variance = random.uniform(0.5, 1.5)
        return reading_time * variance
    
    def _calculate_smooth_scroll_duration(self, distance: int) -> float:
        """计算平滑滚动持续时间"""
        # 基于距离的非线性时间计算
        base_time = distance / 400  # 基础时间
        
        # 添加缓动效果
        easing_factor = 1.2
        return base_time * easing_factor
    
    def simulate_scroll_to_element(self, element_position: int, viewport_height: int,
                                 current_scroll: int = 0,
                                 behavior_profile: Optional[BehaviorProfile] = None) -> List[ScrollAction]:
        """
        模拟滚动到特定元素
        
        Args:
            element_position: 元素在页面中的位置
            viewport_height: 视口高度
            current_scroll: 当前滚动位置
            behavior_profile: 行为配置文件
            
        Returns:
            滚动动作序列
        """
        # 计算目标滚动位置（将元素置于视口中央）
        target_scroll = max(0, element_position - viewport_height // 2)
        scroll_distance = abs(target_scroll - current_scroll)
        
        if scroll_distance < 10:
            # 距离太小，不需要滚动
            return []
        
        # 确定滚动方向
        direction = ScrollDirection.DOWN if target_scroll > current_scroll else ScrollDirection.UP
        
        # 生成滚动序列
        if scroll_distance > 500:
            # 长距离滚动，使用分段
            return self._generate_segmented_scroll_to_target(
                scroll_distance, direction, behavior_profile
            )
        else:
            # 短距离滚动，直接滚动
            action = self._create_scroll_action(direction, scroll_distance, behavior_profile)
            return [action]
    
    def _generate_segmented_scroll_to_target(self, total_distance: int, 
                                           direction: ScrollDirection,
                                           behavior_profile: Optional[BehaviorProfile] = None) -> List[ScrollAction]:
        """生成分段滚动到目标"""
        actions = []
        remaining_distance = total_distance
        
        while remaining_distance > 0:
            # 计算本次滚动距离
            if remaining_distance > 300:
                segment_distance = random.randint(200, 400)
            else:
                segment_distance = remaining_distance
            
            segment_distance = min(segment_distance, remaining_distance)
            
            # 创建滚动动作
            action = self._create_scroll_action(direction, segment_distance, behavior_profile)
            
            # 最后一段滚动可能需要微调
            if remaining_distance <= segment_distance:
                # 可能过冲然后回调
                if random.random() < 0.3:
                    overshoot = random.randint(10, 50)
                    action.distance += overshoot
                    action.overshoot = True
                    
                    # 添加回调动作
                    opposite_direction = ScrollDirection.UP if direction == ScrollDirection.DOWN else ScrollDirection.DOWN
                    correction_action = self._create_scroll_action(
                        opposite_direction, overshoot, behavior_profile
                    )
                    correction_action.duration *= 0.5  # 更快的纠正
                    actions.append(action)
                    actions.append(correction_action)
                    break
            
            actions.append(action)
            remaining_distance -= segment_distance
            
            # 添加短暂暂停
            if remaining_distance > 0:
                action.pause_between_segments = random.uniform(0.1, 0.5)
        
        return actions
    
    def generate_random_scroll_exploration(self, page_height: int, viewport_height: int,
                                         exploration_ratio: float = 0.7,
                                         behavior_profile: Optional[BehaviorProfile] = None) -> List[ScrollAction]:
        """
        生成随机滚动探索序列（模拟用户浏览页面）
        
        Args:
            page_height: 页面总高度
            viewport_height: 视口高度
            exploration_ratio: 探索比例（0-1）
            behavior_profile: 行为配置文件
            
        Returns:
            滚动动作序列
        """
        actions = []
        scrollable_height = page_height - viewport_height
        target_exploration = int(scrollable_height * exploration_ratio)
        
        current_position = 0
        explored_positions = set()
        
        while len(explored_positions) < target_exploration // 100:  # 每100像素算一个探索点
            # 随机选择滚动方向和距离
            if current_position == 0:
                direction = ScrollDirection.DOWN
            elif current_position >= scrollable_height:
                direction = ScrollDirection.UP
            else:
                direction = random.choice([ScrollDirection.DOWN, ScrollDirection.UP])
            
            # 计算滚动距离
            if direction == ScrollDirection.DOWN:
                max_distance = min(400, scrollable_height - current_position)
            else:
                max_distance = min(400, current_position)
            
            if max_distance <= 0:
                break
            
            distance = random.randint(50, max_distance)
            
            # 创建滚动动作
            action = self._create_scroll_action(direction, distance, behavior_profile)
            actions.append(action)
            
            # 更新位置
            if direction == ScrollDirection.DOWN:
                current_position += distance
            else:
                current_position -= distance
            
            # 记录探索位置
            explored_positions.add(current_position // 100)
            
            # 添加观察暂停
            if random.random() < 0.4:
                action.pause_between_segments = random.uniform(1.0, 4.0)
        
        return actions
