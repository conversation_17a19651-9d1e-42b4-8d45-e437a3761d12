"""
行为模拟模块 - 时间控制器
控制各种行为的时间间隔，模拟真实用户的时间感知
"""

import random
import math
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

from .models import TimingAction, BehaviorProfile, BehaviorPattern
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("timing_controller")


class TimingController:
    """时间控制器"""
    
    def __init__(self, config: dict):
        """
        初始化时间控制器
        
        Args:
            config: 时间控制配置
        """
        self.config = config
        self.page_load_wait = config.get("page_load_wait", [2, 8])
        self.interaction_delay = config.get("interaction_delay", [0.5, 2.5])
        self.reading_time_per_word = config.get("reading_time_per_word", [0.2, 0.4])
        
        # 时间模型参数
        self.fatigue_factor = 0.0  # 疲劳因子
        self.attention_level = 1.0  # 注意力水平
        self.session_start_time = datetime.now()
        
        logger.debug("时间控制器初始化完成")
    
    def calculate_page_load_wait(self, page_complexity: str = "medium",
                               behavior_profile: Optional[BehaviorProfile] = None) -> float:
        """
        计算页面加载等待时间
        
        Args:
            page_complexity: 页面复杂度 (simple, medium, complex)
            behavior_profile: 行为配置文件
            
        Returns:
            等待时间（秒）
        """
        # 基础等待时间
        if behavior_profile:
            base_range = behavior_profile.page_load_wait_range
        else:
            base_range = self.page_load_wait
        
        base_wait = random.uniform(base_range[0], base_range[1])
        
        # 根据页面复杂度调整
        complexity_multipliers = {
            "simple": 0.7,
            "medium": 1.0,
            "complex": 1.5
        }
        
        multiplier = complexity_multipliers.get(page_complexity, 1.0)
        adjusted_wait = base_wait * multiplier
        
        # 应用疲劳和注意力因子
        adjusted_wait = self._apply_human_factors(adjusted_wait)
        
        return max(0.5, adjusted_wait)  # 最少等待0.5秒
    
    def calculate_interaction_delay(self, interaction_type: str = "click",
                                  complexity: str = "medium",
                                  behavior_profile: Optional[BehaviorProfile] = None) -> float:
        """
        计算交互延迟时间
        
        Args:
            interaction_type: 交互类型 (click, type, scroll, hover)
            complexity: 交互复杂度
            behavior_profile: 行为配置文件
            
        Returns:
            延迟时间（秒）
        """
        # 基础延迟时间
        if behavior_profile:
            base_range = behavior_profile.interaction_delay_range
        else:
            base_range = self.interaction_delay
        
        base_delay = random.uniform(base_range[0], base_range[1])
        
        # 根据交互类型调整
        type_multipliers = {
            "click": 1.0,
            "double_click": 0.8,
            "right_click": 1.2,
            "type": 1.5,
            "scroll": 0.6,
            "hover": 0.4,
            "drag": 1.8
        }
        
        multiplier = type_multipliers.get(interaction_type, 1.0)
        adjusted_delay = base_delay * multiplier
        
        # 根据复杂度调整
        complexity_multipliers = {
            "simple": 0.8,
            "medium": 1.0,
            "complex": 1.4
        }
        
        complexity_multiplier = complexity_multipliers.get(complexity, 1.0)
        adjusted_delay *= complexity_multiplier
        
        # 应用人类因子
        adjusted_delay = self._apply_human_factors(adjusted_delay)
        
        return max(0.1, adjusted_delay)  # 最少延迟0.1秒
    
    def calculate_reading_time(self, text_content: str,
                             behavior_profile: Optional[BehaviorProfile] = None) -> float:
        """
        计算阅读时间
        
        Args:
            text_content: 文本内容
            behavior_profile: 行为配置文件
            
        Returns:
            阅读时间（秒）
        """
        # 计算单词数量
        word_count = len(text_content.split())
        
        # 获取阅读速度
        if behavior_profile:
            reading_speed = behavior_profile.reading_speed
        else:
            reading_speed = random.uniform(*self.reading_time_per_word)
        
        # 基础阅读时间
        base_reading_time = word_count * reading_speed
        
        # 根据行为模式调整
        if behavior_profile:
            pattern = behavior_profile.pattern
            if pattern == BehaviorPattern.QUICK_SCANNING:
                base_reading_time *= 0.3  # 快速扫描
            elif pattern == BehaviorPattern.FOCUSED_READING:
                base_reading_time *= 1.5  # 专注阅读
            elif pattern == BehaviorPattern.CASUAL_BROWSING:
                base_reading_time *= 0.8  # 休闲浏览
        
        # 应用人类因子
        adjusted_time = self._apply_human_factors(base_reading_time)
        
        # 添加随机变化
        variance = random.uniform(0.7, 1.3)
        final_time = adjusted_time * variance
        
        return max(1.0, final_time)  # 最少阅读1秒
    
    def calculate_thinking_pause(self, decision_complexity: str = "medium",
                               behavior_profile: Optional[BehaviorProfile] = None) -> float:
        """
        计算思考暂停时间
        
        Args:
            decision_complexity: 决策复杂度 (simple, medium, complex)
            behavior_profile: 行为配置文件
            
        Returns:
            思考时间（秒）
        """
        # 基础思考时间
        base_times = {
            "simple": (0.5, 1.5),
            "medium": (1.0, 3.0),
            "complex": (2.0, 6.0)
        }
        
        time_range = base_times.get(decision_complexity, base_times["medium"])
        base_thinking_time = random.uniform(time_range[0], time_range[1])
        
        # 应用人类因子
        adjusted_time = self._apply_human_factors(base_thinking_time)
        
        return adjusted_time
    
    def calculate_distraction_pause(self, behavior_profile: Optional[BehaviorProfile] = None) -> float:
        """
        计算分心暂停时间
        
        Args:
            behavior_profile: 行为配置文件
            
        Returns:
            分心时间（秒）
        """
        # 分心时间通常较长且变化较大
        base_distraction = random.uniform(3.0, 15.0)
        
        # 根据注意力水平调整
        attention_factor = 2.0 - self.attention_level  # 注意力越低，分心时间越长
        adjusted_time = base_distraction * attention_factor
        
        return adjusted_time
    
    def _apply_human_factors(self, base_time: float) -> float:
        """
        应用人类因子（疲劳、注意力等）
        
        Args:
            base_time: 基础时间
            
        Returns:
            调整后的时间
        """
        # 应用疲劳因子
        fatigue_multiplier = 1.0 + self.fatigue_factor * 0.5
        
        # 应用注意力因子
        attention_multiplier = 2.0 - self.attention_level
        
        # 综合调整
        adjusted_time = base_time * fatigue_multiplier * attention_multiplier
        
        return adjusted_time
    
    def update_session_state(self, elapsed_time: float):
        """
        更新会话状态（疲劳度、注意力等）
        
        Args:
            elapsed_time: 已经过的时间（秒）
        """
        # 更新疲劳度（随时间增加）
        fatigue_increase_rate = 0.0001  # 每秒增加的疲劳度
        self.fatigue_factor = min(1.0, self.fatigue_factor + elapsed_time * fatigue_increase_rate)
        
        # 更新注意力水平（随时间和疲劳度下降）
        attention_decay_rate = 0.00005
        fatigue_impact = self.fatigue_factor * 0.3
        
        self.attention_level = max(0.3, self.attention_level - 
                                 (elapsed_time * attention_decay_rate + fatigue_impact))
        
        logger.debug(f"会话状态更新 | 疲劳度: {self.fatigue_factor:.3f} | 注意力: {self.attention_level:.3f}")
    
    def should_take_break(self, session_duration: float,
                         behavior_profile: Optional[BehaviorProfile] = None) -> bool:
        """
        判断是否应该休息
        
        Args:
            session_duration: 会话持续时间（秒）
            behavior_profile: 行为配置文件
            
        Returns:
            是否应该休息
        """
        # 基于疲劳度和注意力水平判断
        fatigue_threshold = 0.7
        attention_threshold = 0.4
        
        if self.fatigue_factor > fatigue_threshold or self.attention_level < attention_threshold:
            return True
        
        # 基于时间判断（长时间使用后需要休息）
        if behavior_profile and hasattr(behavior_profile, 'attention_span'):
            if session_duration > behavior_profile.attention_span:
                return True
        elif session_duration > 1800:  # 默认30分钟
            return True
        
        return False
    
    def calculate_break_duration(self, break_type: str = "short") -> float:
        """
        计算休息时间
        
        Args:
            break_type: 休息类型 (micro, short, long)
            
        Returns:
            休息时间（秒）
        """
        break_durations = {
            "micro": (5, 15),      # 微休息
            "short": (30, 120),    # 短休息
            "long": (300, 900)     # 长休息
        }
        
        duration_range = break_durations.get(break_type, break_durations["short"])
        break_duration = random.uniform(duration_range[0], duration_range[1])
        
        return break_duration
    
    def reset_after_break(self, break_duration: float):
        """
        休息后重置状态
        
        Args:
            break_duration: 休息时长（秒）
        """
        # 根据休息时长恢复疲劳度和注意力
        fatigue_recovery = min(self.fatigue_factor, break_duration / 600)  # 10分钟完全恢复
        attention_recovery = min(1.0 - self.attention_level, break_duration / 300)  # 5分钟完全恢复
        
        self.fatigue_factor = max(0.0, self.fatigue_factor - fatigue_recovery)
        self.attention_level = min(1.0, self.attention_level + attention_recovery)
        
        logger.info(f"休息后状态恢复 | 疲劳度: {self.fatigue_factor:.3f} | 注意力: {self.attention_level:.3f}")
    
    def create_timing_action(self, action_name: str, delay: float,
                           variance: float = 0.2,
                           delay_type: str = "normal",
                           context: Optional[Dict[str, Any]] = None) -> TimingAction:
        """
        创建时间控制动作
        
        Args:
            action_name: 动作名称
            delay: 延迟时间
            variance: 时间变化范围
            delay_type: 延迟类型
            context: 上下文信息
            
        Returns:
            时间控制动作
        """
        # 应用时间变化
        if delay_type == "normal":
            # 正态分布变化
            actual_delay = random.normalvariate(delay, delay * variance)
        elif delay_type == "exponential":
            # 指数分布变化
            actual_delay = random.expovariate(1.0 / delay)
        elif delay_type == "uniform":
            # 均匀分布变化
            range_size = delay * variance
            actual_delay = random.uniform(delay - range_size, delay + range_size)
        else:
            actual_delay = delay
        
        # 确保延迟时间为正数
        actual_delay = max(0.01, actual_delay)
        
        return TimingAction(
            action_name=action_name,
            delay=actual_delay,
            variance=variance,
            delay_type=delay_type,
            context=context or {}
        )
    
    def get_session_statistics(self) -> Dict[str, Any]:
        """
        获取会话统计信息
        
        Returns:
            统计信息字典
        """
        current_time = datetime.now()
        session_duration = (current_time - self.session_start_time).total_seconds()
        
        return {
            "session_duration": session_duration,
            "fatigue_factor": self.fatigue_factor,
            "attention_level": self.attention_level,
            "session_start_time": self.session_start_time.isoformat(),
            "current_time": current_time.isoformat(),
            "should_take_break": self.should_take_break(session_duration)
        }
