"""
指纹生成模块 - Canvas指纹加噪器
对Canvas渲染结果进行微小的、随机的像素级修改
"""

import hashlib
import random
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from typing import Dict, List, Any, Optional, Tuple
import io
import base64

from .models import CanvasFingerprint, NoiseConfig
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("canvas_noise")


class CanvasNoiseGenerator:
    """Canvas指纹加噪器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Canvas加噪器
        
        Args:
            config: Canvas噪音配置
        """
        self.config = config
        self.enabled = config.get("enabled", True)
        self.noise_level = config.get("noise_level", 0.1)
        self.methods = config.get("methods", ["pixel_shift", "color_variance", "geometric_distortion"])
        
        # 噪音配置
        self.noise_config = NoiseConfig(
            enabled=self.enabled,
            level=self.noise_level,
            methods=self.methods
        )
        
        logger.debug(f"Canvas加噪器初始化 | 启用: {self.enabled} | 噪音级别: {self.noise_level}")
    
    def generate_canvas_fingerprint(self, width: int = 200, height: int = 50) -> CanvasFingerprint:
        """
        生成Canvas指纹
        
        Args:
            width: Canvas宽度
            height: Canvas高度
            
        Returns:
            Canvas指纹信息
        """
        # 创建基础Canvas图像
        original_image = self._create_base_canvas(width, height)
        original_hash = self._calculate_image_hash(original_image)
        
        if not self.enabled:
            return CanvasFingerprint(
                original_hash=original_hash,
                noised_hash=original_hash,
                noise_level=0.0,
                noise_methods=[],
                image_data=self._image_to_bytes(original_image)
            )
        
        # 应用噪音
        noised_image = self._apply_noise(original_image.copy())
        noised_hash = self._calculate_image_hash(noised_image)
        
        logger.debug(f"Canvas指纹生成 | 原始哈希: {original_hash[:8]}... | 加噪哈希: {noised_hash[:8]}...")
        
        return CanvasFingerprint(
            original_hash=original_hash,
            noised_hash=noised_hash,
            noise_level=self.noise_level,
            noise_methods=self.methods.copy(),
            image_data=self._image_to_bytes(noised_image)
        )
    
    def _create_base_canvas(self, width: int, height: int) -> Image.Image:
        """创建基础Canvas图像"""
        # 创建白色背景
        image = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(image)
        
        # 绘制一些基本图形来模拟Canvas指纹测试
        # 这些图形模拟了常见的Canvas指纹检测代码
        
        # 绘制矩形
        draw.rectangle([10, 10, 50, 30], fill='red', outline='black')
        
        # 绘制圆形
        draw.ellipse([60, 10, 100, 30], fill='blue', outline='green')
        
        # 绘制线条
        draw.line([10, 35, 100, 35], fill='purple', width=2)
        
        # 绘制文本（如果有字体的话）
        try:
            # 尝试使用默认字体
            font = ImageFont.load_default()
            draw.text((110, 15), "Canvas Test 🔒", fill='black', font=font)
        except:
            # 如果字体加载失败，使用简单文本
            draw.text((110, 15), "Canvas Test", fill='black')
        
        # 绘制一些随机点
        for _ in range(10):
            x = random.randint(0, width-1)
            y = random.randint(0, height-1)
            color = (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))
            draw.point((x, y), fill=color)
        
        return image
    
    def _apply_noise(self, image: Image.Image) -> Image.Image:
        """对图像应用噪音"""
        # 转换为numpy数组以便处理
        img_array = np.array(image)
        
        # 应用不同的噪音方法
        for method in self.methods:
            if method == "pixel_shift":
                img_array = self._apply_pixel_shift(img_array)
            elif method == "color_variance":
                img_array = self._apply_color_variance(img_array)
            elif method == "geometric_distortion":
                img_array = self._apply_geometric_distortion(img_array)
        
        # 转换回PIL图像
        return Image.fromarray(np.clip(img_array, 0, 255).astype(np.uint8))
    
    def _apply_pixel_shift(self, img_array: np.ndarray) -> np.ndarray:
        """应用像素偏移噪音"""
        height, width, channels = img_array.shape
        
        # 随机选择一些像素进行偏移
        num_pixels = int(height * width * self.noise_level)
        
        for _ in range(num_pixels):
            # 随机选择像素位置
            y = random.randint(0, height - 1)
            x = random.randint(0, width - 1)
            
            # 随机偏移方向
            dy = random.randint(-1, 1)
            dx = random.randint(-1, 1)
            
            # 计算新位置
            new_y = max(0, min(height - 1, y + dy))
            new_x = max(0, min(width - 1, x + dx))
            
            # 交换像素值
            if (new_y, new_x) != (y, x):
                img_array[y, x], img_array[new_y, new_x] = img_array[new_y, new_x].copy(), img_array[y, x].copy()
        
        return img_array
    
    def _apply_color_variance(self, img_array: np.ndarray) -> np.ndarray:
        """应用颜色变化噪音"""
        height, width, channels = img_array.shape
        
        # 随机选择一些像素进行颜色调整
        num_pixels = int(height * width * self.noise_level)
        
        for _ in range(num_pixels):
            y = random.randint(0, height - 1)
            x = random.randint(0, width - 1)
            
            # 对每个颜色通道应用小幅度的随机变化
            for c in range(channels):
                # 生成-5到+5的随机变化
                variance = random.randint(-5, 5)
                current_value = int(img_array[y, x, c])
                new_value = max(0, min(255, current_value + variance))
                img_array[y, x, c] = np.uint8(new_value)
        
        return img_array
    
    def _apply_geometric_distortion(self, img_array: np.ndarray) -> np.ndarray:
        """应用几何扭曲噪音"""
        height, width, channels = img_array.shape
        
        # 创建轻微的几何扭曲
        # 这里使用简单的行偏移来模拟扭曲
        distorted = img_array.copy()
        
        # 随机选择一些行进行轻微偏移
        num_rows = int(height * self.noise_level)
        
        for _ in range(num_rows):
            row = random.randint(0, height - 1)
            shift = random.randint(-2, 2)  # 最多偏移2个像素
            
            if shift != 0:
                if shift > 0:
                    # 向右偏移
                    distorted[row, shift:] = img_array[row, :-shift]
                    distorted[row, :shift] = img_array[row, -shift:]
                else:
                    # 向左偏移
                    shift = abs(shift)
                    distorted[row, :-shift] = img_array[row, shift:]
                    distorted[row, -shift:] = img_array[row, :shift]
        
        return distorted
    
    def _calculate_image_hash(self, image: Image.Image) -> str:
        """计算图像哈希值"""
        # 将图像转换为字节
        img_bytes = self._image_to_bytes(image)
        
        # 计算MD5哈希
        return hashlib.md5(img_bytes).hexdigest()
    
    def _image_to_bytes(self, image: Image.Image) -> bytes:
        """将图像转换为字节"""
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        return buffer.getvalue()
    
    def get_canvas_script(self, fingerprint: CanvasFingerprint) -> str:
        """
        生成Canvas指纹脚本
        
        Args:
            fingerprint: Canvas指纹信息
            
        Returns:
            JavaScript代码字符串
        """
        # 将图像数据转换为base64
        image_base64 = base64.b64encode(fingerprint.image_data).decode('utf-8')
        
        script = f"""
        // Canvas指纹伪装脚本
        (function() {{
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
            
            // 预生成的噪音数据
            const noisedImageData = 'data:image/png;base64,{image_base64}';
            
            // 重写toDataURL方法
            HTMLCanvasElement.prototype.toDataURL = function(type, quality) {{
                // 如果是指纹检测相关的Canvas，返回加噪数据
                if (this.width <= 300 && this.height <= 100) {{
                    return noisedImageData;
                }}
                return originalToDataURL.call(this, type, quality);
            }};
            
            // 重写getImageData方法
            CanvasRenderingContext2D.prototype.getImageData = function(sx, sy, sw, sh) {{
                const imageData = originalGetImageData.call(this, sx, sy, sw, sh);
                
                // 对小尺寸的Canvas应用噪音
                if (sw <= 300 && sh <= 100) {{
                    const data = imageData.data;
                    for (let i = 0; i < data.length; i += 4) {{
                        // 对RGB值应用微小的随机变化
                        if (Math.random() < {self.noise_level}) {{
                            data[i] = Math.max(0, Math.min(255, data[i] + (Math.random() - 0.5) * 10));
                            data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + (Math.random() - 0.5) * 10));
                            data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + (Math.random() - 0.5) * 10));
                        }}
                    }}
                }}
                
                return imageData;
            }};
        }})();
        """
        
        return script.strip()
    
    def test_noise_effectiveness(self, iterations: int = 100) -> Dict[str, Any]:
        """
        测试噪音效果
        
        Args:
            iterations: 测试迭代次数
            
        Returns:
            测试结果统计
        """
        unique_hashes = set()
        
        for _ in range(iterations):
            fingerprint = self.generate_canvas_fingerprint()
            unique_hashes.add(fingerprint.noised_hash)
        
        uniqueness_rate = len(unique_hashes) / iterations * 100
        
        logger.info(f"Canvas噪音效果测试 | 迭代: {iterations} | 唯一性: {uniqueness_rate:.2f}%")
        
        return {
            "iterations": iterations,
            "unique_hashes": len(unique_hashes),
            "uniqueness_rate": uniqueness_rate,
            "noise_level": self.noise_level,
            "methods": self.methods
        }
