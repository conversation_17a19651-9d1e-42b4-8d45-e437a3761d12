"""
指纹生成模块 - Audio指纹加噪器
对AudioContext的音频缓冲区进行微小的数据级修改
"""

import hashlib
import random
import math
from typing import Dict, List, Any, Optional

from .models import AudioFingerprint
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("audio_noise")


class AudioNoiseGenerator:
    """Audio指纹加噪器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Audio加噪器
        
        Args:
            config: Audio噪音配置
        """
        self.config = config
        self.enabled = config.get("enabled", True)
        self.frequency_shift = config.get("frequency_shift", True)
        self.amplitude_variance = config.get("amplitude_variance", 0.05)
        
        logger.debug(f"Audio加噪器初始化 | 启用: {self.enabled} | 振幅变化: {self.amplitude_variance}")
    
    def generate_audio_fingerprint(self) -> AudioFingerprint:
        """
        生成Audio指纹
        
        Returns:
            Audio指纹信息
        """
        # 生成基础音频上下文哈希
        context_hash = self._generate_context_hash()
        
        # 生成振荡器哈希
        oscillator_hash = self._generate_oscillator_hash()
        
        # 应用噪音参数
        frequency_shift_value = 0.0
        amplitude_variance_value = 0.0
        
        if self.enabled:
            if self.frequency_shift:
                frequency_shift_value = random.uniform(-0.1, 0.1)  # ±0.1Hz的频率偏移
            
            amplitude_variance_value = random.uniform(0, self.amplitude_variance)
        
        fingerprint = AudioFingerprint(
            context_hash=context_hash,
            oscillator_hash=oscillator_hash,
            noise_applied=self.enabled,
            frequency_shift=frequency_shift_value,
            amplitude_variance=amplitude_variance_value
        )
        
        logger.debug(f"Audio指纹生成 | 上下文哈希: {context_hash[:8]}... | 振荡器哈希: {oscillator_hash[:8]}...")
        
        return fingerprint
    
    def _generate_context_hash(self) -> str:
        """生成AudioContext哈希"""
        # 模拟AudioContext的基本属性
        sample_rate = random.choice([44100, 48000])  # 常见采样率
        max_channel_count = random.choice([2, 6, 8])  # 最大声道数
        number_of_inputs = random.choice([1, 2])
        number_of_outputs = random.choice([1, 2])
        
        # 创建上下文字符串
        context_string = f"{sample_rate}_{max_channel_count}_{number_of_inputs}_{number_of_outputs}"
        
        # 如果启用噪音，添加随机变化
        if self.enabled:
            noise_factor = random.randint(1, 1000)
            context_string += f"_{noise_factor}"
        
        return hashlib.md5(context_string.encode()).hexdigest()
    
    def _generate_oscillator_hash(self) -> str:
        """生成振荡器哈希"""
        # 模拟振荡器测试的音频数据
        # 这通常涉及创建一个短暂的音频信号并分析其特征
        
        # 基础参数
        frequency = 440.0  # A4音符
        duration = 0.1  # 100ms
        sample_rate = 44100
        
        # 应用频率偏移
        if self.enabled and self.frequency_shift:
            frequency += random.uniform(-1.0, 1.0)
        
        # 生成音频样本
        samples = []
        for i in range(int(sample_rate * duration)):
            t = i / sample_rate
            sample = math.sin(2 * math.pi * frequency * t)
            
            # 应用振幅变化
            if self.enabled:
                amplitude_noise = random.uniform(-self.amplitude_variance, self.amplitude_variance)
                sample += amplitude_noise
            
            samples.append(sample)
        
        # 计算音频特征
        # 这里简化为计算样本的统计特征
        avg_amplitude = sum(abs(s) for s in samples) / len(samples)
        max_amplitude = max(abs(s) for s in samples)
        
        # 创建特征字符串
        feature_string = f"{frequency:.6f}_{avg_amplitude:.6f}_{max_amplitude:.6f}"
        
        return hashlib.md5(feature_string.encode()).hexdigest()
    
    def get_audio_script(self, fingerprint: AudioFingerprint) -> str:
        """
        生成Audio指纹伪装脚本
        
        Args:
            fingerprint: Audio指纹信息
            
        Returns:
            JavaScript代码字符串
        """
        script = f"""
        // Audio指纹伪装脚本
        (function() {{
            const AudioContext = window.AudioContext || window.webkitAudioContext;
            if (!AudioContext) return;
            
            const originalCreateOscillator = AudioContext.prototype.createOscillator;
            const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
            const originalGetFloatFrequencyData = AnalyserNode.prototype.getFloatFrequencyData;
            const originalGetByteFrequencyData = AnalyserNode.prototype.getByteFrequencyData;
            const originalGetFloatTimeDomainData = AnalyserNode.prototype.getFloatTimeDomainData;
            const originalGetByteTimeDomainData = AnalyserNode.prototype.getByteTimeDomainData;
            
            // 噪音参数
            const frequencyShift = {fingerprint.frequency_shift};
            const amplitudeVariance = {fingerprint.amplitude_variance};
            const noiseApplied = {str(fingerprint.noise_applied).lower()};
            
            // 重写createOscillator方法
            AudioContext.prototype.createOscillator = function() {{
                const oscillator = originalCreateOscillator.call(this);
                
                if (noiseApplied) {{
                    const originalSetFrequency = oscillator.frequency.setValueAtTime;
                    oscillator.frequency.setValueAtTime = function(value, startTime) {{
                        // 应用频率偏移
                        const noisedValue = value + frequencyShift;
                        return originalSetFrequency.call(this, noisedValue, startTime);
                    }};
                }}
                
                return oscillator;
            }};
            
            // 重写AnalyserNode的数据获取方法
            function addNoiseToArray(array, variance) {{
                if (!noiseApplied || variance === 0) return;
                
                for (let i = 0; i < array.length; i++) {{
                    const noise = (Math.random() - 0.5) * variance * 2;
                    if (array instanceof Float32Array) {{
                        array[i] += noise;
                    }} else {{
                        array[i] = Math.max(0, Math.min(255, array[i] + noise * 255));
                    }}
                }}
            }}
            
            AnalyserNode.prototype.getFloatFrequencyData = function(array) {{
                originalGetFloatFrequencyData.call(this, array);
                addNoiseToArray(array, amplitudeVariance);
            }};
            
            AnalyserNode.prototype.getByteFrequencyData = function(array) {{
                originalGetByteFrequencyData.call(this, array);
                addNoiseToArray(array, amplitudeVariance);
            }};
            
            AnalyserNode.prototype.getFloatTimeDomainData = function(array) {{
                originalGetFloatTimeDomainData.call(this, array);
                addNoiseToArray(array, amplitudeVariance);
            }};
            
            AnalyserNode.prototype.getByteTimeDomainData = function(array) {{
                originalGetByteTimeDomainData.call(this, array);
                addNoiseToArray(array, amplitudeVariance);
            }};
            
            // 重写AudioContext属性
            const originalSampleRate = Object.getOwnPropertyDescriptor(AudioContext.prototype, 'sampleRate') ||
                                     Object.getOwnPropertyDescriptor(AudioContext.prototype.__proto__, 'sampleRate');
            
            if (originalSampleRate) {{
                Object.defineProperty(AudioContext.prototype, 'sampleRate', {{
                    get: function() {{
                        const originalValue = originalSampleRate.get.call(this);
                        // 对于指纹检测，可能会添加微小的变化
                        if (noiseApplied && Math.random() < 0.1) {{
                            return originalValue + (Math.random() - 0.5) * 0.1;
                        }}
                        return originalValue;
                    }},
                    configurable: true
                }});
            }}
            
            // 重写createDynamicsCompressor方法（另一个常见的指纹检测点）
            const originalCreateDynamicsCompressor = AudioContext.prototype.createDynamicsCompressor;
            AudioContext.prototype.createDynamicsCompressor = function() {{
                const compressor = originalCreateDynamicsCompressor.call(this);
                
                if (noiseApplied) {{
                    // 对压缩器参数添加微小变化
                    const originalThreshold = compressor.threshold.value;
                    const originalKnee = compressor.knee.value;
                    const originalRatio = compressor.ratio.value;
                    const originalAttack = compressor.attack.value;
                    const originalRelease = compressor.release.value;
                    
                    compressor.threshold.value = originalThreshold + (Math.random() - 0.5) * 0.1;
                    compressor.knee.value = originalKnee + (Math.random() - 0.5) * 0.1;
                    compressor.ratio.value = originalRatio + (Math.random() - 0.5) * 0.1;
                    compressor.attack.value = originalAttack + (Math.random() - 0.5) * 0.001;
                    compressor.release.value = originalRelease + (Math.random() - 0.5) * 0.001;
                }}
                
                return compressor;
            }};
        }})();
        """
        
        return script.strip()
    
    def test_noise_effectiveness(self, iterations: int = 100) -> Dict[str, Any]:
        """
        测试噪音效果
        
        Args:
            iterations: 测试迭代次数
            
        Returns:
            测试结果统计
        """
        unique_context_hashes = set()
        unique_oscillator_hashes = set()
        
        for _ in range(iterations):
            fingerprint = self.generate_audio_fingerprint()
            unique_context_hashes.add(fingerprint.context_hash)
            unique_oscillator_hashes.add(fingerprint.oscillator_hash)
        
        context_uniqueness = len(unique_context_hashes) / iterations * 100
        oscillator_uniqueness = len(unique_oscillator_hashes) / iterations * 100
        
        logger.info(
            f"Audio噪音效果测试 | 迭代: {iterations} | "
            f"上下文唯一性: {context_uniqueness:.2f}% | "
            f"振荡器唯一性: {oscillator_uniqueness:.2f}%"
        )
        
        return {
            "iterations": iterations,
            "context_unique_hashes": len(unique_context_hashes),
            "oscillator_unique_hashes": len(unique_oscillator_hashes),
            "context_uniqueness_rate": context_uniqueness,
            "oscillator_uniqueness_rate": oscillator_uniqueness,
            "frequency_shift_enabled": self.frequency_shift,
            "amplitude_variance": self.amplitude_variance
        }
