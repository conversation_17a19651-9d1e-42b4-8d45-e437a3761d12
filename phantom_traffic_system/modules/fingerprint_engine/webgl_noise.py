"""
指纹生成模块 - WebGL指纹加噪器
对WebGL渲染器信息进行伪装和修改
"""

import random
import hashlib
from typing import Dict, List, Any, Optional

from .models import WebG<PERSON>ingerprint, DevicePersona, DeviceType
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("webgl_noise")


class WebGLNoiseGenerator:
    """WebGL指纹加噪器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化WebGL加噪器
        
        Args:
            config: WebGL噪音配置
        """
        self.config = config
        self.enabled = config.get("enabled", True)
        self.shader_modification = config.get("shader_modification", True)
        self.renderer_spoofing = config.get("renderer_spoofing", True)
        
        # WebGL扩展数据库
        self._init_webgl_extensions()
        
        # WebGL参数数据库
        self._init_webgl_parameters()
        
        logger.debug(f"WebGL加噪器初始化 | 启用: {self.enabled}")
    
    def _init_webgl_extensions(self):
        """初始化WebGL扩展数据库"""
        self.common_extensions = [
            "ANGLE_instanced_arrays",
            "EXT_blend_minmax",
            "EXT_color_buffer_half_float",
            "EXT_disjoint_timer_query",
            "EXT_float_blend",
            "EXT_frag_depth",
            "EXT_shader_texture_lod",
            "EXT_texture_compression_bptc",
            "EXT_texture_compression_rgtc",
            "EXT_texture_filter_anisotropic",
            "WEBKIT_EXT_texture_filter_anisotropic",
            "EXT_sRGB",
            "OES_element_index_uint",
            "OES_fbo_render_mipmap",
            "OES_standard_derivatives",
            "OES_texture_float",
            "OES_texture_float_linear",
            "OES_texture_half_float",
            "OES_texture_half_float_linear",
            "OES_vertex_array_object",
            "WEBGL_color_buffer_float",
            "WEBGL_compressed_texture_s3tc",
            "WEBGL_compressed_texture_s3tc_srgb",
            "WEBGL_debug_renderer_info",
            "WEBGL_debug_shaders",
            "WEBGL_depth_texture",
            "WEBGL_draw_buffers",
            "WEBGL_lose_context"
        ]
        
        # 不同设备类型的扩展差异
        self.device_specific_extensions = {
            DeviceType.WINDOWS_DESKTOP: [
                "ANGLE_instanced_arrays",
                "EXT_texture_compression_bptc",
                "EXT_texture_compression_rgtc"
            ],
            DeviceType.MAC_DESKTOP: [
                "WEBKIT_EXT_texture_filter_anisotropic",
                "EXT_color_buffer_half_float"
            ],
            DeviceType.ANDROID_MOBILE: [
                "OES_compressed_ETC1_RGB8_texture",
                "WEBGL_compressed_texture_etc1"
            ],
            DeviceType.IOS_MOBILE: [
                "WEBKIT_EXT_texture_filter_anisotropic",
                "EXT_color_buffer_half_float"
            ]
        }
    
    def _init_webgl_parameters(self):
        """初始化WebGL参数数据库"""
        self.webgl_parameters = {
            "MAX_TEXTURE_SIZE": [4096, 8192, 16384],
            "MAX_CUBE_MAP_TEXTURE_SIZE": [4096, 8192, 16384],
            "MAX_RENDERBUFFER_SIZE": [4096, 8192, 16384],
            "MAX_VIEWPORT_DIMS": [(4096, 4096), (8192, 8192), (16384, 16384)],
            "MAX_VERTEX_ATTRIBS": [16, 32],
            "MAX_VERTEX_UNIFORM_VECTORS": [256, 512, 1024],
            "MAX_FRAGMENT_UNIFORM_VECTORS": [256, 512, 1024],
            "MAX_VARYING_VECTORS": [8, 16, 32],
            "MAX_TEXTURE_IMAGE_UNITS": [16, 32],
            "MAX_VERTEX_TEXTURE_IMAGE_UNITS": [16, 32],
            "MAX_COMBINED_TEXTURE_IMAGE_UNITS": [32, 64, 80],
            "SHADING_LANGUAGE_VERSION": [
                "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)",
                "WebGL GLSL ES 1.0 (1.0)",
                "WebGL GLSL ES 1.0"
            ]
        }
    
    def generate_webgl_fingerprint(self, device_persona: DevicePersona) -> WebGLFingerprint:
        """
        生成WebGL指纹
        
        Args:
            device_persona: 设备人格
            
        Returns:
            WebGL指纹信息
        """
        # 基于设备人格生成WebGL信息
        vendor, renderer = self._generate_vendor_renderer(device_persona)
        version = self._generate_webgl_version()
        shading_language_version = self._generate_shading_language_version()
        extensions = self._generate_extensions(device_persona.device_type)
        parameters = self._generate_parameters(device_persona)
        
        fingerprint = WebGLFingerprint(
            vendor=vendor,
            renderer=renderer,
            version=version,
            shading_language_version=shading_language_version,
            extensions=extensions,
            parameters=parameters,
            noise_applied=self.enabled
        )
        
        logger.debug(f"WebGL指纹生成 | 渲染器: {renderer[:30]}... | 扩展数量: {len(extensions)}")
        
        return fingerprint
    
    def _generate_vendor_renderer(self, device_persona: DevicePersona) -> tuple[str, str]:
        """生成供应商和渲染器信息"""
        gpu_vendor = device_persona.hardware_info.gpu_vendor
        gpu_renderer = device_persona.hardware_info.gpu_renderer
        
        # 根据GPU供应商确定WebGL供应商
        if "NVIDIA" in gpu_vendor:
            vendor = "NVIDIA Corporation"
        elif "AMD" in gpu_vendor:
            vendor = "ATI Technologies Inc."
        elif "Intel" in gpu_vendor:
            vendor = "Intel Inc."
        elif "Apple" in gpu_vendor:
            vendor = "Apple Inc."
        elif "Qualcomm" in gpu_vendor:
            vendor = "Qualcomm"
        else:
            vendor = gpu_vendor
        
        # 生成渲染器字符串
        if self.renderer_spoofing and self.enabled:
            # 应用轻微的渲染器信息修改
            renderer = self._apply_renderer_noise(gpu_renderer, device_persona.device_type)
        else:
            renderer = gpu_renderer
        
        return vendor, renderer
    
    def _apply_renderer_noise(self, original_renderer: str, device_type: DeviceType) -> str:
        """对渲染器信息应用噪音"""
        # 对于桌面设备，可能会有ANGLE前缀
        if device_type in [DeviceType.WINDOWS_DESKTOP, DeviceType.MAC_DESKTOP]:
            if random.choice([True, False]):
                return f"ANGLE ({original_renderer} Direct3D11 vs_5_0 ps_5_0)"
            else:
                return f"ANGLE ({original_renderer})"
        
        # 移动设备通常直接显示GPU名称
        return original_renderer
    
    def _generate_webgl_version(self) -> str:
        """生成WebGL版本"""
        versions = [
            "WebGL 1.0 (OpenGL ES 2.0 Chromium)",
            "WebGL 1.0",
            "WebGL 2.0 (OpenGL ES 3.0 Chromium)",
            "WebGL 2.0"
        ]
        
        # 大多数情况下使用WebGL 1.0
        if random.random() < 0.8:
            return random.choice(versions[:2])
        else:
            return random.choice(versions[2:])
    
    def _generate_shading_language_version(self) -> str:
        """生成着色语言版本"""
        return random.choice(self.webgl_parameters["SHADING_LANGUAGE_VERSION"])
    
    def _generate_extensions(self, device_type: DeviceType) -> List[str]:
        """生成WebGL扩展列表"""
        # 基础扩展
        extensions = random.sample(self.common_extensions, random.randint(15, 25))
        
        # 添加设备特定扩展
        if device_type in self.device_specific_extensions:
            device_extensions = self.device_specific_extensions[device_type]
            extensions.extend(random.sample(device_extensions, random.randint(1, len(device_extensions))))
        
        # 去重并排序
        extensions = sorted(list(set(extensions)))
        
        return extensions
    
    def _generate_parameters(self, device_persona: DevicePersona) -> Dict[str, Any]:
        """生成WebGL参数"""
        parameters = {}
        
        # 根据GPU性能级别调整参数
        gpu_renderer = device_persona.hardware_info.gpu_renderer.lower()
        
        # 判断GPU性能级别
        if any(high_end in gpu_renderer for high_end in ["rtx", "4090", "4080", "3080", "m1", "m2"]):
            performance_level = "high"
        elif any(mid_range in gpu_renderer for mid_range in ["gtx", "1660", "rx", "5700"]):
            performance_level = "medium"
        else:
            performance_level = "low"
        
        # 根据性能级别设置参数
        if performance_level == "high":
            parameters["MAX_TEXTURE_SIZE"] = 16384
            parameters["MAX_CUBE_MAP_TEXTURE_SIZE"] = 16384
            parameters["MAX_RENDERBUFFER_SIZE"] = 16384
            parameters["MAX_VIEWPORT_DIMS"] = [16384, 16384]
            parameters["MAX_VERTEX_UNIFORM_VECTORS"] = 1024
            parameters["MAX_FRAGMENT_UNIFORM_VECTORS"] = 1024
        elif performance_level == "medium":
            parameters["MAX_TEXTURE_SIZE"] = 8192
            parameters["MAX_CUBE_MAP_TEXTURE_SIZE"] = 8192
            parameters["MAX_RENDERBUFFER_SIZE"] = 8192
            parameters["MAX_VIEWPORT_DIMS"] = [8192, 8192]
            parameters["MAX_VERTEX_UNIFORM_VECTORS"] = 512
            parameters["MAX_FRAGMENT_UNIFORM_VECTORS"] = 512
        else:
            parameters["MAX_TEXTURE_SIZE"] = 4096
            parameters["MAX_CUBE_MAP_TEXTURE_SIZE"] = 4096
            parameters["MAX_RENDERBUFFER_SIZE"] = 4096
            parameters["MAX_VIEWPORT_DIMS"] = [4096, 4096]
            parameters["MAX_VERTEX_UNIFORM_VECTORS"] = 256
            parameters["MAX_FRAGMENT_UNIFORM_VECTORS"] = 256
        
        # 其他固定参数
        parameters["MAX_VERTEX_ATTRIBS"] = 16
        parameters["MAX_VARYING_VECTORS"] = random.choice([8, 16])
        parameters["MAX_TEXTURE_IMAGE_UNITS"] = random.choice([16, 32])
        parameters["MAX_VERTEX_TEXTURE_IMAGE_UNITS"] = random.choice([16, 32])
        parameters["MAX_COMBINED_TEXTURE_IMAGE_UNITS"] = random.choice([32, 64, 80])
        
        return parameters
    
    def get_webgl_script(self, fingerprint: WebGLFingerprint) -> str:
        """
        生成WebGL指纹伪装脚本
        
        Args:
            fingerprint: WebGL指纹信息
            
        Returns:
            JavaScript代码字符串
        """
        extensions_js = str(fingerprint.extensions).replace("'", '"')
        parameters_js = str(fingerprint.parameters).replace("'", '"')
        
        script = f"""
        // WebGL指纹伪装脚本
        (function() {{
            const getParameter = WebGLRenderingContext.prototype.getParameter;
            const getSupportedExtensions = WebGLRenderingContext.prototype.getSupportedExtensions;
            const getExtension = WebGLRenderingContext.prototype.getExtension;
            
            // 预定义的WebGL信息
            const spoofedVendor = "{fingerprint.vendor}";
            const spoofedRenderer = "{fingerprint.renderer}";
            const spoofedExtensions = {extensions_js};
            const spoofedParameters = {parameters_js};
            
            // 重写getParameter方法
            WebGLRenderingContext.prototype.getParameter = function(parameter) {{
                switch(parameter) {{
                    case this.VENDOR:
                        return spoofedVendor;
                    case this.RENDERER:
                        return spoofedRenderer;
                    case this.VERSION:
                        return "{fingerprint.version}";
                    case this.SHADING_LANGUAGE_VERSION:
                        return "{fingerprint.shading_language_version}";
                    case this.MAX_TEXTURE_SIZE:
                        return spoofedParameters.MAX_TEXTURE_SIZE || 4096;
                    case this.MAX_CUBE_MAP_TEXTURE_SIZE:
                        return spoofedParameters.MAX_CUBE_MAP_TEXTURE_SIZE || 4096;
                    case this.MAX_RENDERBUFFER_SIZE:
                        return spoofedParameters.MAX_RENDERBUFFER_SIZE || 4096;
                    case this.MAX_VIEWPORT_DIMS:
                        return new Int32Array(spoofedParameters.MAX_VIEWPORT_DIMS || [4096, 4096]);
                    case this.MAX_VERTEX_ATTRIBS:
                        return spoofedParameters.MAX_VERTEX_ATTRIBS || 16;
                    case this.MAX_VERTEX_UNIFORM_VECTORS:
                        return spoofedParameters.MAX_VERTEX_UNIFORM_VECTORS || 256;
                    case this.MAX_FRAGMENT_UNIFORM_VECTORS:
                        return spoofedParameters.MAX_FRAGMENT_UNIFORM_VECTORS || 256;
                    case this.MAX_VARYING_VECTORS:
                        return spoofedParameters.MAX_VARYING_VECTORS || 8;
                    case this.MAX_TEXTURE_IMAGE_UNITS:
                        return spoofedParameters.MAX_TEXTURE_IMAGE_UNITS || 16;
                    case this.MAX_VERTEX_TEXTURE_IMAGE_UNITS:
                        return spoofedParameters.MAX_VERTEX_TEXTURE_IMAGE_UNITS || 16;
                    case this.MAX_COMBINED_TEXTURE_IMAGE_UNITS:
                        return spoofedParameters.MAX_COMBINED_TEXTURE_IMAGE_UNITS || 32;
                    default:
                        return getParameter.call(this, parameter);
                }}
            }};
            
            // 重写getSupportedExtensions方法
            WebGLRenderingContext.prototype.getSupportedExtensions = function() {{
                return spoofedExtensions.slice(); // 返回副本
            }};
            
            // 重写getExtension方法
            WebGLRenderingContext.prototype.getExtension = function(name) {{
                if (spoofedExtensions.includes(name)) {{
                    return getExtension.call(this, name) || {{}};
                }}
                return null;
            }};
            
            // 同样处理WebGL2
            if (typeof WebGL2RenderingContext !== 'undefined') {{
                WebGL2RenderingContext.prototype.getParameter = WebGLRenderingContext.prototype.getParameter;
                WebGL2RenderingContext.prototype.getSupportedExtensions = WebGLRenderingContext.prototype.getSupportedExtensions;
                WebGL2RenderingContext.prototype.getExtension = WebGLRenderingContext.prototype.getExtension;
            }}
        }})();
        """
        
        return script.strip()
