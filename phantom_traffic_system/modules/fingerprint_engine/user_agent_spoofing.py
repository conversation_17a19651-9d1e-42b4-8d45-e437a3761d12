"""
指纹生成模块 - User-Agent伪装器
负责生成和管理User-Agent字符串的伪装
"""

import random
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

from .models import DevicePersona, DeviceType, BrowserType
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("user_agent_spoofing")


class UserAgentSpoofing:
    """User-Agent伪装器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化User-Agent伪装器
        
        Args:
            config: 伪装配置
        """
        self.config = config
        self.realistic_combinations = config.get("realistic_combinations", True)
        self.os_specific_fonts = config.get("os_specific_fonts", True)
        
        # 初始化User-Agent模板
        self._init_ua_templates()
        
        # 初始化版本号数据库
        self._init_version_database()
        
        logger.debug("User-Agent伪装器初始化完成")
    
    def _init_ua_templates(self):
        """初始化User-Agent模板"""
        self.ua_templates = {
            DeviceType.WINDOWS_DESKTOP: {
                BrowserType.CHROME: [
                    "Mozilla/5.0 (Windows NT {os_version}; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{browser_version} Safari/537.36",
                    "Mozilla/5.0 (Windows NT {os_version}; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{browser_version} Safari/537.36"
                ],
                BrowserType.FIREFOX: [
                    "Mozilla/5.0 (Windows NT {os_version}; Win64; x64; rv:{firefox_version}) Gecko/20100101 Firefox/{browser_version}",
                    "Mozilla/5.0 (Windows NT {os_version}; WOW64; rv:{firefox_version}) Gecko/20100101 Firefox/{browser_version}"
                ],
                BrowserType.EDGE: [
                    "Mozilla/5.0 (Windows NT {os_version}; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36 Edg/{browser_version}",
                    "Mozilla/5.0 (Windows NT {os_version}; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36 Edge/{browser_version}"
                ]
            },
            DeviceType.MAC_DESKTOP: {
                BrowserType.SAFARI: [
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X {mac_version}) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/{browser_version} Safari/605.1.15",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X {mac_version}) AppleWebKit/537.36 (KHTML, like Gecko) Version/{browser_version} Safari/537.36"
                ],
                BrowserType.CHROME: [
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X {mac_version}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{browser_version} Safari/537.36"
                ],
                BrowserType.FIREFOX: [
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X {mac_version}) Gecko/20100101 Firefox/{browser_version}"
                ]
            },
            DeviceType.ANDROID_MOBILE: {
                BrowserType.CHROME: [
                    "Mozilla/5.0 (Linux; Android {android_version}; {device_model}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{browser_version} Mobile Safari/537.36",
                    "Mozilla/5.0 (Linux; Android {android_version}; {device_model} wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/{browser_version} Mobile Safari/537.36"
                ]
            },
            DeviceType.IOS_MOBILE: {
                BrowserType.SAFARI: [
                    "Mozilla/5.0 (iPhone; CPU iPhone OS {ios_version} like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/{safari_version} Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS {ios_version} like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/{chrome_version} Mobile/15E148 Safari/604.1"
                ]
            }
        }
    
    def _init_version_database(self):
        """初始化版本号数据库"""
        # 获取当前日期用于生成最新版本
        current_date = datetime.now()
        
        self.version_database = {
            "chrome_versions": [
                "120.0.6099.109", "120.0.6099.71", "120.0.6099.62",
                "119.0.6045.199", "119.0.6045.159", "119.0.6045.123",
                "118.0.5993.117", "118.0.5993.88", "118.0.5993.70"
            ],
            "firefox_versions": [
                "121.0", "120.0.1", "120.0", "119.0.1", "119.0",
                "118.0.2", "118.0.1", "118.0", "117.0.1", "117.0"
            ],
            "safari_versions": [
                "17.1.2", "17.1.1", "17.1", "17.0.1", "17.0",
                "16.6.1", "16.6", "16.5.2", "16.5.1", "16.5"
            ],
            "edge_versions": [
                "120.0.2210.61", "120.0.2210.55", "120.0.2210.47",
                "119.0.2151.97", "119.0.2151.93", "119.0.2151.72"
            ],
            "windows_versions": [
                "10.0", "11.0", "6.3", "6.1"  # Win10, Win11, Win8.1, Win7
            ],
            "mac_versions": [
                "10_15_7", "11_7_10", "12_7_1", "13_6_3", "14_1_2"
            ],
            "android_versions": [
                "14", "13", "12", "11", "10", "9"
            ],
            "ios_versions": [
                "17_1_1", "17_1", "17_0_3", "16_7_2", "16_7_1", "15_8"
            ],
            "android_devices": [
                "SM-G991B", "SM-G996B", "SM-G998B",  # Samsung Galaxy S21 series
                "SM-A525F", "SM-A715F", "SM-A515F",  # Samsung Galaxy A series
                "Pixel 8", "Pixel 7", "Pixel 6",     # Google Pixel
                "OnePlus 11", "OnePlus 10T", "OnePlus 9",  # OnePlus
                "Mi 13", "Mi 12", "Redmi Note 12",    # Xiaomi
                "HUAWEI P50", "HUAWEI Mate 40"        # Huawei
            ]
        }
    
    def generate_user_agent(self, device_persona: DevicePersona) -> str:
        """
        为设备人格生成User-Agent
        
        Args:
            device_persona: 设备人格
            
        Returns:
            User-Agent字符串
        """
        device_type = device_persona.device_type
        browser_type = device_persona.browser_type
        
        # 获取对应的模板
        if device_type not in self.ua_templates:
            logger.warning(f"不支持的设备类型: {device_type}")
            return self._generate_fallback_ua()
        
        if browser_type not in self.ua_templates[device_type]:
            logger.warning(f"设备类型 {device_type} 不支持浏览器 {browser_type}")
            # 使用该设备类型的第一个可用浏览器
            browser_type = list(self.ua_templates[device_type].keys())[0]
        
        templates = self.ua_templates[device_type][browser_type]
        template = random.choice(templates)
        
        # 生成版本参数
        version_params = self._generate_version_params(device_type, browser_type, device_persona)
        
        # 填充模板
        try:
            user_agent = template.format(**version_params)
            logger.debug(f"生成User-Agent | 设备: {device_type.value} | 浏览器: {browser_type.value}")
            return user_agent
        except KeyError as e:
            logger.error(f"User-Agent模板参数缺失: {e}")
            return self._generate_fallback_ua()
    
    def _generate_version_params(self, device_type: DeviceType, browser_type: BrowserType, 
                                device_persona: DevicePersona) -> Dict[str, str]:
        """生成版本参数"""
        params = {}
        
        # 操作系统版本
        if device_type == DeviceType.WINDOWS_DESKTOP:
            params["os_version"] = device_persona.os_version or random.choice(self.version_database["windows_versions"])
        elif device_type == DeviceType.MAC_DESKTOP:
            mac_version = device_persona.os_version.replace(".", "_") if device_persona.os_version else random.choice(self.version_database["mac_versions"])
            params["mac_version"] = mac_version
        elif device_type == DeviceType.ANDROID_MOBILE:
            params["android_version"] = device_persona.os_version or random.choice(self.version_database["android_versions"])
            params["device_model"] = random.choice(self.version_database["android_devices"])
        elif device_type == DeviceType.IOS_MOBILE:
            ios_version = device_persona.os_version.replace(".", "_") if device_persona.os_version else random.choice(self.version_database["ios_versions"])
            params["ios_version"] = ios_version
        
        # 浏览器版本
        if browser_type == BrowserType.CHROME:
            params["browser_version"] = device_persona.browser_version or random.choice(self.version_database["chrome_versions"])
            params["chrome_version"] = params["browser_version"]
        elif browser_type == BrowserType.FIREFOX:
            firefox_version = device_persona.browser_version or random.choice(self.version_database["firefox_versions"])
            params["browser_version"] = firefox_version
            params["firefox_version"] = firefox_version.split(".")[0]  # 主版本号
        elif browser_type == BrowserType.SAFARI:
            params["browser_version"] = device_persona.browser_version or random.choice(self.version_database["safari_versions"])
            params["safari_version"] = params["browser_version"]
        elif browser_type == BrowserType.EDGE:
            params["browser_version"] = device_persona.browser_version or random.choice(self.version_database["edge_versions"])
            # Edge通常基于Chromium，所以需要Chrome版本
            params["chrome_version"] = random.choice(self.version_database["chrome_versions"])
        
        return params
    
    def _generate_fallback_ua(self) -> str:
        """生成备用User-Agent"""
        chrome_version = random.choice(self.version_database["chrome_versions"])
        windows_version = random.choice(self.version_database["windows_versions"])
        
        return f"Mozilla/5.0 (Windows NT {windows_version}; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36"
    
    def validate_user_agent(self, user_agent: str) -> Dict[str, Any]:
        """
        验证User-Agent的有效性
        
        Args:
            user_agent: User-Agent字符串
            
        Returns:
            验证结果
        """
        validation_result = {
            "is_valid": True,
            "issues": [],
            "detected_browser": None,
            "detected_os": None,
            "detected_device": None
        }
        
        # 基本格式检查
        if not user_agent or len(user_agent) < 50:
            validation_result["is_valid"] = False
            validation_result["issues"].append("User-Agent过短")
            return validation_result
        
        # 检测浏览器
        if "Chrome/" in user_agent:
            validation_result["detected_browser"] = "Chrome"
        elif "Firefox/" in user_agent:
            validation_result["detected_browser"] = "Firefox"
        elif "Safari/" in user_agent and "Chrome" not in user_agent:
            validation_result["detected_browser"] = "Safari"
        elif "Edg/" in user_agent or "Edge/" in user_agent:
            validation_result["detected_browser"] = "Edge"
        
        # 检测操作系统
        if "Windows NT" in user_agent:
            validation_result["detected_os"] = "Windows"
        elif "Mac OS X" in user_agent or "Macintosh" in user_agent:
            validation_result["detected_os"] = "macOS"
        elif "Android" in user_agent:
            validation_result["detected_os"] = "Android"
        elif "iPhone OS" in user_agent:
            validation_result["detected_os"] = "iOS"
        
        # 检测设备类型
        if "Mobile" in user_agent:
            validation_result["detected_device"] = "Mobile"
        elif "Tablet" in user_agent or "iPad" in user_agent:
            validation_result["detected_device"] = "Tablet"
        else:
            validation_result["detected_device"] = "Desktop"
        
        # 一致性检查
        if validation_result["detected_browser"] == "Safari" and validation_result["detected_os"] not in ["macOS", "iOS"]:
            validation_result["issues"].append("Safari浏览器与操作系统不匹配")
        
        if validation_result["detected_os"] == "iOS" and "Safari" not in user_agent:
            validation_result["issues"].append("iOS设备应该使用Safari内核")
        
        # 版本号合理性检查
        version_patterns = {
            "Chrome": r"Chrome/(\d+\.\d+\.\d+\.\d+)",
            "Firefox": r"Firefox/(\d+\.\d+)",
            "Safari": r"Version/(\d+\.\d+\.\d+)",
            "Edge": r"Edg?/(\d+\.\d+\.\d+\.\d+)"
        }
        
        browser = validation_result["detected_browser"]
        if browser and browser in version_patterns:
            pattern = version_patterns[browser]
            match = re.search(pattern, user_agent)
            if match:
                version = match.group(1)
                if not self._is_reasonable_version(browser, version):
                    validation_result["issues"].append(f"{browser}版本号不合理: {version}")
        
        if validation_result["issues"]:
            validation_result["is_valid"] = False
        
        return validation_result
    
    def _is_reasonable_version(self, browser: str, version: str) -> bool:
        """检查版本号是否合理"""
        try:
            major_version = int(version.split(".")[0])
            
            # 定义合理的版本范围
            reasonable_ranges = {
                "Chrome": (100, 130),
                "Firefox": (100, 130),
                "Safari": (15, 18),
                "Edge": (100, 130)
            }
            
            if browser in reasonable_ranges:
                min_ver, max_ver = reasonable_ranges[browser]
                return min_ver <= major_version <= max_ver
            
            return True
        except (ValueError, IndexError):
            return False
    
    def get_spoofing_script(self, user_agent: str) -> str:
        """
        生成User-Agent伪装脚本
        
        Args:
            user_agent: 目标User-Agent
            
        Returns:
            JavaScript代码字符串
        """
        script = f"""
        // User-Agent伪装脚本
        (function() {{
            const spoofedUserAgent = "{user_agent}";
            
            // 重写navigator.userAgent
            Object.defineProperty(navigator, 'userAgent', {{
                get: function() {{
                    return spoofedUserAgent;
                }},
                configurable: true
            }});
            
            // 重写navigator.appVersion
            Object.defineProperty(navigator, 'appVersion', {{
                get: function() {{
                    return spoofedUserAgent.substring(spoofedUserAgent.indexOf('/') + 1);
                }},
                configurable: true
            }});
            
            // 重写navigator.appName
            Object.defineProperty(navigator, 'appName', {{
                get: function() {{
                    if (spoofedUserAgent.includes('Firefox')) return 'Netscape';
                    return 'Netscape';
                }},
                configurable: true
            }});
            
            // 重写navigator.product
            Object.defineProperty(navigator, 'product', {{
                get: function() {{
                    return 'Gecko';
                }},
                configurable: true
            }});
            
            // 重写navigator.vendor
            Object.defineProperty(navigator, 'vendor', {{
                get: function() {{
                    if (spoofedUserAgent.includes('Chrome')) return 'Google Inc.';
                    if (spoofedUserAgent.includes('Safari')) return 'Apple Computer, Inc.';
                    if (spoofedUserAgent.includes('Firefox')) return '';
                    if (spoofedUserAgent.includes('Edge')) return 'Microsoft Corporation';
                    return '';
                }},
                configurable: true
            }});
        }})();
        """
        
        return script.strip()
