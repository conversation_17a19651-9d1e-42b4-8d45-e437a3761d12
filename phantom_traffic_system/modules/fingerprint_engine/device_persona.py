"""
指纹生成模块 - 设备人格生成器
负责生成真实可信的设备配置组合
"""

import json
import random
from pathlib import Path
from typing import Dict, List, Any, Optional

from .models import DevicePersona, DeviceType, BrowserType, ScreenInfo, HardwareInfo
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("device_persona")


class DevicePersonaGenerator:
    """设备人格生成器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化设备人格生成器
        
        Args:
            config: 指纹配置
        """
        self.config = config
        self.database_path = config.get("device_personas", {}).get("database_path", "data/device_personas.json")
        self.auto_update = config.get("device_personas", {}).get("auto_update", True)
        self.categories = config.get("device_personas", {}).get("categories", [
            "windows_desktop", "mac_desktop", "android_mobile", "ios_mobile"
        ])
        
        # 加载设备人格数据库
        self.persona_database = self._load_persona_database()
        
        # 预定义的真实设备配置
        self._init_builtin_personas()
    
    def _load_persona_database(self) -> Dict[str, List[Dict[str, Any]]]:
        """加载设备人格数据库"""
        database_file = Path(self.database_path)
        
        if database_file.exists():
            try:
                with open(database_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"加载设备人格数据库 | 文件: {self.database_path}")
                return data
            except Exception as e:
                logger.error(f"加载设备人格数据库失败: {e}")
        
        # 如果文件不存在或加载失败，返回空数据库
        return {}
    
    def _init_builtin_personas(self):
        """初始化内置设备人格"""
        builtin_personas = {
            "windows_desktop": [
                {
                    "os_name": "Windows",
                    "os_version": "10.0",
                    "browser_type": "chrome",
                    "browser_version": "120.0.6099.109",
                    "screen_resolutions": [(1920, 1080), (1366, 768), (1440, 900), (1600, 900)],
                    "cpu_cores": [4, 6, 8, 12, 16],
                    "memory_options": [8, 16, 32],
                    "gpu_vendors": ["NVIDIA Corporation", "AMD", "Intel Inc."],
                    "common_fonts": [
                        "Arial", "Times New Roman", "Courier New", "Verdana", "Georgia",
                        "Palatino Linotype", "Trebuchet MS", "Arial Black", "Impact",
                        "Tahoma", "Comic Sans MS", "Lucida Console", "Lucida Sans Unicode"
                    ]
                },
                {
                    "os_name": "Windows",
                    "os_version": "11.0",
                    "browser_type": "chrome",
                    "browser_version": "120.0.6099.109",
                    "screen_resolutions": [(1920, 1080), (2560, 1440), (3840, 2160)],
                    "cpu_cores": [6, 8, 12, 16],
                    "memory_options": [16, 32, 64],
                    "gpu_vendors": ["NVIDIA Corporation", "AMD", "Intel Inc."],
                    "common_fonts": [
                        "Arial", "Times New Roman", "Courier New", "Verdana", "Georgia",
                        "Palatino Linotype", "Trebuchet MS", "Arial Black", "Impact",
                        "Tahoma", "Comic Sans MS", "Lucida Console", "Segoe UI"
                    ]
                }
            ],
            "mac_desktop": [
                {
                    "os_name": "Mac OS X",
                    "os_version": "10.15.7",
                    "browser_type": "safari",
                    "browser_version": "17.1.2",
                    "screen_resolutions": [(1440, 900), (1680, 1050), (1920, 1080), (2560, 1600)],
                    "cpu_cores": [4, 6, 8, 10],
                    "memory_options": [8, 16, 32, 64],
                    "gpu_vendors": ["Apple", "AMD", "Intel Inc."],
                    "common_fonts": [
                        "Times", "Helvetica", "Courier", "Arial", "Verdana",
                        "Georgia", "Palatino", "Times New Roman", "Arial Black",
                        "Comic Sans MS", "Impact", "Trebuchet MS", "Webdings"
                    ]
                }
            ],
            "android_mobile": [
                {
                    "os_name": "Android",
                    "os_version": "13",
                    "browser_type": "chrome",
                    "browser_version": "120.0.6099.43",
                    "screen_resolutions": [(360, 640), (375, 667), (414, 896), (393, 851)],
                    "cpu_cores": [4, 6, 8],
                    "memory_options": [4, 6, 8, 12],
                    "gpu_vendors": ["Qualcomm", "ARM", "PowerVR"],
                    "common_fonts": [
                        "Roboto", "Droid Sans", "Droid Serif", "Droid Sans Mono"
                    ]
                }
            ],
            "ios_mobile": [
                {
                    "os_name": "iPhone OS",
                    "os_version": "17.1.1",
                    "browser_type": "safari",
                    "browser_version": "17.1",
                    "screen_resolutions": [(375, 667), (414, 896), (390, 844), (428, 926)],
                    "cpu_cores": [6, 8],
                    "memory_options": [4, 6, 8],
                    "gpu_vendors": ["Apple"],
                    "common_fonts": [
                        "Times", "Helvetica", "Courier", "Arial", "Verdana",
                        "Georgia", "Palatino", "Times New Roman", "Arial Black"
                    ]
                }
            ]
        }
        
        # 合并内置人格到数据库
        for category, personas in builtin_personas.items():
            if category not in self.persona_database:
                self.persona_database[category] = []
            self.persona_database[category].extend(personas)
    
    def generate_persona(self, device_type: Optional[DeviceType] = None, 
                        browser_type: Optional[BrowserType] = None) -> DevicePersona:
        """
        生成设备人格
        
        Args:
            device_type: 指定设备类型
            browser_type: 指定浏览器类型
            
        Returns:
            设备人格
        """
        # 随机选择设备类型
        if device_type is None:
            available_types = [DeviceType(cat) for cat in self.categories if cat in self.persona_database]
            device_type = random.choice(available_types)
        
        # 获取对应的人格模板
        category_key = device_type.value
        if category_key not in self.persona_database or not self.persona_database[category_key]:
            raise ValueError(f"没有找到设备类型 {device_type.value} 的人格模板")
        
        template = random.choice(self.persona_database[category_key])
        
        # 生成具体的设备配置
        persona = self._generate_from_template(device_type, template, browser_type)
        
        logger.debug(f"生成设备人格 | 类型: {device_type.value} | 浏览器: {persona.browser_type.value}")
        
        return persona
    
    def _generate_from_template(self, device_type: DeviceType, 
                               template: Dict[str, Any], 
                               browser_type: Optional[BrowserType] = None) -> DevicePersona:
        """从模板生成具体的设备人格"""
        
        # 确定浏览器类型
        if browser_type is None:
            browser_type = BrowserType(template.get("browser_type", "chrome"))
        
        # 生成屏幕信息
        screen_resolution = random.choice(template["screen_resolutions"])
        screen_info = ScreenInfo(
            width=screen_resolution[0],
            height=screen_resolution[1],
            color_depth=random.choice([24, 32]),
            pixel_ratio=self._get_pixel_ratio(device_type, screen_resolution)
        )
        
        # 生成硬件信息
        hardware_info = HardwareInfo(
            cpu_cores=random.choice(template["cpu_cores"]),
            memory_gb=random.choice(template["memory_options"]),
            gpu_vendor=random.choice(template["gpu_vendors"]),
            gpu_renderer=self._generate_gpu_renderer(template["gpu_vendors"][0]),
            platform=self._get_platform(device_type),
            max_touch_points=self._get_max_touch_points(device_type)
        )
        
        # 生成User-Agent
        user_agent = self._generate_user_agent(
            device_type, browser_type, template["os_name"], 
            template["os_version"], template["browser_version"]
        )
        
        # 生成字体列表
        fonts = self._generate_font_list(template["common_fonts"], device_type)
        
        # 生成插件列表
        plugins = self._generate_plugin_list(browser_type, device_type)
        
        # 生成语言设置
        languages = self._generate_languages()
        
        return DevicePersona(
            device_type=device_type,
            browser_type=browser_type,
            user_agent=user_agent,
            screen_info=screen_info,
            hardware_info=hardware_info,
            os_name=template["os_name"],
            os_version=template["os_version"],
            browser_version=template["browser_version"],
            browser_build=self._generate_browser_build(browser_type),
            languages=languages,
            timezone=self._generate_timezone(),
            fonts=fonts,
            plugins=plugins,
            webrtc_enabled=random.choice([True, False]) if device_type in [DeviceType.WINDOWS_DESKTOP, DeviceType.MAC_DESKTOP] else True,
            do_not_track=random.choice([True, False]),
            java_enabled=random.choice([True, False]) if device_type in [DeviceType.WINDOWS_DESKTOP, DeviceType.MAC_DESKTOP] else False
        )
    
    def _get_pixel_ratio(self, device_type: DeviceType, resolution: tuple) -> float:
        """获取像素比例"""
        if device_type in [DeviceType.IOS_MOBILE, DeviceType.ANDROID_MOBILE]:
            # 移动设备通常有更高的像素密度
            if resolution[0] >= 400:
                return random.choice([2.0, 3.0])
            else:
                return random.choice([1.5, 2.0])
        else:
            # 桌面设备
            if resolution[0] >= 2560:
                return random.choice([1.5, 2.0])
            else:
                return 1.0
    
    def _generate_gpu_renderer(self, gpu_vendor: str) -> str:
        """生成GPU渲染器名称"""
        renderers = {
            "NVIDIA Corporation": [
                "NVIDIA GeForce RTX 4090",
                "NVIDIA GeForce RTX 4080", 
                "NVIDIA GeForce RTX 3080",
                "NVIDIA GeForce GTX 1660 Ti",
                "NVIDIA GeForce GTX 1050 Ti"
            ],
            "AMD": [
                "AMD Radeon RX 7900 XTX",
                "AMD Radeon RX 6800 XT",
                "AMD Radeon RX 5700 XT",
                "AMD Radeon RX 580"
            ],
            "Intel Inc.": [
                "Intel(R) UHD Graphics 630",
                "Intel(R) Iris(R) Xe Graphics",
                "Intel(R) HD Graphics 620"
            ],
            "Apple": [
                "Apple M2",
                "Apple M1",
                "Apple M1 Pro",
                "Apple M1 Max"
            ],
            "Qualcomm": [
                "Adreno (TM) 740",
                "Adreno (TM) 730",
                "Adreno (TM) 650"
            ]
        }
        
        if gpu_vendor in renderers:
            return random.choice(renderers[gpu_vendor])
        else:
            return f"{gpu_vendor} Graphics"

    def _get_platform(self, device_type: DeviceType) -> str:
        """获取平台标识"""
        platform_map = {
            DeviceType.WINDOWS_DESKTOP: "Win32",
            DeviceType.MAC_DESKTOP: "MacIntel",
            DeviceType.LINUX_DESKTOP: "Linux x86_64",
            DeviceType.ANDROID_MOBILE: "Linux armv8l",
            DeviceType.IOS_MOBILE: "iPhone",
            DeviceType.ANDROID_TABLET: "Linux armv8l",
            DeviceType.IPAD: "iPad"
        }
        return platform_map.get(device_type, "Unknown")

    def _get_max_touch_points(self, device_type: DeviceType) -> int:
        """获取最大触摸点数"""
        if device_type in [DeviceType.ANDROID_MOBILE, DeviceType.IOS_MOBILE,
                          DeviceType.ANDROID_TABLET, DeviceType.IPAD]:
            return random.choice([5, 10])
        else:
            return 0

    def _generate_user_agent(self, device_type: DeviceType, browser_type: BrowserType,
                           os_name: str, os_version: str, browser_version: str) -> str:
        """生成User-Agent字符串"""

        if device_type == DeviceType.WINDOWS_DESKTOP:
            if browser_type == BrowserType.CHROME:
                return f"Mozilla/5.0 (Windows NT {os_version}; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{browser_version} Safari/537.36"
            elif browser_type == BrowserType.FIREFOX:
                return f"Mozilla/5.0 (Windows NT {os_version}; Win64; x64; rv:{browser_version.split('.')[0]}.0) Gecko/20100101 Firefox/{browser_version}"
            elif browser_type == BrowserType.EDGE:
                return f"Mozilla/5.0 (Windows NT {os_version}; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{browser_version} Safari/537.36 Edg/{browser_version}"

        elif device_type == DeviceType.MAC_DESKTOP:
            if browser_type == BrowserType.SAFARI:
                return f"Mozilla/5.0 (Macintosh; Intel Mac OS X {os_version.replace('.', '_')}) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/{browser_version} Safari/605.1.15"
            elif browser_type == BrowserType.CHROME:
                return f"Mozilla/5.0 (Macintosh; Intel Mac OS X {os_version.replace('.', '_')}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{browser_version} Safari/537.36"

        elif device_type == DeviceType.ANDROID_MOBILE:
            return f"Mozilla/5.0 (Linux; Android {os_version}; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{browser_version} Mobile Safari/537.36"

        elif device_type == DeviceType.IOS_MOBILE:
            ios_version = os_version.replace('.', '_')
            return f"Mozilla/5.0 (iPhone; CPU iPhone OS {ios_version} like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/{browser_version} Mobile/15E148 Safari/604.1"

        # 默认返回Chrome User-Agent
        return f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{browser_version} Safari/537.36"

    def _generate_browser_build(self, browser_type: BrowserType) -> str:
        """生成浏览器构建号"""
        import time
        timestamp = int(time.time())

        if browser_type == BrowserType.CHROME:
            return f"6099.{random.randint(100, 999)}"
        elif browser_type == BrowserType.FIREFOX:
            return f"20{random.randint(100000, 999999)}"
        elif browser_type == BrowserType.SAFARI:
            return f"605.1.{random.randint(10, 99)}"
        else:
            return f"{timestamp % 10000}.{random.randint(100, 999)}"

    def _generate_font_list(self, common_fonts: List[str], device_type: DeviceType) -> List[str]:
        """生成字体列表"""
        # 基础字体
        fonts = common_fonts.copy()

        # 根据设备类型添加特定字体
        if device_type == DeviceType.WINDOWS_DESKTOP:
            windows_fonts = [
                "Calibri", "Cambria", "Consolas", "Constantia", "Corbel",
                "Candara", "Franklin Gothic Medium", "Gabriola", "Malgun Gothic",
                "Microsoft YaHei", "Segoe UI", "Segoe UI Symbol"
            ]
            fonts.extend(random.sample(windows_fonts, random.randint(3, 8)))

        elif device_type == DeviceType.MAC_DESKTOP:
            mac_fonts = [
                "Helvetica Neue", "Lucida Grande", "Menlo", "Monaco",
                "Optima", "Palatino", "SF Pro Display", "SF Pro Text"
            ]
            fonts.extend(random.sample(mac_fonts, random.randint(3, 6)))

        # 随机打乱并返回
        random.shuffle(fonts)
        return fonts[:random.randint(15, 30)]

    def _generate_plugin_list(self, browser_type: BrowserType, device_type: DeviceType) -> List[Dict[str, str]]:
        """生成插件列表"""
        plugins = []

        # 现代浏览器通常只有少量插件
        if device_type in [DeviceType.WINDOWS_DESKTOP, DeviceType.MAC_DESKTOP]:
            if browser_type == BrowserType.CHROME:
                plugins.extend([
                    {
                        "name": "Chrome PDF Plugin",
                        "filename": "internal-pdf-viewer",
                        "description": "Portable Document Format"
                    },
                    {
                        "name": "Chrome PDF Viewer",
                        "filename": "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                        "description": "Portable Document Format"
                    }
                ])

            # 随机添加一些常见插件
            if random.choice([True, False]):
                plugins.append({
                    "name": "Adobe Flash Player",
                    "filename": "pepflashplayer.dll" if device_type == DeviceType.WINDOWS_DESKTOP else "PepperFlashPlayer.plugin",
                    "description": "Shockwave Flash"
                })

        return plugins

    def _generate_languages(self) -> List[str]:
        """生成语言设置"""
        language_combinations = [
            ["en-US", "en"],
            ["en-GB", "en"],
            ["zh-CN", "zh"],
            ["ja-JP", "ja"],
            ["ko-KR", "ko"],
            ["de-DE", "de"],
            ["fr-FR", "fr"],
            ["es-ES", "es"],
            ["pt-BR", "pt"],
            ["ru-RU", "ru"]
        ]

        return random.choice(language_combinations)

    def _generate_timezone(self) -> str:
        """生成时区"""
        timezones = [
            "America/New_York", "America/Los_Angeles", "America/Chicago",
            "Europe/London", "Europe/Paris", "Europe/Berlin",
            "Asia/Tokyo", "Asia/Shanghai", "Asia/Seoul",
            "Australia/Sydney", "America/Toronto"
        ]

        return random.choice(timezones)
