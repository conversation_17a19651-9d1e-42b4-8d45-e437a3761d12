"""
指纹生成模块 - 数据模型
定义设备人格和指纹相关的数据结构
"""

from enum import Enum
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime


class DeviceType(Enum):
    """设备类型枚举"""
    WINDOWS_DESKTOP = "windows_desktop"
    MAC_DESKTOP = "mac_desktop"
    LINUX_DESKTOP = "linux_desktop"
    ANDROID_MOBILE = "android_mobile"
    IOS_MOBILE = "ios_mobile"
    ANDROID_TABLET = "android_tablet"
    IPAD = "ipad"


class BrowserType(Enum):
    """浏览器类型枚举"""
    CHROME = "chrome"
    FIREFOX = "firefox"
    SAFARI = "safari"
    EDGE = "edge"
    OPERA = "opera"


@dataclass
class ScreenInfo:
    """屏幕信息"""
    width: int
    height: int
    color_depth: int = 24
    pixel_ratio: float = 1.0
    available_width: Optional[int] = None
    available_height: Optional[int] = None
    
    def __post_init__(self):
        if self.available_width is None:
            self.available_width = self.width
        if self.available_height is None:
            self.available_height = self.height - 40  # 减去任务栏高度


@dataclass
class HardwareInfo:
    """硬件信息"""
    cpu_cores: int
    memory_gb: int
    gpu_vendor: str
    gpu_renderer: str
    platform: str
    architecture: str = "x86_64"
    max_touch_points: int = 0
    
    @property
    def memory_mb(self) -> int:
        return self.memory_gb * 1024


@dataclass
class DevicePersona:
    """设备人格 - 完整的设备配置信息"""
    device_type: DeviceType
    browser_type: BrowserType
    user_agent: str
    screen_info: ScreenInfo
    hardware_info: HardwareInfo
    
    # 操作系统信息
    os_name: str
    os_version: str
    
    # 浏览器信息
    browser_version: str
    browser_build: str
    
    # 语言和地区
    languages: List[str] = field(default_factory=lambda: ["en-US", "en"])
    timezone: str = "America/New_York"
    
    # 字体列表
    fonts: List[str] = field(default_factory=list)
    
    # 插件列表
    plugins: List[Dict[str, str]] = field(default_factory=list)
    
    # WebRTC信息
    webrtc_enabled: bool = True
    webrtc_local_ips: List[str] = field(default_factory=list)
    
    # 其他特征
    do_not_track: bool = False
    cookie_enabled: bool = True
    java_enabled: bool = False
    
    # 元数据
    created_at: datetime = field(default_factory=datetime.now)
    persona_id: str = ""
    
    def __post_init__(self):
        if not self.persona_id:
            import uuid
            self.persona_id = str(uuid.uuid4())


@dataclass
class CanvasFingerprint:
    """Canvas指纹信息"""
    original_hash: str
    noised_hash: str
    noise_level: float
    noise_methods: List[str]
    image_data: Optional[bytes] = None


@dataclass
class WebGLFingerprint:
    """WebGL指纹信息"""
    vendor: str
    renderer: str
    version: str
    shading_language_version: str
    extensions: List[str]
    parameters: Dict[str, Any]
    noise_applied: bool = False


@dataclass
class AudioFingerprint:
    """Audio指纹信息"""
    context_hash: str
    oscillator_hash: str
    noise_applied: bool = False
    frequency_shift: float = 0.0
    amplitude_variance: float = 0.0


@dataclass
class FingerprintProfile:
    """完整的指纹配置文件"""
    device_persona: DevicePersona
    canvas_fingerprint: Optional[CanvasFingerprint] = None
    webgl_fingerprint: Optional[WebGLFingerprint] = None
    audio_fingerprint: Optional[AudioFingerprint] = None
    
    # 地理位置信息
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    accuracy: Optional[float] = None
    
    # 网络信息
    connection_type: str = "wifi"
    effective_type: str = "4g"
    downlink: float = 10.0
    rtt: int = 100
    
    # 权限状态
    permissions: Dict[str, str] = field(default_factory=dict)
    
    # 存储信息
    local_storage_enabled: bool = True
    session_storage_enabled: bool = True
    indexed_db_enabled: bool = True
    
    # 创建时间
    created_at: datetime = field(default_factory=datetime.now)
    profile_id: str = ""
    
    def __post_init__(self):
        if not self.profile_id:
            import uuid
            self.profile_id = str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "profile_id": self.profile_id,
            "device_persona": {
                "persona_id": self.device_persona.persona_id,
                "device_type": self.device_persona.device_type.value,
                "browser_type": self.device_persona.browser_type.value,
                "user_agent": self.device_persona.user_agent,
                "screen": {
                    "width": self.device_persona.screen_info.width,
                    "height": self.device_persona.screen_info.height,
                    "color_depth": self.device_persona.screen_info.color_depth,
                    "pixel_ratio": self.device_persona.screen_info.pixel_ratio
                },
                "hardware": {
                    "cpu_cores": self.device_persona.hardware_info.cpu_cores,
                    "memory_gb": self.device_persona.hardware_info.memory_gb,
                    "gpu_vendor": self.device_persona.hardware_info.gpu_vendor,
                    "gpu_renderer": self.device_persona.hardware_info.gpu_renderer,
                    "platform": self.device_persona.hardware_info.platform
                },
                "os": {
                    "name": self.device_persona.os_name,
                    "version": self.device_persona.os_version
                },
                "browser": {
                    "version": self.device_persona.browser_version,
                    "build": self.device_persona.browser_build
                },
                "languages": self.device_persona.languages,
                "timezone": self.device_persona.timezone,
                "fonts": self.device_persona.fonts,
                "plugins": self.device_persona.plugins
            },
            "geolocation": {
                "latitude": self.latitude,
                "longitude": self.longitude,
                "accuracy": self.accuracy
            },
            "network": {
                "connection_type": self.connection_type,
                "effective_type": self.effective_type,
                "downlink": self.downlink,
                "rtt": self.rtt
            },
            "permissions": self.permissions,
            "storage": {
                "local_storage": self.local_storage_enabled,
                "session_storage": self.session_storage_enabled,
                "indexed_db": self.indexed_db_enabled
            },
            "created_at": self.created_at.isoformat()
        }


@dataclass
class NoiseConfig:
    """噪音配置"""
    enabled: bool = True
    level: float = 0.1  # 噪音强度 0.0-1.0
    methods: List[str] = field(default_factory=list)
    seed: Optional[int] = None
    
    def __post_init__(self):
        if self.seed is None:
            import random
            self.seed = random.randint(1, 1000000)
