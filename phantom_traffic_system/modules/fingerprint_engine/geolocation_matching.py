"""
指纹生成模块 - 地理位置匹配器
确保浏览器的地理位置信息与代理IP地址严格匹配
"""

import random
import json
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("geolocation_matching")


class GeolocationMatching:
    """地理位置匹配器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化地理位置匹配器
        
        Args:
            config: 地理位置配置
        """
        self.config = config
        self.preferred_countries = config.get("preferred_countries", ["US", "CA", "GB", "DE", "FR"])
        self.city_targeting = config.get("city_targeting", True)
        self.timezone_matching = config.get("timezone_matching", True)
        
        # 加载地理位置数据库
        self.geo_database = self._load_geo_database()
        
        logger.debug(f"地理位置匹配器初始化 | 支持国家: {len(self.geo_database)}")
    
    def _load_geo_database(self) -> Dict[str, Any]:
        """加载地理位置数据库"""
        # 内置地理位置数据库
        geo_data = {
            "US": {
                "country_name": "United States",
                "timezone_offset": -5,  # EST
                "major_cities": [
                    {
                        "name": "New York",
                        "latitude": 40.7128,
                        "longitude": -74.0060,
                        "timezone": "America/New_York",
                        "timezone_offset": -5
                    },
                    {
                        "name": "Los Angeles", 
                        "latitude": 34.0522,
                        "longitude": -118.2437,
                        "timezone": "America/Los_Angeles",
                        "timezone_offset": -8
                    },
                    {
                        "name": "Chicago",
                        "latitude": 41.8781,
                        "longitude": -87.6298,
                        "timezone": "America/Chicago",
                        "timezone_offset": -6
                    },
                    {
                        "name": "Houston",
                        "latitude": 29.7604,
                        "longitude": -95.3698,
                        "timezone": "America/Chicago",
                        "timezone_offset": -6
                    },
                    {
                        "name": "Miami",
                        "latitude": 25.7617,
                        "longitude": -80.1918,
                        "timezone": "America/New_York",
                        "timezone_offset": -5
                    }
                ]
            },
            "CA": {
                "country_name": "Canada",
                "timezone_offset": -5,
                "major_cities": [
                    {
                        "name": "Toronto",
                        "latitude": 43.6532,
                        "longitude": -79.3832,
                        "timezone": "America/Toronto",
                        "timezone_offset": -5
                    },
                    {
                        "name": "Vancouver",
                        "latitude": 49.2827,
                        "longitude": -123.1207,
                        "timezone": "America/Vancouver",
                        "timezone_offset": -8
                    },
                    {
                        "name": "Montreal",
                        "latitude": 45.5017,
                        "longitude": -73.5673,
                        "timezone": "America/Montreal",
                        "timezone_offset": -5
                    }
                ]
            },
            "GB": {
                "country_name": "United Kingdom",
                "timezone_offset": 0,
                "major_cities": [
                    {
                        "name": "London",
                        "latitude": 51.5074,
                        "longitude": -0.1278,
                        "timezone": "Europe/London",
                        "timezone_offset": 0
                    },
                    {
                        "name": "Manchester",
                        "latitude": 53.4808,
                        "longitude": -2.2426,
                        "timezone": "Europe/London",
                        "timezone_offset": 0
                    },
                    {
                        "name": "Birmingham",
                        "latitude": 52.4862,
                        "longitude": -1.8904,
                        "timezone": "Europe/London",
                        "timezone_offset": 0
                    }
                ]
            },
            "DE": {
                "country_name": "Germany",
                "timezone_offset": 1,
                "major_cities": [
                    {
                        "name": "Berlin",
                        "latitude": 52.5200,
                        "longitude": 13.4050,
                        "timezone": "Europe/Berlin",
                        "timezone_offset": 1
                    },
                    {
                        "name": "Munich",
                        "latitude": 48.1351,
                        "longitude": 11.5820,
                        "timezone": "Europe/Berlin",
                        "timezone_offset": 1
                    },
                    {
                        "name": "Frankfurt",
                        "latitude": 50.1109,
                        "longitude": 8.6821,
                        "timezone": "Europe/Berlin",
                        "timezone_offset": 1
                    }
                ]
            },
            "FR": {
                "country_name": "France",
                "timezone_offset": 1,
                "major_cities": [
                    {
                        "name": "Paris",
                        "latitude": 48.8566,
                        "longitude": 2.3522,
                        "timezone": "Europe/Paris",
                        "timezone_offset": 1
                    },
                    {
                        "name": "Lyon",
                        "latitude": 45.7640,
                        "longitude": 4.8357,
                        "timezone": "Europe/Paris",
                        "timezone_offset": 1
                    },
                    {
                        "name": "Marseille",
                        "latitude": 43.2965,
                        "longitude": 5.3698,
                        "timezone": "Europe/Paris",
                        "timezone_offset": 1
                    }
                ]
            },
            "AU": {
                "country_name": "Australia",
                "timezone_offset": 10,
                "major_cities": [
                    {
                        "name": "Sydney",
                        "latitude": -33.8688,
                        "longitude": 151.2093,
                        "timezone": "Australia/Sydney",
                        "timezone_offset": 10
                    },
                    {
                        "name": "Melbourne",
                        "latitude": -37.8136,
                        "longitude": 144.9631,
                        "timezone": "Australia/Melbourne",
                        "timezone_offset": 10
                    },
                    {
                        "name": "Brisbane",
                        "latitude": -27.4698,
                        "longitude": 153.0251,
                        "timezone": "Australia/Brisbane",
                        "timezone_offset": 10
                    }
                ]
            },
            "JP": {
                "country_name": "Japan",
                "timezone_offset": 9,
                "major_cities": [
                    {
                        "name": "Tokyo",
                        "latitude": 35.6762,
                        "longitude": 139.6503,
                        "timezone": "Asia/Tokyo",
                        "timezone_offset": 9
                    },
                    {
                        "name": "Osaka",
                        "latitude": 34.6937,
                        "longitude": 135.5023,
                        "timezone": "Asia/Tokyo",
                        "timezone_offset": 9
                    }
                ]
            }
        }
        
        return geo_data
    
    def match_geolocation_to_proxy(self, proxy_ip: str, proxy_country: Optional[str] = None, 
                                  proxy_city: Optional[str] = None) -> Dict[str, Any]:
        """
        根据代理IP匹配地理位置信息
        
        Args:
            proxy_ip: 代理IP地址
            proxy_country: 代理所在国家代码
            proxy_city: 代理所在城市
            
        Returns:
            匹配的地理位置信息
        """
        # 如果没有提供国家信息，尝试从IP推断或使用默认
        if not proxy_country:
            proxy_country = self._detect_country_from_ip(proxy_ip)
        
        # 确保国家代码在支持列表中
        if proxy_country not in self.geo_database:
            logger.warning(f"不支持的国家代码: {proxy_country}，使用默认US")
            proxy_country = "US"
        
        country_data = self.geo_database[proxy_country]
        
        # 选择城市
        if self.city_targeting and proxy_city:
            # 尝试匹配指定城市
            city_data = self._find_city_by_name(country_data, proxy_city)
        else:
            # 随机选择一个主要城市
            city_data = random.choice(country_data["major_cities"])
        
        # 添加随机偏移以增加真实性
        latitude, longitude = self._add_location_noise(city_data["latitude"], city_data["longitude"])
        
        # 生成地理位置信息
        geolocation_info = {
            "country": proxy_country,
            "country_name": country_data["country_name"],
            "city": city_data["name"],
            "latitude": latitude,
            "longitude": longitude,
            "accuracy": random.uniform(10, 100),  # 精度范围10-100米
            "timezone": city_data["timezone"],
            "timezone_offset": city_data["timezone_offset"]
        }
        
        logger.debug(f"地理位置匹配 | IP: {proxy_ip} | 国家: {proxy_country} | 城市: {city_data['name']}")
        
        return geolocation_info
    
    def _detect_country_from_ip(self, ip: str) -> str:
        """从IP地址检测国家（简化实现）"""
        # 这里是简化实现，实际应该使用GeoIP数据库
        # 暂时返回随机的首选国家
        return random.choice(self.preferred_countries)
    
    def _find_city_by_name(self, country_data: Dict[str, Any], city_name: str) -> Dict[str, Any]:
        """根据城市名称查找城市数据"""
        for city in country_data["major_cities"]:
            if city["name"].lower() == city_name.lower():
                return city
        
        # 如果找不到指定城市，返回随机城市
        return random.choice(country_data["major_cities"])
    
    def _add_location_noise(self, latitude: float, longitude: float) -> Tuple[float, float]:
        """为地理位置添加随机噪音"""
        # 添加小范围的随机偏移（约1-5公里）
        lat_noise = random.uniform(-0.05, 0.05)  # 约5.5公里
        lng_noise = random.uniform(-0.05, 0.05)
        
        return latitude + lat_noise, longitude + lng_noise
    
    def get_geolocation_script(self, geolocation_info: Dict[str, Any]) -> str:
        """
        生成地理位置伪装脚本
        
        Args:
            geolocation_info: 地理位置信息
            
        Returns:
            JavaScript代码字符串
        """
        script = f"""
        // 地理位置伪装脚本
        (function() {{
            const spoofedPosition = {{
                coords: {{
                    latitude: {geolocation_info['latitude']},
                    longitude: {geolocation_info['longitude']},
                    accuracy: {geolocation_info['accuracy']},
                    altitude: null,
                    altitudeAccuracy: null,
                    heading: null,
                    speed: null
                }},
                timestamp: Date.now()
            }};
            
            // 重写navigator.geolocation.getCurrentPosition
            const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition;
            navigator.geolocation.getCurrentPosition = function(success, error, options) {{
                if (success) {{
                    setTimeout(() => {{
                        success(spoofedPosition);
                    }}, Math.random() * 100 + 50); // 模拟真实的延迟
                }}
            }};
            
            // 重写navigator.geolocation.watchPosition
            const originalWatchPosition = navigator.geolocation.watchPosition;
            navigator.geolocation.watchPosition = function(success, error, options) {{
                if (success) {{
                    setTimeout(() => {{
                        success(spoofedPosition);
                    }}, Math.random() * 100 + 50);
                }}
                return Math.floor(Math.random() * 1000000); // 返回随机watch ID
            }};
            
            // 重写时区相关信息
            const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
            Intl.DateTimeFormat.prototype.resolvedOptions = function() {{
                const options = originalResolvedOptions.call(this);
                options.timeZone = "{geolocation_info['timezone']}";
                return options;
            }};
            
            // 重写Date.prototype.getTimezoneOffset
            const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
            Date.prototype.getTimezoneOffset = function() {{
                return {geolocation_info['timezone_offset'] * -60}; // 转换为分钟并取反
            }};
            
            // 重写navigator.language和navigator.languages
            const countryLanguageMap = {{
                'US': ['en-US', 'en'],
                'CA': ['en-CA', 'fr-CA', 'en'],
                'GB': ['en-GB', 'en'],
                'DE': ['de-DE', 'de', 'en'],
                'FR': ['fr-FR', 'fr', 'en'],
                'AU': ['en-AU', 'en'],
                'JP': ['ja-JP', 'ja', 'en']
            }};
            
            const languages = countryLanguageMap["{geolocation_info['country']}"] || ['en-US', 'en'];
            
            Object.defineProperty(navigator, 'language', {{
                get: function() {{
                    return languages[0];
                }},
                configurable: true
            }});
            
            Object.defineProperty(navigator, 'languages', {{
                get: function() {{
                    return languages.slice();
                }},
                configurable: true
            }});
        }})();
        """
        
        return script.strip()
    
    def validate_geolocation_consistency(self, proxy_info: Dict[str, Any], 
                                       geolocation_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证地理位置一致性
        
        Args:
            proxy_info: 代理信息
            geolocation_info: 地理位置信息
            
        Returns:
            验证结果
        """
        validation_result = {
            "is_consistent": True,
            "issues": [],
            "confidence_score": 100
        }
        
        # 检查国家一致性
        proxy_country = proxy_info.get("country")
        geo_country = geolocation_info.get("country")
        
        if proxy_country and geo_country and proxy_country != geo_country:
            validation_result["is_consistent"] = False
            validation_result["issues"].append(f"国家不匹配: 代理({proxy_country}) vs 地理位置({geo_country})")
            validation_result["confidence_score"] -= 50
        
        # 检查城市一致性（如果有的话）
        proxy_city = proxy_info.get("city")
        geo_city = geolocation_info.get("city")
        
        if proxy_city and geo_city and proxy_city.lower() != geo_city.lower():
            validation_result["issues"].append(f"城市不匹配: 代理({proxy_city}) vs 地理位置({geo_city})")
            validation_result["confidence_score"] -= 20
        
        # 检查时区一致性
        if self.timezone_matching:
            expected_timezone = geolocation_info.get("timezone")
            if not expected_timezone:
                validation_result["issues"].append("缺少时区信息")
                validation_result["confidence_score"] -= 10
        
        # 检查坐标合理性
        latitude = geolocation_info.get("latitude")
        longitude = geolocation_info.get("longitude")
        
        if latitude is None or longitude is None:
            validation_result["is_consistent"] = False
            validation_result["issues"].append("缺少坐标信息")
            validation_result["confidence_score"] -= 30
        elif not (-90 <= latitude <= 90) or not (-180 <= longitude <= 180):
            validation_result["is_consistent"] = False
            validation_result["issues"].append("坐标超出有效范围")
            validation_result["confidence_score"] -= 40
        
        return validation_result
    
    def get_supported_countries(self) -> List[str]:
        """获取支持的国家列表"""
        return list(self.geo_database.keys())
    
    def get_cities_by_country(self, country_code: str) -> List[Dict[str, Any]]:
        """获取指定国家的城市列表"""
        if country_code in self.geo_database:
            return self.geo_database[country_code]["major_cities"]
        return []
