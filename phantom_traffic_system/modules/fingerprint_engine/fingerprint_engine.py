"""
指纹生成模块 - 指纹引擎
统一管理所有指纹生成和伪装功能
"""

import random
from typing import Dict, List, Any, Optional

from .models import Device<PERSON>ersona, FingerprintProfile, DeviceType, BrowserType
from .device_persona import DevicePersonaGenerator
from .canvas_noise import CanvasNoiseGenerator
from .webgl_noise import WebGLNoiseGenerator
from .audio_noise import AudioNoiseGenerator
from .user_agent_spoofing import UserAgentSpoofing
from .geolocation_matching import GeolocationMatching
from ...core.logger import phantom_logger

logger = phantom_logger.get_logger("fingerprint_engine")


class FingerprintEngine:
    """指纹引擎 - 指纹系统的中央控制器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化指纹引擎
        
        Args:
            config: 指纹配置
        """
        self.config = config
        
        # 初始化各个组件
        self.device_persona_generator = DevicePersonaGenerator(config)
        self.canvas_noise_generator = CanvasNoiseGenerator(config.get("canvas_noise", {}))
        self.webgl_noise_generator = WebGLNoiseGenerator(config.get("webgl_noise", {}))
        self.audio_noise_generator = AudioNoiseGenerator(config.get("audio_noise", {}))
        self.user_agent_spoofing = UserAgentSpoofing(config.get("fonts_plugins", {}))
        self.geolocation_matching = GeolocationMatching(config.get("geolocation", {}))
        
        logger.info("指纹引擎初始化完成")
    
    def generate_complete_fingerprint(self, 
                                    device_type: Optional[DeviceType] = None,
                                    browser_type: Optional[BrowserType] = None,
                                    proxy_info: Optional[Dict[str, Any]] = None) -> FingerprintProfile:
        """
        生成完整的指纹配置文件
        
        Args:
            device_type: 指定设备类型
            browser_type: 指定浏览器类型
            proxy_info: 代理信息（用于地理位置匹配）
            
        Returns:
            完整的指纹配置文件
        """
        logger.info(f"开始生成指纹 | 设备: {device_type} | 浏览器: {browser_type}")
        
        # 1. 生成设备人格
        device_persona = self.device_persona_generator.generate_persona(device_type, browser_type)
        
        # 2. 生成Canvas指纹
        canvas_fingerprint = self.canvas_noise_generator.generate_canvas_fingerprint()
        
        # 3. 生成WebGL指纹
        webgl_fingerprint = self.webgl_noise_generator.generate_webgl_fingerprint(device_persona)
        
        # 4. 生成Audio指纹
        audio_fingerprint = self.audio_noise_generator.generate_audio_fingerprint()
        
        # 5. 生成地理位置信息
        geolocation_info = None
        if proxy_info:
            geolocation_info = self.geolocation_matching.match_geolocation_to_proxy(
                proxy_info.get("ip", ""),
                proxy_info.get("country"),
                proxy_info.get("city")
            )
        
        # 6. 创建完整的指纹配置文件
        fingerprint_profile = FingerprintProfile(
            device_persona=device_persona,
            canvas_fingerprint=canvas_fingerprint,
            webgl_fingerprint=webgl_fingerprint,
            audio_fingerprint=audio_fingerprint
        )
        
        # 7. 设置地理位置信息
        if geolocation_info:
            fingerprint_profile.latitude = geolocation_info["latitude"]
            fingerprint_profile.longitude = geolocation_info["longitude"]
            fingerprint_profile.accuracy = geolocation_info["accuracy"]
        
        # 8. 设置网络信息
        self._set_network_info(fingerprint_profile, device_persona)
        
        # 9. 设置权限状态
        self._set_permissions(fingerprint_profile, device_persona)
        
        logger.info(f"指纹生成完成 | ID: {fingerprint_profile.profile_id}")
        
        return fingerprint_profile
    
    def _set_network_info(self, profile: FingerprintProfile, device_persona: DevicePersona):
        """设置网络信息"""
        if device_persona.device_type in [DeviceType.ANDROID_MOBILE, DeviceType.IOS_MOBILE]:
            # 移动设备
            profile.connection_type = "cellular"
            profile.effective_type = "4g"
            profile.downlink = random.uniform(5.0, 25.0)
            profile.rtt = random.randint(50, 200)
        else:
            # 桌面设备
            profile.connection_type = "wifi"
            profile.effective_type = "4g"
            profile.downlink = random.uniform(10.0, 100.0)
            profile.rtt = random.randint(20, 100)
    
    def _set_permissions(self, profile: FingerprintProfile, device_persona: DevicePersona):
        """设置权限状态"""
        
        # 基础权限
        permissions = {
            "geolocation": random.choice(["granted", "denied", "prompt"]),
            "notifications": random.choice(["granted", "denied", "prompt"]),
            "camera": random.choice(["granted", "denied", "prompt"]),
            "microphone": random.choice(["granted", "denied", "prompt"])
        }
        
        # 移动设备特有权限
        if device_persona.device_type in [DeviceType.ANDROID_MOBILE, DeviceType.IOS_MOBILE]:
            permissions.update({
                "accelerometer": "granted",
                "gyroscope": "granted",
                "magnetometer": "granted"
            })
        
        profile.permissions = permissions
    
    def generate_injection_scripts(self, profile: FingerprintProfile) -> Dict[str, str]:
        """
        生成所有注入脚本
        
        Args:
            profile: 指纹配置文件
            
        Returns:
            脚本字典
        """
        scripts = {}
        
        # User-Agent伪装脚本
        scripts["user_agent"] = self.user_agent_spoofing.get_spoofing_script(
            profile.device_persona.user_agent
        )
        
        # Canvas指纹伪装脚本
        if profile.canvas_fingerprint:
            scripts["canvas"] = self.canvas_noise_generator.get_canvas_script(
                profile.canvas_fingerprint
            )
        
        # WebGL指纹伪装脚本
        if profile.webgl_fingerprint:
            scripts["webgl"] = self.webgl_noise_generator.get_webgl_script(
                profile.webgl_fingerprint
            )
        
        # Audio指纹伪装脚本
        if profile.audio_fingerprint:
            scripts["audio"] = self.audio_noise_generator.get_audio_script(
                profile.audio_fingerprint
            )
        
        # 地理位置伪装脚本
        if profile.latitude is not None and profile.longitude is not None:
            geolocation_info = {
                "latitude": profile.latitude,
                "longitude": profile.longitude,
                "accuracy": profile.accuracy,
                "country": "US",  # 从设备人格推断
                "timezone": profile.device_persona.timezone
            }
            scripts["geolocation"] = self.geolocation_matching.get_geolocation_script(
                geolocation_info
            )
        
        # 通用反检测脚本
        scripts["anti_detection"] = self._generate_anti_detection_script(profile)
        
        logger.debug(f"生成注入脚本 | 脚本数量: {len(scripts)}")
        
        return scripts
    
    def _generate_anti_detection_script(self, profile: FingerprintProfile) -> str:
        """生成通用反检测脚本"""
        device_persona = profile.device_persona
        
        script = f"""
        // 通用反检测脚本
        (function() {{
            // 移除webdriver属性
            delete navigator.__proto__.webdriver;
            delete navigator.webdriver;
            
            // 重写navigator属性
            Object.defineProperty(navigator, 'platform', {{
                get: function() {{
                    return "{device_persona.hardware_info.platform}";
                }},
                configurable: true
            }});
            
            Object.defineProperty(navigator, 'hardwareConcurrency', {{
                get: function() {{
                    return {device_persona.hardware_info.cpu_cores};
                }},
                configurable: true
            }});
            
            Object.defineProperty(navigator, 'deviceMemory', {{
                get: function() {{
                    return {device_persona.hardware_info.memory_gb};
                }},
                configurable: true
            }});
            
            Object.defineProperty(navigator, 'maxTouchPoints', {{
                get: function() {{
                    return {device_persona.hardware_info.max_touch_points};
                }},
                configurable: true
            }});
            
            // 重写screen属性
            Object.defineProperty(screen, 'width', {{
                get: function() {{
                    return {device_persona.screen_info.width};
                }},
                configurable: true
            }});
            
            Object.defineProperty(screen, 'height', {{
                get: function() {{
                    return {device_persona.screen_info.height};
                }},
                configurable: true
            }});
            
            Object.defineProperty(screen, 'colorDepth', {{
                get: function() {{
                    return {device_persona.screen_info.color_depth};
                }},
                configurable: true
            }});
            
            Object.defineProperty(screen, 'pixelDepth', {{
                get: function() {{
                    return {device_persona.screen_info.color_depth};
                }},
                configurable: true
            }});
            
            Object.defineProperty(screen, 'availWidth', {{
                get: function() {{
                    return {device_persona.screen_info.available_width};
                }},
                configurable: true
            }});
            
            Object.defineProperty(screen, 'availHeight', {{
                get: function() {{
                    return {device_persona.screen_info.available_height};
                }},
                configurable: true
            }});
            
            // 重写window.devicePixelRatio
            Object.defineProperty(window, 'devicePixelRatio', {{
                get: function() {{
                    return {device_persona.screen_info.pixel_ratio};
                }},
                configurable: true
            }});
            
            // 重写navigator.cookieEnabled
            Object.defineProperty(navigator, 'cookieEnabled', {{
                get: function() {{
                    return {str(device_persona.cookie_enabled).lower()};
                }},
                configurable: true
            }});
            
            // 重写navigator.doNotTrack
            Object.defineProperty(navigator, 'doNotTrack', {{
                get: function() {{
                    return "{1 if device_persona.do_not_track else 'null'}";
                }},
                configurable: true
            }});
            
            // 重写navigator.javaEnabled
            navigator.javaEnabled = function() {{
                return {str(device_persona.java_enabled).lower()};
            }};
            
            // 隐藏自动化特征
            const originalQuery = window.document.querySelector;
            window.document.querySelector = function(selector) {{
                if (selector === 'script[src*="webdriver"]' || 
                    selector === 'script[src*="chromedriver"]' ||
                    selector === 'script[src*="automation"]') {{
                    return null;
                }}
                return originalQuery.call(this, selector);
            }};
            
            // 重写权限API
            const originalQuery = navigator.permissions.query;
            navigator.permissions.query = function(permission) {{
                const permissionName = permission.name;
                const permissionState = {str(profile.permissions).replace("'", '"')};
                
                return Promise.resolve({{
                    state: permissionState[permissionName] || 'prompt',
                    onchange: null
                }});
            }};
        }})();
        """
        
        return script.strip()
    
    def validate_fingerprint_consistency(self, profile: FingerprintProfile) -> Dict[str, Any]:
        """
        验证指纹一致性
        
        Args:
            profile: 指纹配置文件
            
        Returns:
            验证结果
        """
        validation_result = {
            "is_consistent": True,
            "issues": [],
            "confidence_score": 100
        }
        
        device_persona = profile.device_persona
        
        # 检查User-Agent与设备信息的一致性
        ua_validation = self.user_agent_spoofing.validate_user_agent(device_persona.user_agent)
        if not ua_validation["is_valid"]:
            validation_result["issues"].extend(ua_validation["issues"])
            validation_result["confidence_score"] -= 20
        
        # 检查屏幕分辨率的合理性
        if device_persona.device_type in [DeviceType.ANDROID_MOBILE, DeviceType.IOS_MOBILE]:
            if device_persona.screen_info.width > 500 or device_persona.screen_info.height > 1000:
                validation_result["issues"].append("移动设备屏幕分辨率过大")
                validation_result["confidence_score"] -= 15
        
        # 检查硬件配置的合理性
        if device_persona.hardware_info.cpu_cores > 32:
            validation_result["issues"].append("CPU核心数过多")
            validation_result["confidence_score"] -= 10
        
        if device_persona.hardware_info.memory_gb > 128:
            validation_result["issues"].append("内存容量过大")
            validation_result["confidence_score"] -= 10
        
        # 检查字体与操作系统的一致性
        if device_persona.device_type == DeviceType.WINDOWS_DESKTOP:
            if "Helvetica Neue" in device_persona.fonts:
                validation_result["issues"].append("Windows系统不应包含Mac字体")
                validation_result["confidence_score"] -= 5
        
        if validation_result["issues"]:
            validation_result["is_consistent"] = False
        
        return validation_result
    
    def get_fingerprint_summary(self, profile: FingerprintProfile) -> Dict[str, Any]:
        """
        获取指纹摘要信息
        
        Args:
            profile: 指纹配置文件
            
        Returns:
            指纹摘要
        """
        return {
            "profile_id": profile.profile_id,
            "device_type": profile.device_persona.device_type.value,
            "browser_type": profile.device_persona.browser_type.value,
            "user_agent": profile.device_persona.user_agent[:100] + "...",
            "screen_resolution": f"{profile.device_persona.screen_info.width}x{profile.device_persona.screen_info.height}",
            "cpu_cores": profile.device_persona.hardware_info.cpu_cores,
            "memory_gb": profile.device_persona.hardware_info.memory_gb,
            "canvas_hash": profile.canvas_fingerprint.noised_hash[:8] + "..." if profile.canvas_fingerprint else None,
            "webgl_renderer": profile.webgl_fingerprint.renderer[:30] + "..." if profile.webgl_fingerprint else None,
            "geolocation": f"{profile.latitude:.4f},{profile.longitude:.4f}" if profile.latitude else None,
            "created_at": profile.created_at.isoformat()
        }
