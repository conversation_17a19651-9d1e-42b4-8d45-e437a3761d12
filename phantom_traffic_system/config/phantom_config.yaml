# 幻影流量系统核心配置文件
# Project: Phantom Traffic System

# 系统全局配置
system:
  name: "Phantom Traffic System"
  version: "1.0.0"
  debug: true
  max_concurrent_sessions: 50
  session_timeout: 300  # 5分钟
  
# 代理管理配置
proxy:
  # 代理供应商配置
  providers:
    mobile_4g_5g:
      enabled: true
      priority: 1  # 最高优先级
      api_endpoints:
        - "https://api.mobile-proxy-provider.com/v1"
      auth:
        api_key: "${MOBILE_PROXY_API_KEY}"
        secret: "${MOBILE_PROXY_SECRET}"
      rotation_strategy: "session_sticky"  # session_sticky | high_frequency
      session_duration: 900  # 15分钟
      
    residential:
      enabled: true
      priority: 2
      api_endpoints:
        - "https://api.residential-proxy.com/v2"
      auth:
        username: "${RESIDENTIAL_PROXY_USER}"
        password: "${RESIDENTIAL_PROXY_PASS}"
      rotation_strategy: "session_sticky"
      session_duration: 600  # 10分钟
      
  # IP健康检查
  health_check:
    enabled: true
    timeout: 10
    check_urls:
      - "https://httpbin.org/ip"
      - "https://api.ipify.org"
    anonymity_check: true
    speed_threshold: 5000  # ms
    
  # 地理位置配置
  geolocation:
    preferred_countries: ["US", "CA", "GB", "DE", "FR", "AU"]
    city_targeting: true
    timezone_matching: true

# 指纹生成与伪装配置
fingerprint:
  # 设备人格数据库
  device_personas:
    database_path: "data/device_personas.json"
    auto_update: true
    categories:
      - "windows_desktop"
      - "mac_desktop" 
      - "android_mobile"
      - "ios_mobile"
      - "linux_desktop"
      
  # Canvas指纹加噪
  canvas_noise:
    enabled: true
    noise_level: 0.1  # 0.1 = 10%噪音
    methods: ["pixel_shift", "color_variance", "geometric_distortion"]
    
  # WebGL指纹加噪
  webgl_noise:
    enabled: true
    shader_modification: true
    renderer_spoofing: true
    
  # Audio指纹加噪
  audio_noise:
    enabled: true
    frequency_shift: true
    amplitude_variance: 0.05
    
  # 字体与插件伪装
  fonts_plugins:
    realistic_combinations: true
    os_specific_fonts: true
    common_plugins_only: true

# 真人行为模拟配置
behavior:
  # 鼠标移动
  mouse_movement:
    algorithm: "bezier_curves"
    speed_variance: 0.3  # 30%速度变化
    micro_movements: true
    pause_probability: 0.15  # 15%概率暂停
    
  # 页面滚动
  scrolling:
    method: "segmented"  # segmented | smooth | inertial
    speed_range: [100, 800]  # pixels per second
    pause_duration: [0.5, 3.0]  # seconds
    
  # 时间控制
  timing:
    page_load_wait: [2, 8]  # seconds
    interaction_delay: [0.5, 2.5]  # seconds
    reading_time_per_word: [0.2, 0.4]  # seconds
    
  # 真实性模式
  realism:
    typos_enabled: false  # 暂时关闭打字错误
    back_button_usage: 0.05  # 5%概率使用后退
    tab_switching: 0.03  # 3%概率切换标签

# 会话与任务调度配置
session:
  # 会话预热
  warmup:
    enabled: true
    steps:
      - action: "google_search"
        keywords: ["news", "weather", "sports", "technology"]
        duration: [30, 90]
      - action: "visit_random_site"
        sites: ["wikipedia.org", "reddit.com", "stackoverflow.com"]
        duration: [60, 180]
        
  # 任务调度
  scheduling:
    max_tasks_per_hour: 100
    traffic_curve: "realistic"  # flat | realistic | custom
    peak_hours: [9, 12, 14, 17, 20]  # 流量高峰时段
    
  # 分布式配置
  distributed:
    enabled: false  # 单机模式
    coordination_server: "redis://localhost:6379"
    worker_id: "phantom_worker_001"

# 浏览器配置
browser:
  # 默认浏览器引擎
  engine: "playwright"  # playwright | selenium | undetected_chrome
  
  # Playwright配置
  playwright:
    browser_type: "chromium"  # chromium | firefox | webkit
    headless: true
    stealth_mode: true
    
  # 启动参数
  launch_args:
    - "--no-sandbox"
    - "--disable-blink-features=AutomationControlled"
    - "--disable-features=VizDisplayCompositor"
    - "--disable-ipc-flooding-protection"
    - "--disable-renderer-backgrounding"
    - "--disable-backgrounding-occluded-windows"
    - "--disable-field-trial-config"
    
  # 页面配置
  page:
    viewport_randomization: true
    user_agent_rotation: true
    accept_language_spoofing: true

# 数据存储配置
storage:
  # 数据库类型
  database: "sqlite"  # sqlite | mongodb | redis
  
  # SQLite配置
  sqlite:
    path: "data/phantom.db"
    
  # 日志配置
  logging:
    level: "INFO"  # DEBUG | INFO | WARNING | ERROR
    file_path: "logs/phantom.log"
    max_size: "100MB"
    backup_count: 5

# 安全与隐私配置
security:
  # 数据加密
  encryption:
    enabled: true
    algorithm: "AES-256"
    key_rotation: 86400  # 24小时
    
  # 隐私保护
  privacy:
    clear_cookies: true
    clear_cache: true
    clear_local_storage: true
    
  # 反检测措施
  anti_detection:
    webdriver_property_removal: true
    chrome_runtime_patching: true
    navigator_permissions_spoofing: true
