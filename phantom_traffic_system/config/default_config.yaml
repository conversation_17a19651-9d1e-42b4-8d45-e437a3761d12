# 幻影流量系统 - 默认配置文件
# 这是一个完整的配置示例，包含所有模块的配置选项

# 系统全局配置
system:
  name: "幻影流量系统"
  version: "1.0.0"
  debug: false
  log_level: "INFO"

# 代理管理器配置
proxy_manager:
  enabled: true
  
  # 代理源配置
  sources:
    - type: "file"
      path: "proxies.txt"
      format: "ip:port:username:password"
    - type: "api"
      url: "https://api.proxy-provider.com/list"
      auth_token: "your_token_here"
      refresh_interval: 3600
  
  # 代理验证配置
  validation:
    enabled: true
    timeout: 10
    test_urls:
      - "https://httpbin.org/ip"
      - "https://api.ipify.org"
    max_retries: 3
    validation_interval: 300
  
  # 代理轮换配置
  rotation:
    strategy: "round_robin"  # round_robin, random, least_used
    max_usage_per_proxy: 100
    cooldown_period: 60
  
  # 地理位置过滤
  geo_filter:
    enabled: false
    allowed_countries: ["US", "CA", "GB", "DE", "FR"]
    blocked_countries: ["CN", "RU"]

# 指纹生成引擎配置
fingerprint_engine:
  enabled: true
  
  # 设备指纹配置
  device_fingerprints:
    enabled: true
    profiles_path: "fingerprints/devices.json"
    randomization_level: "medium"  # low, medium, high
  
  # 浏览器指纹配置
  browser_fingerprints:
    enabled: true
    user_agents_path: "fingerprints/user_agents.json"
    canvas_fingerprinting: true
    webgl_fingerprinting: true
    audio_fingerprinting: true
  
  # 网络指纹配置
  network_fingerprints:
    enabled: true
    tcp_fingerprinting: true
    tls_fingerprinting: true
    http_headers_randomization: true
  
  # 时区和语言配置
  locale_settings:
    randomize_timezone: true
    randomize_language: true
    supported_locales:
      - "en-US"
      - "en-GB" 
      - "de-DE"
      - "fr-FR"
      - "es-ES"

# 行为模拟器配置
behavior_simulator:
  enabled: true
  
  # 鼠标行为配置
  mouse_behavior:
    enabled: true
    movement_patterns:
      - "human_like"
      - "jittery"
      - "smooth"
    click_patterns:
      - "single_click"
      - "double_click"
      - "right_click"
    scroll_patterns:
      - "smooth_scroll"
      - "page_scroll"
      - "random_scroll"
  
  # 键盘行为配置
  keyboard_behavior:
    enabled: true
    typing_speed: "medium"  # slow, medium, fast
    typing_patterns:
      - "hunt_and_peck"
      - "touch_typing"
      - "mixed"
  
  # 页面交互配置
  page_interaction:
    enabled: true
    interaction_probability: 0.7
    max_interactions_per_page: 5
    interaction_types:
      - "click_links"
      - "fill_forms"
      - "scroll_page"
      - "hover_elements"
  
  # 时间行为配置
  timing_behavior:
    page_load_wait: [2, 8]  # 秒，范围
    interaction_delay: [0.5, 3.0]  # 秒，范围
    session_duration: [30, 300]  # 秒，范围

# 会话调度器配置
session_scheduler:
  enabled: true
  
  # 任务队列配置
  task_queue:
    max_queue_size: 1000
    priority_levels: 3
    worker_threads: 5
  
  # 会话预热配置
  warmup:
    enabled: true
    warmup_steps: 3
    warmup_urls:
      - "https://www.google.com"
      - "https://www.bing.com"
      - "https://duckduckgo.com"
  
  # 流量控制配置
  traffic_control:
    enabled: true
    default_pattern: "realistic"  # flat, realistic, burst, gradual
    base_rate: 100  # 任务/小时
    max_rate: 1000  # 任务/小时
  
  # 分布式配置
  distributed:
    enabled: false
    is_master: true
    node_id: "master_node_1"
    listen_port: 8080
    master_host: "localhost"
    master_port: 8080
    heartbeat_interval: 30
    node_timeout: 120

# 浏览器控制器配置
browser_controller:
  enabled: true
  
  # 浏览器池配置
  browser_pool:
    max_instances: 10
    min_instances: 2
    instance_timeout: 3600  # 秒
    health_check_interval: 60  # 秒
  
  # 浏览器配置
  browser_config:
    browser_type: "chromium"  # chromium, firefox, webkit
    headless: true
    viewport_width: 1920
    viewport_height: 1080
    page_load_timeout: 30.0
    navigation_timeout: 30.0
  
  # 反检测配置
  anti_detection:
    enabled: true
    hide_webdriver: true
    spoof_permissions: true
    randomize_viewport: true
    human_delays: true
    mouse_movements: true
    patch_chrome_runtime: true
    patch_permissions_api: true
    disable_automation_indicators: true
  
  # 页面交互配置
  page_interaction:
    default_timeout: 30000  # 毫秒
    navigation_timeout: 30000  # 毫秒
    human_delays: true
    screenshot_on_error: true

# API服务器配置
api_server:
  enabled: true
  host: "0.0.0.0"
  port: 8080
  ssl_enabled: false
  ssl_cert_path: null
  ssl_key_path: null
  debug: false
  static_dir: null

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_enabled: true
  file_path: "logs/phantom_traffic.log"
  file_max_size: "10MB"
  file_backup_count: 5
  console_enabled: true

# 目标URL配置
targets:
  urls:
    - "https://example.com"
    - "https://httpbin.org"
  
  # URL分组配置
  groups:
    test_sites:
      - "https://httpbin.org/get"
      - "https://httpbin.org/headers"
    
    search_engines:
      - "https://www.google.com"
      - "https://www.bing.com"
      - "https://duckduckgo.com"

# 数据存储配置
storage:
  enabled: false
  type: "sqlite"  # sqlite, mysql, postgresql
  connection_string: "sqlite:///phantom_traffic.db"
  
  # 数据保留配置
  retention:
    session_data: 30  # 天
    log_data: 7  # 天
    statistics_data: 90  # 天

# 监控和告警配置
monitoring:
  enabled: false
  
  # 性能监控
  performance:
    enabled: true
    metrics_interval: 60  # 秒
    
  # 告警配置
  alerts:
    enabled: false
    webhook_url: null
    email_config:
      smtp_server: null
      smtp_port: 587
      username: null
      password: null
      recipients: []

# 安全配置
security:
  # API认证
  api_auth:
    enabled: false
    type: "token"  # token, basic, oauth
    token: "your_secret_token_here"
  
  # 访问控制
  access_control:
    enabled: false
    allowed_ips: []
    blocked_ips: []
  
  # 加密配置
  encryption:
    enabled: false
    key_path: "keys/encryption.key"
