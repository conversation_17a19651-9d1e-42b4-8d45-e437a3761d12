"""
幻影流量系统 - 系统优化器
提供系统性能优化和调优功能
"""

import asyncio
import psutil
import gc
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from ..core.logger import phantom_logger

logger = phantom_logger.get_logger("system_optimizer")


class SystemOptimizer:
    """系统优化器"""
    
    def __init__(self, phantom_system):
        """
        初始化系统优化器
        
        Args:
            phantom_system: 幻影流量系统实例
        """
        self.phantom_system = phantom_system
        self.optimization_history = []
        self.performance_metrics = {}
        
        logger.debug("系统优化器初始化完成")
    
    async def analyze_system_performance(self) -> Dict[str, Any]:
        """分析系统性能"""
        logger.info("开始系统性能分析...")
        
        analysis = {
            "timestamp": datetime.now().isoformat(),
            "system_resources": self._analyze_system_resources(),
            "module_performance": await self._analyze_module_performance(),
            "memory_usage": self._analyze_memory_usage(),
            "bottlenecks": [],
            "recommendations": []
        }
        
        # 识别性能瓶颈
        bottlenecks = self._identify_bottlenecks(analysis)
        analysis["bottlenecks"] = bottlenecks
        
        # 生成优化建议
        recommendations = self._generate_recommendations(analysis)
        analysis["recommendations"] = recommendations
        
        logger.info(f"性能分析完成，发现 {len(bottlenecks)} 个瓶颈，{len(recommendations)} 条建议")
        
        return analysis
    
    def _analyze_system_resources(self) -> Dict[str, Any]:
        """分析系统资源使用"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # 内存使用
            memory = psutil.virtual_memory()
            
            # 磁盘使用
            disk = psutil.disk_usage('/')
            
            # 网络统计
            network = psutil.net_io_counters()
            
            return {
                "cpu": {
                    "usage_percent": cpu_percent,
                    "core_count": cpu_count,
                    "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "usage_percent": memory.percent
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "usage_percent": (disk.used / disk.total) * 100
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                }
            }
        except Exception as e:
            logger.error(f"系统资源分析失败: {e}")
            return {}
    
    async def _analyze_module_performance(self) -> Dict[str, Any]:
        """分析模块性能"""
        module_performance = {}
        
        try:
            # 代理管理器性能
            if hasattr(self.phantom_system, 'proxy_manager'):
                proxy_stats = self.phantom_system.proxy_manager.get_statistics()
                module_performance["proxy_manager"] = {
                    "available_proxies": proxy_stats.get("available_proxies", 0),
                    "success_rate": proxy_stats.get("success_rate", 0),
                    "average_response_time": proxy_stats.get("average_response_time", 0)
                }
            
            # 指纹引擎性能
            if hasattr(self.phantom_system, 'fingerprint_engine'):
                # 测试指纹生成性能
                start_time = time.time()
                for _ in range(10):
                    self.phantom_system.fingerprint_engine.generate_complete_fingerprint()
                fingerprint_time = (time.time() - start_time) / 10
                
                module_performance["fingerprint_engine"] = {
                    "average_generation_time": fingerprint_time,
                    "generations_per_second": 1 / fingerprint_time if fingerprint_time > 0 else 0
                }
            
            # 行为模拟器性能
            if hasattr(self.phantom_system, 'behavior_simulator'):
                start_time = time.time()
                page_context = {
                    "url": "https://example.com",
                    "title": "Example",
                    "viewport_size": (1920, 1080),
                    "page_height": 2000
                }
                for _ in range(5):
                    self.phantom_system.behavior_simulator.generate_page_interaction_sequence(page_context)
                behavior_time = (time.time() - start_time) / 5
                
                module_performance["behavior_simulator"] = {
                    "average_sequence_generation_time": behavior_time,
                    "sequences_per_second": 1 / behavior_time if behavior_time > 0 else 0
                }
            
            # 浏览器控制器性能
            if hasattr(self.phantom_system, 'browser_controller') and self.phantom_system.is_running:
                browser_stats = self.phantom_system.browser_controller.get_controller_statistics()
                module_performance["browser_controller"] = {
                    "active_sessions": browser_stats.get("active_sessions", 0),
                    "browser_pool": browser_stats.get("browser_pool", {}),
                    "utilization_rate": browser_stats.get("browser_pool", {}).get("utilization_rate", 0)
                }
            
            # 会话调度器性能
            if hasattr(self.phantom_system, 'session_scheduler') and self.phantom_system.is_running:
                scheduler_stats = self.phantom_system.session_scheduler.get_comprehensive_statistics()
                module_performance["session_scheduler"] = {
                    "tasks": scheduler_stats.get("tasks", {}),
                    "sessions": scheduler_stats.get("sessions", {}),
                    "success_rate": scheduler_stats.get("tasks", {}).get("success_rate", 0)
                }
            
        except Exception as e:
            logger.error(f"模块性能分析失败: {e}")
        
        return module_performance
    
    def _analyze_memory_usage(self) -> Dict[str, Any]:
        """分析内存使用情况"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            # 触发垃圾回收并测量
            gc.collect()
            memory_after_gc = process.memory_info()
            
            return {
                "rss": memory_info.rss,
                "vms": memory_info.vms,
                "rss_after_gc": memory_after_gc.rss,
                "vms_after_gc": memory_after_gc.vms,
                "gc_freed": memory_info.rss - memory_after_gc.rss,
                "memory_percent": process.memory_percent()
            }
        except Exception as e:
            logger.error(f"内存使用分析失败: {e}")
            return {}
    
    def _identify_bottlenecks(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """识别性能瓶颈"""
        bottlenecks = []
        
        # CPU瓶颈
        cpu_usage = analysis.get("system_resources", {}).get("cpu", {}).get("usage_percent", 0)
        if cpu_usage > 80:
            bottlenecks.append({
                "type": "cpu",
                "severity": "high" if cpu_usage > 90 else "medium",
                "description": f"CPU使用率过高: {cpu_usage:.1f}%",
                "metric": cpu_usage
            })
        
        # 内存瓶颈
        memory_usage = analysis.get("system_resources", {}).get("memory", {}).get("usage_percent", 0)
        if memory_usage > 80:
            bottlenecks.append({
                "type": "memory",
                "severity": "high" if memory_usage > 90 else "medium",
                "description": f"内存使用率过高: {memory_usage:.1f}%",
                "metric": memory_usage
            })
        
        # 模块性能瓶颈
        module_perf = analysis.get("module_performance", {})
        
        # 指纹生成性能
        fingerprint_perf = module_perf.get("fingerprint_engine", {})
        fingerprint_rate = fingerprint_perf.get("generations_per_second", 0)
        if fingerprint_rate < 50:
            bottlenecks.append({
                "type": "fingerprint_generation",
                "severity": "medium",
                "description": f"指纹生成速度较慢: {fingerprint_rate:.1f} 个/秒",
                "metric": fingerprint_rate
            })
        
        # 浏览器池利用率
        browser_perf = module_perf.get("browser_controller", {})
        utilization = browser_perf.get("utilization_rate", 0)
        if utilization > 90:
            bottlenecks.append({
                "type": "browser_pool",
                "severity": "high",
                "description": f"浏览器池利用率过高: {utilization:.1f}%",
                "metric": utilization
            })
        
        return bottlenecks
    
    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成优化建议"""
        recommendations = []
        bottlenecks = analysis.get("bottlenecks", [])
        
        for bottleneck in bottlenecks:
            if bottleneck["type"] == "cpu":
                recommendations.append({
                    "category": "system",
                    "priority": "high",
                    "title": "优化CPU使用",
                    "description": "考虑减少并发任务数量或升级CPU",
                    "actions": [
                        "减少浏览器池最大实例数",
                        "降低流量生成速率",
                        "启用任务队列限流"
                    ]
                })
            
            elif bottleneck["type"] == "memory":
                recommendations.append({
                    "category": "system",
                    "priority": "high",
                    "title": "优化内存使用",
                    "description": "减少内存占用或增加系统内存",
                    "actions": [
                        "启用更频繁的垃圾回收",
                        "减少浏览器实例缓存时间",
                        "优化指纹数据结构"
                    ]
                })
            
            elif bottleneck["type"] == "fingerprint_generation":
                recommendations.append({
                    "category": "module",
                    "priority": "medium",
                    "title": "优化指纹生成性能",
                    "description": "提高指纹生成效率",
                    "actions": [
                        "启用指纹缓存",
                        "减少指纹复杂度",
                        "使用预生成指纹池"
                    ]
                })
            
            elif bottleneck["type"] == "browser_pool":
                recommendations.append({
                    "category": "module",
                    "priority": "high",
                    "title": "扩展浏览器池",
                    "description": "增加浏览器实例数量",
                    "actions": [
                        "增加浏览器池最大实例数",
                        "优化浏览器实例复用",
                        "启用分布式浏览器节点"
                    ]
                })
        
        # 通用优化建议
        memory_info = analysis.get("memory_usage", {})
        gc_freed = memory_info.get("gc_freed", 0)
        if gc_freed > 50 * 1024 * 1024:  # 50MB
            recommendations.append({
                "category": "system",
                "priority": "medium",
                "title": "优化内存管理",
                "description": "垃圾回收释放了大量内存，考虑优化对象生命周期",
                "actions": [
                    "启用自动垃圾回收",
                    "优化对象缓存策略",
                    "减少长期持有的对象引用"
                ]
            })
        
        return recommendations
    
    async def apply_optimizations(self, optimizations: List[str]) -> Dict[str, Any]:
        """应用优化措施"""
        logger.info(f"应用 {len(optimizations)} 项优化措施...")
        
        results = {
            "applied": [],
            "failed": [],
            "performance_before": await self._measure_performance(),
            "performance_after": None
        }
        
        for optimization in optimizations:
            try:
                success = await self._apply_single_optimization(optimization)
                if success:
                    results["applied"].append(optimization)
                    logger.info(f"优化措施应用成功: {optimization}")
                else:
                    results["failed"].append(optimization)
                    logger.warning(f"优化措施应用失败: {optimization}")
            except Exception as e:
                results["failed"].append(optimization)
                logger.error(f"优化措施应用异常: {optimization} - {e}")
        
        # 等待一段时间让优化生效
        await asyncio.sleep(5)
        
        # 测量优化后的性能
        results["performance_after"] = await self._measure_performance()
        
        # 记录优化历史
        self.optimization_history.append({
            "timestamp": datetime.now().isoformat(),
            "optimizations": optimizations,
            "results": results
        })
        
        logger.info(f"优化完成: {len(results['applied'])} 成功, {len(results['failed'])} 失败")
        
        return results
    
    async def _apply_single_optimization(self, optimization: str) -> bool:
        """应用单个优化措施"""
        try:
            if optimization == "enable_gc":
                # 启用更频繁的垃圾回收
                gc.collect()
                return True
            
            elif optimization == "reduce_browser_pool":
                # 减少浏览器池大小
                if hasattr(self.phantom_system, 'browser_controller'):
                    # 这里应该有实际的配置更新逻辑
                    logger.info("浏览器池大小优化")
                    return True
            
            elif optimization == "enable_fingerprint_cache":
                # 启用指纹缓存
                if hasattr(self.phantom_system, 'fingerprint_engine'):
                    # 这里应该有实际的缓存启用逻辑
                    logger.info("指纹缓存已启用")
                    return True
            
            elif optimization == "optimize_traffic_rate":
                # 优化流量速率
                if hasattr(self.phantom_system, 'session_scheduler'):
                    # 这里应该有实际的流量速率调整逻辑
                    logger.info("流量速率已优化")
                    return True
            
            else:
                logger.warning(f"未知的优化措施: {optimization}")
                return False
                
        except Exception as e:
            logger.error(f"应用优化措施失败: {optimization} - {e}")
            return False
    
    async def _measure_performance(self) -> Dict[str, Any]:
        """测量性能指标"""
        try:
            # 测量指纹生成性能
            start_time = time.time()
            for _ in range(10):
                if hasattr(self.phantom_system, 'fingerprint_engine'):
                    self.phantom_system.fingerprint_engine.generate_complete_fingerprint()
            fingerprint_time = (time.time() - start_time) / 10
            
            # 测量内存使用
            process = psutil.Process()
            memory_usage = process.memory_percent()
            
            # 测量CPU使用
            cpu_usage = psutil.cpu_percent(interval=1)
            
            return {
                "fingerprint_generation_time": fingerprint_time,
                "memory_usage_percent": memory_usage,
                "cpu_usage_percent": cpu_usage,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"性能测量失败: {e}")
            return {}
    
    def get_optimization_history(self) -> List[Dict[str, Any]]:
        """获取优化历史"""
        return self.optimization_history
    
    async def auto_optimize(self) -> Dict[str, Any]:
        """自动优化"""
        logger.info("开始自动优化...")
        
        # 分析系统性能
        analysis = await self.analyze_system_performance()
        
        # 根据瓶颈生成优化措施
        optimizations = []
        for bottleneck in analysis.get("bottlenecks", []):
            if bottleneck["type"] == "memory":
                optimizations.append("enable_gc")
            elif bottleneck["type"] == "fingerprint_generation":
                optimizations.append("enable_fingerprint_cache")
            elif bottleneck["type"] == "browser_pool":
                optimizations.append("reduce_browser_pool")
        
        # 应用优化措施
        if optimizations:
            results = await self.apply_optimizations(optimizations)
        else:
            results = {"message": "未发现需要优化的问题"}
        
        logger.info("自动优化完成")
        
        return {
            "analysis": analysis,
            "optimization_results": results
        }
