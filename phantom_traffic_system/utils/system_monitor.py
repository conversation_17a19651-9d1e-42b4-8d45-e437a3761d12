"""
幻影流量系统 - 系统监控器
提供实时系统监控和诊断功能
"""

import asyncio
import time
import psutil
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from collections import deque

from ..core.logger import phantom_logger

logger = phantom_logger.get_logger("system_monitor")


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, phantom_system, config: Dict[str, Any] = None):
        """
        初始化系统监控器
        
        Args:
            phantom_system: 幻影流量系统实例
            config: 监控配置
        """
        self.phantom_system = phantom_system
        self.config = config or {}
        
        # 监控配置
        self.monitor_interval = self.config.get("monitor_interval", 30)  # 30秒
        self.history_size = self.config.get("history_size", 100)  # 保留100个数据点
        self.alert_thresholds = self.config.get("alert_thresholds", {
            "cpu_usage": 80,
            "memory_usage": 80,
            "error_rate": 10,
            "response_time": 5000
        })
        
        # 监控数据
        self.metrics_history = deque(maxlen=self.history_size)
        self.alerts = []
        self.alert_callbacks = []
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
        
        logger.debug("系统监控器初始化完成")
    
    async def start_monitoring(self):
        """启动监控"""
        if self.is_monitoring:
            logger.warning("监控已在运行")
            return
        
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        
        logger.info("系统监控启动")
    
    async def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("系统监控停止")
    
    async def _monitoring_loop(self):
        """监控循环"""
        logger.debug("监控循环启动")
        
        while self.is_monitoring:
            try:
                # 收集指标
                metrics = await self._collect_metrics()
                
                # 存储历史数据
                self.metrics_history.append(metrics)
                
                # 检查告警
                await self._check_alerts(metrics)
                
                # 等待下一次监控
                await asyncio.sleep(self.monitor_interval)
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(5)
        
        logger.debug("监控循环停止")
    
    async def _collect_metrics(self) -> Dict[str, Any]:
        """收集系统指标"""
        timestamp = datetime.now()
        
        metrics = {
            "timestamp": timestamp.isoformat(),
            "system": self._collect_system_metrics(),
            "application": await self._collect_application_metrics(),
            "performance": await self._collect_performance_metrics()
        }
        
        return metrics
    
    def _collect_system_metrics(self) -> Dict[str, Any]:
        """收集系统指标"""
        try:
            # CPU指标
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # 内存指标
            memory = psutil.virtual_memory()
            
            # 磁盘指标
            disk = psutil.disk_usage('/')
            
            # 网络指标
            network = psutil.net_io_counters()
            
            # 进程指标
            process = psutil.Process()
            process_memory = process.memory_info()
            
            return {
                "cpu": {
                    "usage_percent": cpu_percent,
                    "core_count": cpu_count,
                    "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "usage_percent": memory.percent,
                    "process_rss": process_memory.rss,
                    "process_vms": process_memory.vms,
                    "process_percent": process.memory_percent()
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "usage_percent": (disk.used / disk.total) * 100
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                }
            }
        except Exception as e:
            logger.error(f"系统指标收集失败: {e}")
            return {}
    
    async def _collect_application_metrics(self) -> Dict[str, Any]:
        """收集应用指标"""
        try:
            app_metrics = {
                "is_running": self.phantom_system.is_running,
                "uptime": 0
            }
            
            if self.phantom_system.start_time:
                uptime = (datetime.now() - self.phantom_system.start_time).total_seconds()
                app_metrics["uptime"] = uptime
            
            # 代理管理器指标
            if hasattr(self.phantom_system, 'proxy_manager'):
                proxy_stats = self.phantom_system.proxy_manager.get_statistics()
                app_metrics["proxy_manager"] = proxy_stats
            
            # 浏览器控制器指标
            if hasattr(self.phantom_system, 'browser_controller') and self.phantom_system.is_running:
                browser_stats = self.phantom_system.browser_controller.get_controller_statistics()
                app_metrics["browser_controller"] = browser_stats
            
            # 会话调度器指标
            if hasattr(self.phantom_system, 'session_scheduler') and self.phantom_system.is_running:
                scheduler_stats = self.phantom_system.session_scheduler.get_comprehensive_statistics()
                app_metrics["session_scheduler"] = scheduler_stats
            
            return app_metrics
            
        except Exception as e:
            logger.error(f"应用指标收集失败: {e}")
            return {}
    
    async def _collect_performance_metrics(self) -> Dict[str, Any]:
        """收集性能指标"""
        try:
            performance_metrics = {}
            
            # 测试指纹生成性能
            if hasattr(self.phantom_system, 'fingerprint_engine'):
                start_time = time.time()
                try:
                    self.phantom_system.fingerprint_engine.generate_complete_fingerprint()
                    fingerprint_time = (time.time() - start_time) * 1000  # 毫秒
                    performance_metrics["fingerprint_generation_ms"] = fingerprint_time
                except Exception as e:
                    performance_metrics["fingerprint_generation_error"] = str(e)
            
            # 测试行为序列生成性能
            if hasattr(self.phantom_system, 'behavior_simulator'):
                start_time = time.time()
                try:
                    page_context = {
                        "url": "https://example.com",
                        "title": "Example",
                        "viewport_size": (1920, 1080),
                        "page_height": 2000
                    }
                    self.phantom_system.behavior_simulator.generate_page_interaction_sequence(page_context)
                    behavior_time = (time.time() - start_time) * 1000  # 毫秒
                    performance_metrics["behavior_generation_ms"] = behavior_time
                except Exception as e:
                    performance_metrics["behavior_generation_error"] = str(e)
            
            return performance_metrics
            
        except Exception as e:
            logger.error(f"性能指标收集失败: {e}")
            return {}
    
    async def _check_alerts(self, metrics: Dict[str, Any]):
        """检查告警条件"""
        alerts = []
        
        # CPU使用率告警
        cpu_usage = metrics.get("system", {}).get("cpu", {}).get("usage_percent", 0)
        if cpu_usage > self.alert_thresholds.get("cpu_usage", 80):
            alerts.append({
                "type": "cpu_high",
                "severity": "warning" if cpu_usage < 90 else "critical",
                "message": f"CPU使用率过高: {cpu_usage:.1f}%",
                "value": cpu_usage,
                "threshold": self.alert_thresholds["cpu_usage"]
            })
        
        # 内存使用率告警
        memory_usage = metrics.get("system", {}).get("memory", {}).get("usage_percent", 0)
        if memory_usage > self.alert_thresholds.get("memory_usage", 80):
            alerts.append({
                "type": "memory_high",
                "severity": "warning" if memory_usage < 90 else "critical",
                "message": f"内存使用率过高: {memory_usage:.1f}%",
                "value": memory_usage,
                "threshold": self.alert_thresholds["memory_usage"]
            })
        
        # 应用错误率告警
        app_metrics = metrics.get("application", {})
        scheduler_stats = app_metrics.get("session_scheduler", {})
        tasks_stats = scheduler_stats.get("tasks", {})
        
        total_tasks = tasks_stats.get("total_completed", 0) + tasks_stats.get("total_failed", 0)
        if total_tasks > 0:
            error_rate = (tasks_stats.get("total_failed", 0) / total_tasks) * 100
            if error_rate > self.alert_thresholds.get("error_rate", 10):
                alerts.append({
                    "type": "error_rate_high",
                    "severity": "warning",
                    "message": f"任务错误率过高: {error_rate:.1f}%",
                    "value": error_rate,
                    "threshold": self.alert_thresholds["error_rate"]
                })
        
        # 性能响应时间告警
        performance = metrics.get("performance", {})
        fingerprint_time = performance.get("fingerprint_generation_ms", 0)
        if fingerprint_time > self.alert_thresholds.get("response_time", 5000):
            alerts.append({
                "type": "response_time_high",
                "severity": "warning",
                "message": f"指纹生成响应时间过长: {fingerprint_time:.1f}ms",
                "value": fingerprint_time,
                "threshold": self.alert_thresholds["response_time"]
            })
        
        # 处理告警
        for alert in alerts:
            await self._handle_alert(alert)
    
    async def _handle_alert(self, alert: Dict[str, Any]):
        """处理告警"""
        alert["timestamp"] = datetime.now().isoformat()
        alert["id"] = f"{alert['type']}_{int(time.time())}"
        
        # 添加到告警列表
        self.alerts.append(alert)
        
        # 限制告警历史大小
        if len(self.alerts) > 1000:
            self.alerts = self.alerts[-500:]
        
        # 记录日志
        severity = alert["severity"]
        message = alert["message"]
        
        if severity == "critical":
            logger.critical(f"告警: {message}")
        elif severity == "warning":
            logger.warning(f"告警: {message}")
        else:
            logger.info(f"告警: {message}")
        
        # 调用告警回调
        for callback in self.alert_callbacks:
            try:
                await callback(alert)
            except Exception as e:
                logger.error(f"告警回调执行失败: {e}")
    
    def add_alert_callback(self, callback: Callable):
        """添加告警回调"""
        self.alert_callbacks.append(callback)
    
    def remove_alert_callback(self, callback: Callable):
        """移除告警回调"""
        if callback in self.alert_callbacks:
            self.alert_callbacks.remove(callback)
    
    def get_current_metrics(self) -> Optional[Dict[str, Any]]:
        """获取当前指标"""
        if self.metrics_history:
            return self.metrics_history[-1]
        return None
    
    def get_metrics_history(self, limit: int = None) -> List[Dict[str, Any]]:
        """获取指标历史"""
        if limit:
            return list(self.metrics_history)[-limit:]
        return list(self.metrics_history)
    
    def get_alerts(self, severity: str = None, limit: int = None) -> List[Dict[str, Any]]:
        """获取告警列表"""
        alerts = self.alerts
        
        if severity:
            alerts = [alert for alert in alerts if alert["severity"] == severity]
        
        if limit:
            alerts = alerts[-limit:]
        
        return alerts
    
    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        current_metrics = self.get_current_metrics()
        if not current_metrics:
            return {"status": "unknown", "message": "无监控数据"}
        
        # 检查各项指标
        issues = []
        
        # CPU检查
        cpu_usage = current_metrics.get("system", {}).get("cpu", {}).get("usage_percent", 0)
        if cpu_usage > 90:
            issues.append(f"CPU使用率过高: {cpu_usage:.1f}%")
        
        # 内存检查
        memory_usage = current_metrics.get("system", {}).get("memory", {}).get("usage_percent", 0)
        if memory_usage > 90:
            issues.append(f"内存使用率过高: {memory_usage:.1f}%")
        
        # 应用状态检查
        app_running = current_metrics.get("application", {}).get("is_running", False)
        if not app_running:
            issues.append("应用未运行")
        
        # 确定健康状态
        if not issues:
            status = "healthy"
            message = "系统运行正常"
        elif len(issues) <= 2:
            status = "warning"
            message = f"发现 {len(issues)} 个问题: {'; '.join(issues)}"
        else:
            status = "critical"
            message = f"发现 {len(issues)} 个严重问题: {'; '.join(issues)}"
        
        return {
            "status": status,
            "message": message,
            "issues": issues,
            "metrics": current_metrics,
            "last_update": current_metrics.get("timestamp")
        }
    
    async def generate_report(self, hours: int = 24) -> Dict[str, Any]:
        """生成监控报告"""
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        # 过滤时间范围内的数据
        filtered_metrics = []
        for metric in self.metrics_history:
            metric_time = datetime.fromisoformat(metric["timestamp"])
            if start_time <= metric_time <= end_time:
                filtered_metrics.append(metric)
        
        if not filtered_metrics:
            return {"error": "指定时间范围内无监控数据"}
        
        # 计算统计信息
        cpu_values = [m.get("system", {}).get("cpu", {}).get("usage_percent", 0) for m in filtered_metrics]
        memory_values = [m.get("system", {}).get("memory", {}).get("usage_percent", 0) for m in filtered_metrics]
        
        # 过滤时间范围内的告警
        filtered_alerts = []
        for alert in self.alerts:
            alert_time = datetime.fromisoformat(alert["timestamp"])
            if start_time <= alert_time <= end_time:
                filtered_alerts.append(alert)
        
        report = {
            "period": {
                "start": start_time.isoformat(),
                "end": end_time.isoformat(),
                "hours": hours
            },
            "summary": {
                "data_points": len(filtered_metrics),
                "alerts_count": len(filtered_alerts),
                "critical_alerts": len([a for a in filtered_alerts if a["severity"] == "critical"]),
                "warning_alerts": len([a for a in filtered_alerts if a["severity"] == "warning"])
            },
            "performance": {
                "cpu": {
                    "average": sum(cpu_values) / len(cpu_values) if cpu_values else 0,
                    "max": max(cpu_values) if cpu_values else 0,
                    "min": min(cpu_values) if cpu_values else 0
                },
                "memory": {
                    "average": sum(memory_values) / len(memory_values) if memory_values else 0,
                    "max": max(memory_values) if memory_values else 0,
                    "min": min(memory_values) if memory_values else 0
                }
            },
            "alerts": filtered_alerts,
            "recommendations": self._generate_report_recommendations(filtered_metrics, filtered_alerts)
        }
        
        return report
    
    def _generate_report_recommendations(self, metrics: List[Dict], alerts: List[Dict]) -> List[str]:
        """生成报告建议"""
        recommendations = []
        
        # 基于告警生成建议
        alert_types = set(alert["type"] for alert in alerts)
        
        if "cpu_high" in alert_types:
            recommendations.append("考虑优化CPU密集型操作或增加CPU资源")
        
        if "memory_high" in alert_types:
            recommendations.append("检查内存泄漏或考虑增加内存资源")
        
        if "error_rate_high" in alert_types:
            recommendations.append("检查应用错误日志，优化错误处理逻辑")
        
        if "response_time_high" in alert_types:
            recommendations.append("优化性能瓶颈，考虑启用缓存或并行处理")
        
        # 基于指标趋势生成建议
        if metrics:
            avg_cpu = sum(m.get("system", {}).get("cpu", {}).get("usage_percent", 0) for m in metrics) / len(metrics)
            if avg_cpu > 70:
                recommendations.append("平均CPU使用率较高，建议进行性能优化")
        
        return recommendations
