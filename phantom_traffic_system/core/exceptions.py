"""
幻影流量系统 - 异常处理模块
定义系统中所有自定义异常类型
"""

class PhantomTrafficError(Exception):
    """幻影流量系统基础异常类"""
    pass

class PhantomException(PhantomTrafficError):
    """幻影系统基础异常类（向后兼容）"""
    pass

class ProxyException(PhantomException):
    """代理相关异常"""
    pass

class ProxyConnectionError(ProxyException):
    """代理连接失败"""
    pass

class ProxyAuthenticationError(ProxyException):
    """代理认证失败"""
    pass

class ProxyHealthCheckError(ProxyException):
    """代理健康检查失败"""
    pass

class FingerprintException(PhantomException):
    """指纹生成相关异常"""
    pass

class DevicePersonaError(FingerprintException):
    """设备人格生成错误"""
    pass

class CanvasNoiseError(FingerprintException):
    """Canvas指纹加噪错误"""
    pass

class BehaviorException(PhantomException):
    """行为模拟相关异常"""
    pass

class MouseMovementError(BehaviorException):
    """鼠标移动模拟错误"""
    pass

class ScrollingError(BehaviorException):
    """页面滚动模拟错误"""
    pass

class SessionException(PhantomException):
    """会话管理相关异常"""
    pass

class SessionTimeoutError(SessionException):
    """会话超时错误"""
    pass

class WarmupError(SessionException):
    """会话预热错误"""
    pass

class BrowserException(PhantomException):
    """浏览器相关异常"""
    pass

class BrowserLaunchError(BrowserException):
    """浏览器启动失败"""
    pass

class PageLoadError(BrowserException):
    """页面加载失败"""
    pass

class ConfigurationException(PhantomException):
    """配置相关异常"""
    pass

class InvalidConfigError(ConfigurationException):
    """无效配置错误"""
    pass

class MissingConfigError(ConfigurationException):
    """缺失配置错误"""
    pass

class InvalidConfigError(ConfigurationException):
    """无效配置错误"""
    pass

# 别名定义（向后兼容）
ConfigurationError = ConfigurationException
BrowserError = BrowserException
ProxyError = ProxyException
