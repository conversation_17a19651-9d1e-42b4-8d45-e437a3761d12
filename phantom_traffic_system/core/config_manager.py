"""
幻影流量系统 - 配置管理器
负责加载、验证和管理系统配置
"""

import os
import yaml
import json
from typing import Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv

from .exceptions import ConfigurationError, MissingConfigError, InvalidConfigError
from .logger import phantom_logger

logger = phantom_logger.get_logger("config_manager")


class ConfigManager:
    """配置管理器 - 系统配置的中央控制器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认为 config/phantom_config.yaml
        """
        # 默认配置文件路径
        if config_path is None:
            # 尝试找到默认配置文件
            possible_paths = [
                "phantom_traffic_system/config/default_config.yaml",
                "config/default_config.yaml",
                "default_config.yaml"
            ]

            config_path = None
            for path in possible_paths:
                if Path(path).exists():
                    config_path = path
                    break

        self.config_path = config_path
        self.config: Dict[str, Any] = {}
        self._load_environment_variables()
        self._load_config()
        self._validate_config()
    
    def _load_environment_variables(self):
        """加载环境变量"""
        # 加载 .env 文件
        env_path = Path(".env")
        if env_path.exists():
            load_dotenv(env_path)
    
    def _load_config(self):
        """加载配置文件"""
        if self.config_path and Path(self.config_path).exists():
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f) or {}

                # 替换环境变量占位符
                self._substitute_environment_variables(self.config)

            except yaml.YAMLError as e:
                raise ConfigurationError(f"配置文件格式错误: {e}")
            except Exception as e:
                raise ConfigurationError(f"加载配置文件失败: {e}")
        else:
            # 使用默认配置
            self.config = self._get_default_config()
    
    def _substitute_environment_variables(self, obj):
        """递归替换配置中的环境变量占位符"""
        if isinstance(obj, dict):
            for key, value in obj.items():
                obj[key] = self._substitute_environment_variables(value)
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                obj[i] = self._substitute_environment_variables(item)
        elif isinstance(obj, str) and obj.startswith("${") and obj.endswith("}"):
            # 提取环境变量名
            env_var = obj[2:-1]
            return os.getenv(env_var, obj)  # 如果环境变量不存在，返回原值
        
        return obj

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "proxy_manager": {
                "enabled": True,
                "sources": [],
                "validation": {"enabled": False}
            },
            "fingerprint_engine": {
                "enabled": True,
                "device_fingerprints": {"enabled": True},
                "browser_fingerprints": {"enabled": True}
            },
            "behavior_simulator": {
                "enabled": True,
                "mouse_behavior": {"enabled": True},
                "timing_behavior": {"page_load_wait": [1, 3]}
            },
            "session_scheduler": {
                "enabled": True,
                "task_queue": {"max_queue_size": 100},
                "traffic_control": {"enabled": True}
            },
            "browser_controller": {
                "enabled": True,
                "browser_pool": {"max_instances": 5, "min_instances": 1},
                "browser_config": {"browser_type": "chromium"},
                "anti_detection": {"enabled": True}
            },
            "api_server": {
                "enabled": False,
                "host": "127.0.0.1",
                "port": 8080
            },
            "logging": {
                "level": "INFO",
                "file_enabled": False
            }
        }

    def _validate_config(self):
        """验证配置的完整性和有效性"""
        required_sections = [
            "proxy_manager", "fingerprint_engine",
            "behavior_simulator", "session_scheduler", "browser_controller"
        ]

        for section in required_sections:
            if section not in self.config:
                raise InvalidConfigError(f"缺少必需的配置节: {section}")
        
        # 验证代理配置
        self._validate_proxy_config()
        
        # 验证浏览器配置
        self._validate_browser_config()
    
    def _validate_proxy_config(self):
        """验证代理配置"""
        proxy_config = self.config.get("proxy_manager", {})

        # 检查是否启用代理管理器
        if not proxy_config.get("enabled", True):
            return  # 如果禁用代理管理器，跳过验证

        # 检查代理源配置
        sources = proxy_config.get("sources", [])
        providers = proxy_config.get("providers", {})

        # 如果既没有sources也没有providers，只发出警告而不抛出异常
        if not sources and not providers:
            logger.warning("未配置代理供应商，系统将在测试模式下运行")
            return

        # 验证providers配置
        for provider_name, provider_config in providers.items():
            if provider_config.get("enabled", False):
                if not provider_config.get("api_endpoints"):
                    raise InvalidConfigError(f"代理供应商 {provider_name} 缺少 API 端点配置")
    
    def _validate_browser_config(self):
        """验证浏览器配置"""
        browser_config = self.config.get("browser_controller", {})

        # 检查是否启用浏览器控制器
        if not browser_config.get("enabled", True):
            return  # 如果禁用浏览器控制器，跳过验证

        # 检查浏览器类型配置
        browser_type = browser_config.get("browser_config", {}).get("browser_type", "chromium")

        if browser_type not in ["chromium", "firefox", "webkit", "chrome"]:
            logger.warning(f"不支持的浏览器类型: {browser_type}，将使用默认的chromium")
            # 不抛出异常，只记录警告

    def get_config(self) -> Dict[str, Any]:
        """
        获取完整配置

        Returns:
            完整的配置字典
        """
        return self.config

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的嵌套键
        
        Args:
            key: 配置键，如 "proxy.providers.mobile_4g_5g.enabled"
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def get_proxy_config(self) -> Dict[str, Any]:
        """获取代理配置"""
        return self.config.get("proxy", {})
    
    def get_fingerprint_config(self) -> Dict[str, Any]:
        """获取指纹配置"""
        return self.config.get("fingerprint", {})
    
    def get_behavior_config(self) -> Dict[str, Any]:
        """获取行为模拟配置"""
        return self.config.get("behavior", {})
    
    def get_session_config(self) -> Dict[str, Any]:
        """获取会话配置"""
        return self.config.get("session", {})
    
    def get_browser_config(self) -> Dict[str, Any]:
        """获取浏览器配置"""
        return self.config.get("browser", {})
    
    def get_storage_config(self) -> Dict[str, Any]:
        """获取存储配置"""
        return self.config.get("storage", {})
    
    def save_config(self, path: Optional[str] = None):
        """
        保存配置到文件
        
        Args:
            path: 保存路径，默认为原配置文件路径
        """
        save_path = path or self.config_path
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
        except Exception as e:
            raise ConfigurationError(f"保存配置文件失败: {e}")
    
    def validate_config(self, config: Dict[str, Any] = None) -> bool:
        """
        验证配置

        Args:
            config: 要验证的配置，如果为None则验证当前配置

        Returns:
            验证是否通过
        """
        if config is None:
            config = self.config

        # 基本验证
        if not isinstance(config, dict):
            return False

        # 检查必要的配置节
        required_sections = [
            "proxy_manager",
            "fingerprint_engine",
            "behavior_simulator",
            "session_scheduler",
            "browser_controller"
        ]

        for section in required_sections:
            if section not in config:
                return False

        return True

    def reload_config(self):
        """重新加载配置"""
        self._load_config()
        self._validate_config()
    
    def __str__(self) -> str:
        """返回配置的字符串表示"""
        return json.dumps(self.config, indent=2, ensure_ascii=False)
