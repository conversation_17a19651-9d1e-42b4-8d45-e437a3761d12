"""
幻影流量系统 - 核心模块
Project: Phantom Traffic System

这是一个高度复杂的流量模拟系统，用于创造让广告联盟反作弊系统
都信以为真的"完美流量"。

核心理念：我们不创造流量，我们只"扮演"用户。
每一个请求都应该是一个独立的、有背景、有故事的"数字人格"。

作者: Phantom Team
版本: 1.0.0
"""

from .phantom_system import PhantomTrafficSystem
from .config_manager import ConfigManager
from .logger import phantom_logger
from .exceptions import PhantomTrafficError, ConfigurationError, BrowserError, ProxyError

__version__ = "1.0.0"

__all__ = [
    "PhantomTrafficSystem",
    "ConfigManager",
    "phantom_logger",
    "PhantomTrafficError",
    "ConfigurationError",
    "BrowserError",
    "ProxyError"
]
__author__ = "Phantom Team"

__all__ = [
    "PhantomEngine",
    "ConfigManager", 
    "PhantomLogger",
    "PhantomException"
]
