"""
幻影流量系统 - 日志管理器
提供统一的日志记录功能，支持多种输出格式和级别
"""

import sys
import logging
import logging.handlers
from pathlib import Path
from typing import Optional


class PhantomLogger:
    """幻影系统日志管理器"""

    def __init__(self,
                 log_level: str = "INFO",
                 log_file: Optional[str] = None,
                 max_size: int = 100 * 1024 * 1024,  # 100MB
                 backup_count: int = 5):
        """
        初始化日志管理器

        Args:
            log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            log_file: 日志文件路径
            max_size: 单个日志文件最大大小（字节）
            backup_count: 保留的日志文件数量
        """
        self.log_level = getattr(logging, log_level.upper())
        self.log_file = log_file
        self.max_size = max_size
        self.backup_count = backup_count

        # 创建根logger
        self.logger = logging.getLogger("phantom")
        self.logger.setLevel(self.log_level)

        # 清除现有的handlers
        self.logger.handlers.clear()

        # 配置控制台输出
        self._setup_console_logging()

        # 配置文件输出
        if self.log_file:
            self._setup_file_logging()
    
    def _setup_console_logging(self):
        """设置控制台日志输出"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)

        console_format = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s:%(funcName)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(console_format)

        self.logger.addHandler(console_handler)

    def _setup_file_logging(self):
        """设置文件日志输出"""
        # 确保日志目录存在
        log_path = Path(self.log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        # 使用RotatingFileHandler
        file_handler = logging.handlers.RotatingFileHandler(
            self.log_file,
            maxBytes=self.max_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(self.log_level)

        file_format = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s:%(funcName)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S.%f'
        )
        file_handler.setFormatter(file_format)

        self.logger.addHandler(file_handler)
    
    def get_logger(self, name: str = "phantom"):
        """
        获取指定名称的logger实例

        Args:
            name: logger名称

        Returns:
            logger实例
        """
        return logging.getLogger(name)

    def set_level(self, level: str):
        """设置日志级别"""
        self.logger.setLevel(getattr(logging, level.upper()))
        for handler in self.logger.handlers:
            handler.setLevel(getattr(logging, level.upper()))

    def debug(self, message: str, **kwargs):
        """记录DEBUG级别日志"""
        self.logger.debug(message, **kwargs)

    def info(self, message: str, **kwargs):
        """记录INFO级别日志"""
        self.logger.info(message, **kwargs)

    def warning(self, message: str, **kwargs):
        """记录WARNING级别日志"""
        self.logger.warning(message, **kwargs)

    def error(self, message: str, **kwargs):
        """记录ERROR级别日志"""
        self.logger.error(message, **kwargs)

    def critical(self, message: str, **kwargs):
        """记录CRITICAL级别日志"""
        self.logger.critical(message, **kwargs)

    def exception(self, message: str, **kwargs):
        """记录异常信息"""
        self.logger.exception(message, **kwargs)
    
    def log_session_start(self, session_id: str, target_url: str, proxy_ip: str):
        """记录会话开始"""
        self.info(
            f"🚀 会话开始 | ID: {session_id} | 目标: {target_url} | 代理: {proxy_ip}"
        )
    
    def log_session_end(self, session_id: str, success: bool, duration: float):
        """记录会话结束"""
        status = "✅ 成功" if success else "❌ 失败"
        self.info(
            f"🏁 会话结束 | ID: {session_id} | 状态: {status} | 耗时: {duration:.2f}s"
        )
    
    def log_proxy_switch(self, old_ip: str, new_ip: str, reason: str):
        """记录代理切换"""
        self.info(
            f"🔄 代理切换 | {old_ip} -> {new_ip} | 原因: {reason}"
        )
    
    def log_fingerprint_generated(self, session_id: str, device_type: str, user_agent: str):
        """记录指纹生成"""
        self.debug(
            f"🎭 指纹生成 | 会话: {session_id} | 设备: {device_type} | UA: {user_agent[:50]}..."
        )
    
    def log_behavior_action(self, session_id: str, action: str, details: str):
        """记录行为模拟动作"""
        self.debug(
            f"🎬 行为模拟 | 会话: {session_id} | 动作: {action} | 详情: {details}"
        )
    
    def log_warmup_step(self, session_id: str, step: str, url: str, duration: float):
        """记录预热步骤"""
        self.debug(
            f"🔥 预热步骤 | 会话: {session_id} | 步骤: {step} | URL: {url} | 耗时: {duration:.2f}s"
        )
    
    def log_error_with_context(self, error: Exception, context: dict):
        """记录带上下文的错误"""
        context_str = " | ".join([f"{k}: {v}" for k, v in context.items()])
        self.error(f"❌ 错误发生 | {str(error)} | 上下文: {context_str}")
    
    def log_performance_metrics(self, metrics: dict):
        """记录性能指标"""
        metrics_str = " | ".join([f"{k}: {v}" for k, v in metrics.items()])
        self.info(f"📊 性能指标 | {metrics_str}")
    
    def log_traffic_stats(self, stats: dict):
        """记录流量统计"""
        self.info(
            f"📈 流量统计 | "
            f"总请求: {stats.get('total_requests', 0)} | "
            f"成功: {stats.get('successful', 0)} | "
            f"失败: {stats.get('failed', 0)} | "
            f"成功率: {stats.get('success_rate', 0):.2f}%"
        )

    @classmethod
    def setup_from_config(cls, config: dict) -> 'PhantomLogger':
        """
        从配置字典创建日志管理器

        Args:
            config: 日志配置字典

        Returns:
            PhantomLogger实例
        """
        log_config = config.get("logging", {})

        # 解析文件大小
        max_size_str = log_config.get("file_max_size", "100MB")
        if isinstance(max_size_str, str):
            if max_size_str.endswith("MB"):
                max_size = int(max_size_str[:-2]) * 1024 * 1024
            elif max_size_str.endswith("KB"):
                max_size = int(max_size_str[:-2]) * 1024
            else:
                max_size = int(max_size_str)
        else:
            max_size = max_size_str

        return cls(
            log_level=log_config.get("level", "INFO"),
            log_file=log_config.get("file_path"),
            max_size=max_size,
            backup_count=log_config.get("file_backup_count", 5)
        )


# 创建全局logger实例
phantom_logger = PhantomLogger()

# 导出常用的日志函数
debug = phantom_logger.debug
info = phantom_logger.info
warning = phantom_logger.warning
error = phantom_logger.error
critical = phantom_logger.critical
exception = phantom_logger.exception
