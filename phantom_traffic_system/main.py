"""
幻影流量系统 - 主入口文件
提供命令行接口和系统启动功能
"""

import asyncio
import argparse
import signal
import sys
from pathlib import Path

from .core import PhantomTrafficSystem, phantom_logger
from .api import APIServer

logger = phantom_logger.get_logger("main")


class PhantomTrafficApp:
    """幻影流量应用主类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化应用
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.phantom_system = None
        self.api_server = None
        self.shutdown_event = asyncio.Event()
        
    async def start(self, enable_api: bool = True):
        """
        启动应用
        
        Args:
            enable_api: 是否启用API服务器
        """
        try:
            logger.info("正在启动幻影流量系统...")
            
            # 初始化幻影流量系统
            self.phantom_system = PhantomTrafficSystem(self.config_path)
            
            # 启动核心系统
            await self.phantom_system.start()
            
            # 启动API服务器（如果启用）
            if enable_api:
                api_config = self.phantom_system.config.get("api_server", {})
                self.api_server = APIServer(self.phantom_system, api_config)
                await self.api_server.start()
            
            logger.info("幻影流量系统启动完成")
            
            # 设置信号处理
            self._setup_signal_handlers()
            
            # 等待关闭信号
            await self.shutdown_event.wait()
            
        except Exception as e:
            logger.error(f"启动失败: {e}")
            raise
        finally:
            await self.stop()
    
    async def stop(self):
        """停止应用"""
        try:
            logger.info("正在停止幻影流量系统...")
            
            # 停止API服务器
            if self.api_server:
                await self.api_server.stop()
            
            # 停止核心系统
            if self.phantom_system:
                await self.phantom_system.stop()
            
            logger.info("幻影流量系统停止完成")
            
        except Exception as e:
            logger.error(f"停止失败: {e}")
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，正在关闭系统...")
            self.shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def execute_single_visit(self, target_url: str, config: dict = None):
        """
        执行单次访问
        
        Args:
            target_url: 目标URL
            config: 访问配置
        """
        if not self.phantom_system:
            self.phantom_system = PhantomTrafficSystem(self.config_path)
            await self.phantom_system.start()
        
        try:
            result = await self.phantom_system.execute_single_visit(target_url, config)
            return result
        finally:
            await self.phantom_system.stop()


def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="幻影流量系统 - 高级流量模拟工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 启动完整系统（包含API服务器）
  python -m phantom_traffic_system

  # 使用自定义配置文件启动
  python -m phantom_traffic_system --config config.yaml

  # 只启动核心系统，不启动API服务器
  python -m phantom_traffic_system --no-api

  # 执行单次访问
  python -m phantom_traffic_system visit https://example.com

  # 显示系统状态
  python -m phantom_traffic_system status
        """
    )
    
    parser.add_argument(
        "--config", "-c",
        type=str,
        help="配置文件路径"
    )
    
    parser.add_argument(
        "--no-api",
        action="store_true",
        help="不启动API服务器"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别"
    )
    
    # 子命令
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # visit命令
    visit_parser = subparsers.add_parser("visit", help="执行单次访问")
    visit_parser.add_argument("url", help="目标URL")
    visit_parser.add_argument("--proxy", help="指定代理")
    visit_parser.add_argument("--headless", action="store_true", help="无头模式")
    
    # status命令
    status_parser = subparsers.add_parser("status", help="显示系统状态")
    
    # config命令
    config_parser = subparsers.add_parser("config", help="配置管理")
    config_subparsers = config_parser.add_subparsers(dest="config_action")
    config_subparsers.add_parser("show", help="显示当前配置")
    config_subparsers.add_parser("validate", help="验证配置文件")
    
    return parser


async def handle_visit_command(args):
    """处理visit命令"""
    app = PhantomTrafficApp(args.config)
    
    visit_config = {}
    if args.proxy:
        visit_config["proxy"] = args.proxy
    if args.headless:
        visit_config["headless"] = True
    
    result = await app.execute_single_visit(args.url, visit_config)
    
    if result["success"]:
        print(f"✅ 访问成功: {args.url}")
        print(f"   最终URL: {result['final_url']}")
        print(f"   页面标题: {result['page_title']}")
        print(f"   加载时间: {result['load_time']:.2f}s")
        print(f"   总耗时: {result['total_duration']:.2f}s")
        print(f"   行为动作: {result['behavior_actions']}")
    else:
        print(f"❌ 访问失败: {args.url}")
        print(f"   错误: {result['error_message']}")
        sys.exit(1)


async def handle_status_command(args):
    """处理status命令"""
    app = PhantomTrafficApp(args.config)
    
    try:
        # 这里应该连接到运行中的系统获取状态
        # 暂时显示配置信息
        phantom_system = PhantomTrafficSystem(args.config)
        config = phantom_system.config_manager.get_config()
        
        print("📊 幻影流量系统状态")
        print("=" * 50)
        print(f"配置文件: {args.config or '默认配置'}")
        print(f"代理管理器: {'启用' if config.get('proxy_manager', {}).get('enabled', True) else '禁用'}")
        print(f"指纹引擎: {'启用' if config.get('fingerprint_engine', {}).get('enabled', True) else '禁用'}")
        print(f"行为模拟器: {'启用' if config.get('behavior_simulator', {}).get('enabled', True) else '禁用'}")
        print(f"API服务器: {'启用' if not args.no_api else '禁用'}")
        
    except Exception as e:
        print(f"❌ 获取状态失败: {e}")
        sys.exit(1)


def handle_config_command(args):
    """处理config命令"""
    if args.config_action == "show":
        try:
            phantom_system = PhantomTrafficSystem(args.config)
            config = phantom_system.config_manager.get_config()
            
            print("📋 当前配置")
            print("=" * 50)
            import json
            print(json.dumps(config, indent=2, ensure_ascii=False))
            
        except Exception as e:
            print(f"❌ 显示配置失败: {e}")
            sys.exit(1)
    
    elif args.config_action == "validate":
        try:
            phantom_system = PhantomTrafficSystem(args.config)
            print("✅ 配置文件验证通过")
            
        except Exception as e:
            print(f"❌ 配置文件验证失败: {e}")
            sys.exit(1)


async def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    # 设置日志级别
    phantom_logger.set_level(args.log_level)
    
    try:
        if args.command == "visit":
            await handle_visit_command(args)
        elif args.command == "status":
            await handle_status_command(args)
        elif args.command == "config":
            handle_config_command(args)
        else:
            # 默认启动完整系统
            app = PhantomTrafficApp(args.config)
            await app.start(enable_api=not args.no_api)
    
    except KeyboardInterrupt:
        logger.info("用户中断")
    except Exception as e:
        logger.error(f"运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    
    # 运行主函数
    asyncio.run(main())
