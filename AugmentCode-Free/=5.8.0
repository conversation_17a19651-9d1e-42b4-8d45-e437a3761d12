Defaulting to user installation because normal site-packages is not writeable
Collecting PyQt6
  Using cached pyqt6-6.9.1-cp39-abi3-macosx_10_14_universal2.whl.metadata (2.1 kB)
Requirement already satisfied: click in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (8.1.8)
Collecting colorama
  Downloading colorama-0.4.6-py2.py3-none-any.whl.metadata (17 kB)
Requirement already satisfied: psutil in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (7.0.0)
Collecting PyQt6-sip<14,>=13.8 (from PyQt6)
  Using cached pyqt6_sip-13.10.2-cp39-cp39-macosx_10_9_universal2.whl.metadata (494 bytes)
Collecting PyQt6-Qt6<6.10.0,>=6.9.0 (from PyQt6)
  Using cached pyqt6_qt6-6.9.1-py3-none-macosx_11_0_arm64.whl.metadata (534 bytes)
Downloading pyqt6-6.9.1-cp39-abi3-macosx_10_14_universal2.whl (59.8 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 59.8/59.8 MB 57.4 kB/s eta 0:00:00
Downloading pyqt6_qt6-6.9.1-py3-none-macosx_11_0_arm64.whl (60.6 MB)
   ━━━━━━━━━━━━━━━                         23.6/60.6 MB 113.2 kB/s eta 0:05:27
