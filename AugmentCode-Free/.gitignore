# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib60/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a script go in a specific directory.
#  See https://github.com/pyinstaller/pyinstaller/wiki/FAQ#gnulinux-i-cant-run-my-program-executables-produced-by-pyinstaller
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Version number files (pip output accidentally saved as files)
[0-9]*.[0-9]*.[0-9]*
[0-9]*.[0-9]*.[0-9]*'

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# PEP 582; __pypackages__ directory
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static analyzer
.pytype/

# Cython debug symbols
cython_debug/

# ===== Project Specific =====

# AI Memory and Privacy Files
.cunzhi-memory/
.augment/
*.memory
*.session
*.cache

# Build and Distribution
build.log
portable_temp/
*.exe
*.dmg
*.AppImage
*.deb
*.rpm

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Configuration and Secrets
config/user_config.json
config/secrets.json
*.key
*.pem
*.p12
*.pfx
api_keys.txt
secrets.txt

# User Data and Logs
user_data/
logs/
*.log
crash_reports/
debug_*.txt

# Backup Files
*.bak
*.backup
*.old
*_backup
*_old

# Temporary Files
temp/
tmp/
*.tmp
*.temp

# OS Generated Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Development and Testing
test_output/
test_results/
coverage_reports/
.pytest_cache/
.coverage
htmlcov/

# Personal Notes and Documentation
NOTES.md
TODO.md
PERSONAL_*
PRIVATE_*
*_PRIVATE.*
*_PERSONAL.*